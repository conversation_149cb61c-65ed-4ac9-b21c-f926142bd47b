import { useStream } from '@shared/stores/DataCacheStream';
import { IntegrationsStore } from '@shared/stores/IntegrationsStore';
import { ErrorTooltip } from '@web/components/Tooltip/ErrorTooltip';
import { ProcessingTooltip } from '@web/components/Tooltip/ProcessingTooltip';

export const InstallationsBadge = ({ teamId }: { teamId: string }) => {
    const installationsState = useStream(() => IntegrationsStore.get(teamId).installations, [teamId], {
        $case: 'loading',
    });

    if (installationsState.$case === 'loading') {
        return null;
    }

    const installationsAggregate = installationsState.value;

    if (installationsAggregate.error) {
        return (
            <ErrorTooltip modal placement="top">
                {installationsAggregate.error.message}
            </ErrorTooltip>
        );
    }

    if (installationsAggregate.isProcessing && installationsAggregate.progress) {
        return (
            <ProcessingTooltip animate modal placement="top">
                {installationsAggregate.progress.summaryForAskQuestion}
            </ProcessingTooltip>
        );
    }

    return null;
};
