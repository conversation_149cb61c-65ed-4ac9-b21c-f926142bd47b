@use 'flex' as *;
@use 'fonts' as *;
@use 'layout' as *;
@use 'theme' as *;

.ci_settings_verify_host {
    @include flex-column-center;
    padding: $spacer-60 $spacer-24;

    &__content {
        @include flex-column-center;
        max-width: 460px;
        width: 100%;
        box-sizing: border-box;
        text-align: center;
        h1 {
            margin-bottom: $spacer-4;
        }
        p {
            color: themed($text-secondary);
            margin-bottom: $spacer-20;
        }

        .text_input,
        .button {
            width: 100%;
            box-sizing: border-box;
        }
        .text_input {
            margin-bottom: $spacer-16;
        }
    }

    &__error {
        color: themed($danger);
        margin-top: $spacer-8;
    }
}
