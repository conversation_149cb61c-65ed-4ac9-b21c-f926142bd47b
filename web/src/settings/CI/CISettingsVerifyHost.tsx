import { useCallback, useState } from 'react';

import { CIServerVerifyResponse } from '@shared/api/generatedApi';

import { CIServerVerifyError } from '@shared/stores/CIServerStore';
import { Button } from '@shared/webComponents/Button/Button';
import { TextInput } from '@shared/webComponents/TextInput/TextInput';
import { CIProvider, CIProviderTraitsUtil } from '@shared/webUtils/CIProviderTraits/CIProviderTraitsUtil';
import { CIWizardStep } from '@shared/webUtils/CIProviderTraits/CIProviderWizardSteps';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';

import { CIWizardContainer } from './CIWizardContainer';

import './CISettingsVerifyHost.scss';

interface Props {
    provider: CIProvider;
    steps: CIWizardStep[];
    onVerifyHost: (host: string) => Promise<CIServerVerifyResponse>;
    error?: CIServerVerifyError;
}

export const CISettingsVerifyHost = ({ provider, steps, onVerifyHost, error }: Props) => {
    const traits = ProviderTraitsUtil.get(provider);
    const ciTraits = CIProviderTraitsUtil.get(provider);
    const [host, setHost] = useState('');
    const onSubmit = useCallback(
        async (host: string) => {
            if (!host) {
                return;
            }

            await onVerifyHost(host);
        },
        [onVerifyHost]
    );
    return (
        <CIWizardContainer
            provider={provider}
            description="Configure Unblocked to analyze your CI failures."
            steps={steps}
            currentStep={0}
        >
            <div className="ci_settings_verify_host">
                <div className="ci_settings_verify_host__content">
                    <h1>Verify your hostname</h1>
                    <p>
                        Provide the hostname and port of your {traits.displayName} server.
                        <br />
                        For step-by-step instructions,&nbsp;
                        <a href={ciTraits.docsUrl} target="_blank" rel="noopener noreferrer">
                            view our docs
                        </a>
                        .
                    </p>
                    <TextInput value={host} onValueChange={(e) => setHost(e)} onEnter={() => onSubmit(host)} />
                    <Button disabled={!host} onClick={() => onSubmit(host)}>
                        Verify Host
                    </Button>
                    {error ? <div className="ci_settings_verify_host__error">{error.errorMessage}</div> : null}
                </div>
            </div>
        </CIWizardContainer>
    );
};
