import { useCallback, useMemo } from 'react';
import { Navigate } from 'react-router-dom';

import { Provider } from '@shared/api/generatedApi';

import { CIServerStore } from '@shared/stores/CIServerStore';
import { useStream } from '@shared/stores/DataCacheStream';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { DashboardUrls } from '@shared/webUtils';
import { CIProviderTraitsUtil } from '@shared/webUtils/CIProviderTraits/CIProviderTraitsUtil';
import { useTeamContext } from '@web/components/Team/TeamContext';
import { useDocumentTitle } from '@web/hooks/useDocumentTitle';

import { CISettingsVerifyHost } from '../CISettingsVerifyHost';
import { JenkinsCreateHost } from './JenkinsCreateHost';

const provider = Provider.Jenkins;

export const ConnectJenkins = () => {
    useDocumentTitle(() => 'Connect Jenkins', []);
    const { currentTeamId } = useTeamContext();
    const store = useMemo(() => new CIServerStore(currentTeamId, provider), [currentTeamId]);
    const state = useStream(() => store.stream, [store], { $case: 'loading' });

    const onVerifyHost = useCallback(
        async (host: string) => {
            return await store.verifyHost(host);
        },
        [store]
    );

    const onCreateHost = useCallback(
        async (username: string, token: string, serverUrl: string) => {
            return await store.createServer(username, token, serverUrl);
        },
        [store]
    );

    switch (state.$case) {
        case 'loading':
            return <Loading centerAlign />;
        case 'redirect':
        case 'connected':
            return (
                <Navigate
                    to={DashboardUrls.providerInstallation(currentTeamId, provider, state.installationId, {
                        initialConnection: state.$case === 'connected',
                    })}
                />
            );
        case 'unverified':
        case 'errorVerifying':
            return (
                <CISettingsVerifyHost
                    provider={provider}
                    steps={CIProviderTraitsUtil.get(provider).wizardSteps}
                    onVerifyHost={onVerifyHost}
                    error={state.$case === 'errorVerifying' ? state.error : undefined}
                />
            );
        case 'verified':
        case 'errorCreating':
            return (
                <JenkinsCreateHost
                    provider={provider}
                    steps={CIProviderTraitsUtil.get(Provider.Jenkins).wizardSteps}
                    onCreateHost={onCreateHost}
                    serverUrl={state.serverUrl}
                    webhookToken={'FAKE TOKEN'}
                />
            );
    }
};
