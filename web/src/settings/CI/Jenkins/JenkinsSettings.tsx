import { InstallationError, Provider } from '@shared/api/generatedApi';

import { CISettings } from '../CISettings';

interface Props {
    installationId: string;
    initialConnection?: boolean;
    isSetupIncomplete?: boolean;
    installationError?: InstallationError;
}

export const JenkinsSettings = ({ installationId, isSetupIncomplete, installationError }: Props) => {
    return (
        <CISettings
            provider={Provider.Jenkins}
            installationId={installationId}
            isSetupIncomplete={isSetupIncomplete}
            installationError={installationError}
        />
    );
};
