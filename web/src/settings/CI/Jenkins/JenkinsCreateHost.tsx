import { useCallback, useState } from 'react';

import { CIServer } from '@shared/api/generatedApi';

import { OrderedStep, OrderedStepsList } from '@shared/webComponents/OrderedStepsList/OrderedStepsList';
import { CopyInput } from '@shared/webComponents/TextInput/CopyInput';
import { TextInput } from '@shared/webComponents/TextInput/TextInput';
import { HoverTooltip } from '@shared/webComponents/Tooltip/HoverTooltip';
import { CIProvider } from '@shared/webUtils/CIProviderTraits/CIProviderTraitsUtil';
import { CIWizardStep } from '@shared/webUtils/CIProviderTraits/CIProviderWizardSteps';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { SettingsSection } from '@web/settings/SettingsSection';

import { CIWizardContainer } from '../CIWizardContainer';

import './JenkinsCreateHost.scss';

interface Props {
    provider: CIProvider;
    steps: CIWizardStep[];
    onCreateHost: (username: string, token: string, serverUrl: string) => Promise<CIServer>;
    serverUrl: string;
    webhookToken: string;
    error?: string;
}

interface JenkinUrls {
    pluginPageUrl: string;

    patCreationUrl?: string;
    // TODO:
    unblockedSignatureFieldUrl?: string;
}

const generateJenkinUrls = (serverUrl: string, username?: string): JenkinUrls => {
    return {
        pluginPageUrl: `${serverUrl}/manage/configure#unblocked-configuration`,
        patCreationUrl: username ? `${serverUrl}/user/${username}/security/` : undefined,
    };
};

export const JenkinsCreateHost = ({ provider, steps, onCreateHost, serverUrl, webhookToken }: Props) => {
    const providerTraits = ProviderTraitsUtil.get(provider);
    const [username, setUsername] = useState('');
    const [token, setToken] = useState('');
    const onSubmit = useCallback(
        async (username: string, token: string) => {
            if (!username || !token) {
                return;
            }

            await onCreateHost(username, token, serverUrl);
        },
        [onCreateHost, serverUrl]
    );

    const jenkinUrls = generateJenkinUrls(serverUrl, username);
    const onSave = useCallback(async () => {
        await onSubmit(username, token);
    }, [onSubmit, username, token]);

    return (
        <CIWizardContainer
            provider={provider}
            description={serverUrl}
            steps={steps}
            currentStep={1}
            onSave={onSave}
            disableSave={!username || !token}
        >
            <OrderedStepsList className="jenkins_create_host">
                <OrderedStep index={1} title={`Install the Unblocked ${providerTraits.shortenedDisplayName} Plugin`}>
                    <ul>
                        <li>
                            Install the &ldquo;Unblocked&rdquo; plugin, or follow the installation instructions from
                            the&nbsp;
                            <a href={jenkinUrls.pluginPageUrl} target="_blank" rel="noreferrer">
                                Unblocked Plugin page
                            </a>
                            .
                        </li>
                    </ul>
                </OrderedStep>
                <OrderedStep index={2} title={`Allow access to your ${providerTraits.shortenedDisplayName} Server`}>
                    <p>
                        Enter the username and password of a {providerTraits.shortenedDisplayName} user that has access
                        to this server.
                    </p>
                    <SettingsSection title={`Username:`} fontSize="small">
                        <TextInput value={username} onChange={(e) => setUsername(e.target.value)} />
                    </SettingsSection>
                    <SettingsSection
                        title={`Personal Access Token:`}
                        fontSize="small"
                        description={
                            <>
                                {/* TODO: Add link to PAT*/}
                                {jenkinUrls.patCreationUrl ? (
                                    <a href={jenkinUrls.patCreationUrl} target="_blank" rel="noreferrer">
                                        Create a Personal Access Token
                                    </a>
                                ) : (
                                    <HoverTooltip
                                        header={({ ref }) => <a ref={ref}>Create a Personal Access Token</a>}
                                        placement="top"
                                    >
                                        Enter a username
                                    </HoverTooltip>
                                )}{' '}
                                &nbsp;for the user entered above and paste it below.
                            </>
                        }
                    >
                        <TextInput
                            disabled={!username}
                            placeholder={username ? 'Paste your Personal Access Token' : 'Enter a username'}
                            value={token}
                            onChange={(e) => setToken(e.target.value)}
                        />
                    </SettingsSection>
                </OrderedStep>
                <OrderedStep index={3} title={`Copy and Paste the Webhook Token`}>
                    <p>
                        {/* TODO: Add link to Unblocked Signature field */}
                        Copy the generated token below and paste it in the{' '}
                        <a href={jenkinUrls.unblockedSignatureFieldUrl} target="_blank" rel="noreferrer">
                            Unblocked Signature field
                        </a>
                    </p>
                    <CopyInput fullWidth value={webhookToken} />
                </OrderedStep>
            </OrderedStepsList>
        </CIWizardContainer>
    );
};
