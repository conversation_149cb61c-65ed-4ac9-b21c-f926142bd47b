import classNames from 'classnames';

import { useWindowDimensions } from '@web/hooks/useWindowDimensions';

import { useAppLayoutState } from './AppLayoutContext';

import './PinnedContentHeader.scss';
import './LayoutContext.scss';

const MED_BREAKPOINT = 768;
const SM_NAV_HEIGHT = 59;

interface Props {
    children: React.ReactNode;
    className?: string;
}

export const PinnedContentHeader = ({ children, className }: Props) => {
    const { headerHeight } = useAppLayoutState();
    const { width: windowWidth } = useWindowDimensions();
    const top = headerHeight ?? 0;

    return (
        <div
            className={classNames('pinned_content_header', className)}
            style={{ top: windowWidth >= MED_BREAKPOINT ? top : top + SM_NAV_HEIGHT }}
        >
            {children}
        </div>
    );
};
