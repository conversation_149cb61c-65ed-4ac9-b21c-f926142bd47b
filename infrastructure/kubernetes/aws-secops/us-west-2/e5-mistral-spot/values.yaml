global:
  unblocked:
    secrets:
      existingSecret: "vault-e5-mistral-spot-secrets"

fullnameOverride: "e5-mistral-spot"

replicaCount: 0

# Container images configuration
image:
  tgi:
    repository: "877923746456.dkr.ecr.us-west-2.amazonaws.com/onprem-mle5-mistral"
    tag: "1-d0cd9155dcf7737120216e6b93d764ca2eba595c"
    pullPolicy: IfNotPresent

# Environment variables for e5-mistral
env:
  - name: NVIDIA_DRIVER_CAPABILITIES
    value: "compute,utility"
  - name: DISABLE_CUSTOM_KERNELS
    value: "true"
  - name: NVIDIA_REQUIRE_CUDA
    value: "cuda>=8.0"
  - name: HF_HUB_CACHE
    value: "/hf-snapshots"
  - name: HUGGINGFACE_HUB_CACHE
    value: "/hf-snapshots"

# Resource configuration for e5-mistral (1 GPU)
resources:
  nginx:
    requests:
      cpu: "300m"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "1Gi"
  tgi:
    requests:
      cpu: "3"
      memory: "12Gi"
      gpu: "1"
    limits:
      cpu: "4"
      memory: "14Gi"
      gpu: "1"
    shm:
      size: 512Mi

deploymentStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 30%
    maxSurge: 60%


# Health check probes for e5-mistral
startupProbe:
  httpGet:
    path: /ping
    port: 8080
  failureThreshold: 30
  periodSeconds: 10
  initialDelaySeconds: 120

readinessProbe:
  httpGet:
    path: /ping
    port: 8080
    scheme: HTTP
  initialDelaySeconds: 120
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 60
  successThreshold: 1

# Ingress configuration for e5-mistral
ingress:
  enabled: true
  annotations:
    alb.ingress.kubernetes.io/group.order: "100"
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: instance
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/group.name: "ml-internal"
    alb.ingress.kubernetes.io/healthcheck-path: "/healthz"
    external-dns.alpha.kubernetes.io/hostname: spot.ml.alb.secops-2.us-west-2.secops.getunblocked.com
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:877923746456:certificate/1d06b78a-2144-461b-86e5-94b0a021b0bb
  paths:
    - host: spot.ml.alb.secops-2.us-west-2.secops.getunblocked.com
      path: /api/ml/embeddings/e5-mistral
      pathType: Prefix
    - host: spot.ml.alb.secops.getunblocked.com
      path: /api/ml/embeddings/e5-mistral
      pathType: Prefix

# Node scheduling
tolerations:
  - key: "nvidia.com/gpu"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

# We are using built-in labels which are set by Nvidia GPU operator on EKS
nodeSelector:
  "node.kubernetes.io/instance-type": g6e.xlarge
  "spot_instance": "true"

# Persistent volume configuration
pvc:
  enabled: true
  accessModes:
    - ReadWriteMany
  storage: 100Gi
  storageClassName: efs-sc

keda:
  enabled: false
  pollingInterval: 30  # seconds
  cooldownPeriod: 1800  # seconds
  minReplicaCount: 1
  maxReplicaCount: 12
  triggers:
    cpu:
      enabled: false
      metricType: "Utilization"
      value: "70"
    cloudwatch:
      - enabled: true
        identityOwner: "operator"
        awsRegion: "us-west-2"
        # Uses the same metric as the master deployment. HPA will take the total and divide it by the total number of pods
        expression: SELECT SUM(RequestCount) FROM SCHEMA("Custom/ALB", ServiceName) WHERE ServiceName = 'default/e5-mistral-spot-e5-mistral-spot:80'
        metricStat: "Sum"
        metricStatPeriod: "60"
        targetMetricValue: "160"
        metricCollectionTime: "60"
        metricEndTimeOffset: "60"
        minMetricValue: "0"
