# Development values file for temporal-umbrella chart
# This configuration uses AWS Secrets Manager via External Secrets Operator
# with automatic schema and namespace setup for a complete ready-to-use deployment

# Creates the OIDC Kube secret
WebAuthSecrets:
  create: false
  TEMPORAL_AUTH_CLIENT_ID: ""     # Set by helm CLI
  TEMPORAL_AUTH_CLIENT_SECRET: ""  # Set by helm <PERSON>LI

# External Secret configuration for AWS Secrets Manager
externalSecret:
  enabled: true
  awsSecret:
    # Your Aurora Serverless secret name in AWS Secrets Manager
    name: "AuroraServerlessStacktempor-PqTAZ6YxonZv"
    # Note: AWS region should be configured in your ClusterSecretStore resource
  # Automatic schema setup - creates Temporal database schema automatically
  schemaSetup:
    enabled: true

# Temporal subchart configuration
temporal:
  # Override image versions to use available images
  web:
    image:
      repository: temporalio/ui
      tag: "2.39.0"
    additionalEnv:
      - name: TEMPORAL_AUTH_ENABLED
        value: "true"
      - name: TEMPORAL_AUTH_CLIENT_ID
        valueFrom:
          secretKeyRef:
            name: vault-temporal-webauth-secret
            key: client_id
      - name: TEMPORAL_AUTH_CLIENT_SECRET
        valueFrom:
          secretKeyRef:
            name: vault-temporal-webauth-secret
            key: client_secret
      - name: TEMPORAL_AUTH_PROVIDER_URL
        value: "https://nextchaptersoftware.okta.com/oauth2/default"
      - name: TEMPORAL_AUTH_CALLBACK_URL
        value: "https://admin.prod.getunblocked.com:8443/auth/sso/callback"
      - name: TEMPORAL_AUTH_SCOPES
        value: "openid profile email"
  admintools:
    image:
      repository: temporalio/admin-tools
      tag: "1.24.2-tctl-1.18.1-cli-0.13.0"

  # Disable worker deployment
  worker:
    enabled: false

  # Disable temporal chart's built-in schema jobs - we use our own
  schema:
    setup:
      enabled: false
    update:
      enabled: false

  server:
    worker:
      resources:
        requests:
          cpu: 1
          memory: 2560Mi
      autoscaling:
        enabled: true
        minReplicas: 2
        maxReplicas: 6
        targetCPUUtilizationPercentage: 80
        targetMemoryUtilizationPercentage: 80
    dynamicConfig:
      frontend.enableUpdateWorkflowExecution:
        - value: true
      system.advancedVisibilityWritingMode:
        - value: "on"
    config:
      persistence:
        default:
          driver: sql
          sql:
            driver: postgres12
            # Aurora Serverless configuration
            host: auroraserverlessstack-temporalauroraserverless493d-7s8hdcpcv7rw.cluster-czdnrjl0hm44.us-west-2.rds.amazonaws.com
            port: 5432
            database: temporal
            user: postgres
            # The password will be read from the External Secret
            existingSecret: temporal-postgres-secret
            maxConns: 20
            maxConnLifetime: "1h"
        visibility:
          driver: sql
          sql:
            driver: postgres12
            # Aurora Serverless configuration
            host: auroraserverlessstack-temporalauroraserverless493d-7s8hdcpcv7rw.cluster-czdnrjl0hm44.us-west-2.rds.amazonaws.com
            port: 5432
            database: temporal_visibility
            user: postgres
            # The password will be read from the External Secret
            existingSecret: temporal-postgres-secret
            maxConns: 20
            maxConnLifetime: "1h"

# Ingress configuration for AWS Load Balancer Controller
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 8443}]'
    alb.ingress.kubernetes.io/group.order: '30'
    alb.ingress.kubernetes.io/group.name: "adminweb-internal"
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:029574882031:certificate/4ef864e9-7137-4ac2-b542-8302ecb5c43f
  hosts:
    - host: adminweb.prod-1.us-west-2.prod.getunblocked.com
      paths:
        - path: /
          pathType: Prefix
    - host: admin.prod.getunblocked.com
      paths:
        - path: /
          pathType: Prefix
  # Uncomment and configure TLS if needed
  # tls:
  #   - secretName: temporal-tls
  #     hosts:
  #       - temporal.internal.example.com

# Installation:
# helm upgrade --install temporal charts/temporal-umbrella --namespace temporal --create-namespace -f values-dev.yaml
#
# This will:
# 1. Create External Secret to fetch database credentials from AWS Secrets Manager
# 2. Run schema setup job to create Temporal database schema automatically
# 3. Start Temporal services with correct database configuration
# 4. Create the default namespace automatically
# 5. Provide a fully ready Temporal deployment
