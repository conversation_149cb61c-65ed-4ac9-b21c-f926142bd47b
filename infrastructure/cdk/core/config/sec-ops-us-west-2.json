{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-west-2", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "sec-ops", "version": "0.0.0", "build": "0"}, "coreVPC": {"networkCIDR": "***********/16", "maxAZs": 3, "numNATGateways": 1, "peerWithEksVpc": false, "allowICMPinDefaultSG": true, "enableDynamodbEndpoint": false, "transitGatewayId": "tgw-08ce9ea2424be06e2", "transitGatewayTargetCIDRs": ["**********/16", "********/16"]}, "dns": {"route53HostedZoneID": "Z0459776ENS6834PCNMR", "route53HostedZoneName": "secops.getunblocked.com"}, "certRequests": [{"name": "ml.alb.secops.getunblocked.com", "domainName": "ml.alb.secops.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "spot.ml.alb.secops.getunblocked.com", "domainName": "spot.ml.alb.secops.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}], "eks": {"eksClusters": [{"enabled": true, "name": "secops-2", "version": "V1_32", "kubeProxyAddOnVersion": "v1.32.0-eksbuild.2", "enableAlbController": true, "createDefaultDenyPolicy": true, "hostedZoneId": "Z1002878S415F38ZYZAN", "enable_addon_falco": false, "efs_addon_fs_id": "fs-09de4fabe9685ac3c", "enable_addon_grafana": true, "enable_addon_refinery": false, "enable_addon_priority_class": true, "enable_addon_cert_manager_csi": true, "enable_addon_gpu_operator": true, "enable_addon_kms_issuer": true, "enable_addon_external_rbac": true, "enable_addon_prefect_server": false, "enable_addon_prefect_workers": false, "enable_addon_prefect_external_rbac": false, "managedNodeGroup": [{"id": "secops-2-group1-c5a-2xlarge", "minSize": 1, "maxSize": 12, "maxUnavailablePercentage": 30, "instanceType": "c5a.2xlarge"}, {"id": "secops-2-group1-c5-2xlarge", "minSize": 5, "maxSize": 14, "maxUnavailablePercentage": 30, "instanceType": "c5.2xlarge"}, {"id": "unblocked-gpu-g6e-xlarge", "minSize": 6, "maxSize": 20, "instanceType": "g6e.xlarge", "maxUnavailablePercentage": 30, "diskSize": 100, "amiType": "AL2023_X86_64_NVIDIA", "taints": [{"key": "nvidia.com/gpu", "value": "true", "effect": "NO_SCHEDULE"}]}, {"id": "unblocked-gpu-g6e-xlarge-spot-1", "minSize": 0, "maxSize": 10, "instanceType": "g6e.xlarge", "maxUnavailablePercentage": 30, "spotInstance": true, "diskSize": 100, "amiType": "AL2023_X86_64_NVIDIA", "taints": [{"key": "nvidia.com/gpu", "value": "true", "effect": "NO_SCHEDULE"}], "labels": {"spot_instance": "true"}}, {"id": "unblocked-gpu-g6-xlarge", "minSize": 0, "maxSize": 5, "instanceType": "g6.xlarge", "amiType": "AL2023_X86_64_NVIDIA", "maxUnavailablePercentage": 30, "diskSize": 100, "taints": [{"key": "nvidia.com/gpu", "value": "true", "effect": "NO_SCHEDULE"}]}]}], "serviceAccounts": [], "useCoreVPC": true}, "efsFileSystems": [{"name": "HFModelSnapshotsFileSystem", "fileSystemName": "HFModelSnapshotsFileSystem", "outOfInfrequentAccessPolicy": "AFTER_1_ACCESS", "lifecyclePolicy": "AFTER_60_DAYS", "removalPolicy": "DESTROY", "throughputMode": "elastic", "securityGroup": {"name": "HFModelSnapshotsFileSystemSG", "description": "Security group for HFModelSnapshotsFileSystem", "allowAllOutbound": true, "ingressRules": [{"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "***********/16"}, "portRule": {"port": 2049, "portType": "tcp"}}]}}], "ecr": {"ecrRepos": [{"name": "adminwebservice", "maxImageCount": 100}, {"name": "apiservice", "maxImageCount": 100}, {"name": "assetservice", "maxImageCount": 100}, {"name": "authservice", "maxImageCount": 100}, {"name": "billingservice", "maxImageCount": 100}, {"name": "ciservice", "maxImageCount": 100}, {"name": "dashboard", "maxImageCount": 100}, {"name": "dataservice", "maxImageCount": 100}, {"name": "embeddingservice", "maxImageCount": 100}, {"name": "indexservice", "maxImageCount": 100}, {"name": "ingest-asana", "maxImageCount": 100}, {"name": "ingest-coda", "maxImageCount": 100}, {"name": "ingest-confluence", "maxImageCount": 100}, {"name": "ingest-google", "maxImageCount": 100}, {"name": "ingest-jira", "maxImageCount": 100}, {"name": "ingest-linear", "maxImageCount": 100}, {"name": "ingest-microsoft-teams", "maxImageCount": 100}, {"name": "ingest-notion", "maxImageCount": 100}, {"name": "ingest-slack", "maxImageCount": 100}, {"name": "ingest-stackoverflow", "maxImageCount": 100}, {"name": "ingest-web", "maxImageCount": 100}, {"name": "jiraservice", "maxImageCount": 100}, {"name": "maintenanceservice", "maxImageCount": 100}, {"name": "mcpser<PERSON>", "maxImageCount": 100}, {"name": "mermaidservice", "maxImageCount": 100}, {"name": "mlrouterservice", "maxImageCount": 100}, {"name": "msteamsservice", "maxImageCount": 100}, {"name": "notificationservice", "maxImageCount": 100}, {"name": "onprem-huggingface", "maxImageCount": 500, "mutableTags": true}, {"name": "onprem-ml-eval", "maxImageCount": 500, "mutableTags": true}, {"name": "onprem-mle5-mistral", "maxImageCount": 500, "mutableTags": true}, {"name": "onprem-ml-docmap", "maxImageCount": 500, "mutableTags": true}, {"name": "prefect", "maxImageCount": 100, "mutableTags": true}, {"name": "prefect-codeingestion", "maxImageCount": 100, "mutableTags": true}, {"name": "proxy-provider", "maxImageCount": 100}, {"name": "pusherservice", "maxImageCount": 100}, {"name": "publicapiservice", "maxImageCount": 100}, {"name": "queueservice", "maxImageCount": 100}, {"name": "review", "maxImageCount": 100}, {"name": "scmservice", "maxImageCount": 100}, {"name": "searchservice", "maxImageCount": 100}, {"name": "slackservice", "maxImageCount": 100}, {"name": "sourcecodeservice", "maxImageCount": 100}, {"name": "summarizationservice", "maxImageCount": 100}, {"name": "telemetryservice", "maxImageCount": 100}, {"name": "topicservice", "maxImageCount": 100}, {"name": "universal", "maxImageCount": 100}, {"name": "webhookservice", "maxImageCount": 100}], "ecrReplicationTargets": [{"region": "us-west-2", "registryId": "************"}, {"region": "us-west-2", "registryId": "************"}]}, "iam": {"createCloudWatchReadOnlyRole": true, "createCrossAccountAdminReadOnlyRole": true, "createEc2DeploybotRole": true, "peeringAccepterRoleTargetAccounts": ["************", "************"]}, "bastionHost": {"allowedCIDRs": ["0.0.0.0/0"], "sshKeyName": "bastionHost"}, "gradleBuildCacheNode": {"allowedCIDRs": ["0.0.0.0/0"], "sshKeyName": "gradleBuildCacheNode", "blockDeviceVolumeSizeGB": 80}, "s3Buckets": [{"name": "unblocked-source-code-backups", "addAccessLogPolicy": true, "autoDeleteObjects": false, "enableVersioning": true, "enableBackupRetentionPolicyRule": true, "createRootAccountIAMRole": true}, {"name": "unblocked-gh-actions-s3-cache", "addAccessLogPolicy": false, "autoDeleteObjects": false, "enableVersioning": false, "enableBackupRetentionPolicyRule": true, "createRootAccountIAMRole": true}, {"name": "unblocked-public-video-assets", "addAccessLogPolicy": false, "autoDeleteObjects": false, "enableVersioning": true, "enableBackupRetentionPolicyRule": false, "createRootAccountIAMRole": true, "transferAcceleration": true, "publicReadAccess": true, "blockPublicAccess": false}], "transitGateway": {"transitGatewayAttachments": [{"name": "devEksVpcTgwAttachment", "attachmentId": "tgw-attach-0302ad23cdcd86d0a"}, {"name": "devCoreVpcTgwAttachment", "attachmentId": "tgw-attach-02eb8032f2774a070"}, {"name": "prodEksVpcTgwAttachment", "attachmentId": "tgw-attach-0e946c47eb2ee9fe7"}, {"name": "prodCoreVpcTgwAttachment", "attachmentId": "tgw-attach-0f37d0307728127fb"}]}}