{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-east-2", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>", "coldSite": true}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "prod", "version": "0.0.0", "build": "0"}, "eksVPC": {"networkCIDR": "*********/16", "maxAZs": 3, "numNATGateways": 3, "allowICMPinDefaultSG": true, "peeringTarget": []}, "eks": {"eksClusters": [{"enabled": false, "name": "prod-1", "version": "V1_31", "kubeProxyAddOnVersion": "v1.31.2-eksbuild.3", "enableAlbController": true, "createDefaultDenyPolicy": true, "hostedZoneId": "Z09612693JMQR2HD5CJXS", "enable_addon_falco": false, "enable_addon_grafana": false, "enable_addon_refinery": true, "enable_addon_priority_class": true, "enable_addon_cert_manager_csi": true, "enable_addon_kms_issuer": true, "enable_addon_external_rbac": true, "enable_addon_prefect_server": true, "enable_addon_prefect_workers": true, "enable_addon_prefect_external_rbac": true, "managedNodeGroup": [{"id": "prod-1-group2-c5a-xlarge", "minSize": 3, "maxSize": 12, "maxUnavailablePercentage": 30, "instanceType": "c5a.2xlarge", "diskSize": 250}], "additionalAllowedCIDRs": ["*********/8"]}], "serviceAccounts": [{"name": "adminwebservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "dynamodb_read_sensitiveLogCache", "lambda_invoke_functions", "s3_adminwebservice_read", "s3_jiraservice_read", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe"]}, {"name": "apiservice", "customPolicies": ["base_service_permissions", "dynamodb_read_rawIngestionData"]}, {"name": "assetservice", "customPolicies": ["base_service_permissions", "s3_assetservice_read_write"]}, {"name": "authservice", "customPolicies": ["base_service_permissions"]}, {"name": "billingservice", "customPolicies": ["base_service_permissions", "s3_billingservice_read_write"]}, {"name": "ciservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData"]}, {"name": "dataservice", "customPolicies": ["base_service_permissions", "s3_dataservice_read_write"]}, {"name": "embeddingservice", "customPolicies": ["rds_read_write", "activemq_secret_read", "redis_secret_read", "dynamodb_read_write_rawIngestionData"]}, {"name": "encryptionservice", "customPolicies": ["rds_read_write"]}, {"name": "indexservice", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-asana", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-coda", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_codaservice_read_write"]}, {"name": "ingest-confluence", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-google", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-jira", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_jiraservice_read_write"]}, {"name": "ingest-linear", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-microsoft-teams", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-notion", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-slack", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-stackoverflow", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-web", "customPolicies": ["base_service_permissions"]}, {"name": "jiraservice", "customPolicies": ["base_service_permissions"]}, {"name": "maintenanceservice", "customPolicies": ["base_service_permissions"]}, {"name": "mermaidservice", "customPolicies": ["redis_secret_read"]}, {"name": "mlrouterservice", "customPolicies": ["base_service_permissions", "dynamodb_read_write_mlRouterWebhookEventStore"]}, {"name": "msteamsservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_msteamsservice_read"]}, {"name": "sourcecodeservice", "customPolicies": ["base_service_permissions", "s3_sourcecodeservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "notificationservice", "customPolicies": ["base_service_permissions"]}, {"name": "prefect-job", "namespace": "prefect", "createNamespace": true, "customPolicies": ["s3_prefect_job_read_write"]}, {"name": "proxy-provider", "customPolicies": ["base_service_permissions", "proxy_provider_aws_iam_sts_assume_external", "s3_proxy_provider_read"]}, {"name": "publicapiservice", "customPolicies": ["base_service_permissions"]}, {"name": "pusherservice", "customPolicies": ["base_service_permissions"]}, {"name": "queueservice", "customPolicies": ["activemq_secret_read"]}, {"name": "scmservice", "customPolicies": ["base_service_permissions", "s3_scmservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "searchservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_searchservice_read"]}, {"name": "secretservice", "customPolicies": ["rds_read_write", "kms_secretservice_decrypt_encrypt_genkey"]}, {"name": "slackservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_slackservice_read"]}, {"name": "sourcecodeservice", "customPolicies": ["base_service_permissions", "s3_sourcecodeservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "summarizationservice", "customPolicies": ["base_service_permissions"]}, {"name": "telemetryservice", "customPolicies": []}, {"name": "topicservice", "customPolicies": ["base_service_permissions", "s3_topicservice_read_write", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe"]}, {"name": "webhookservice", "customPolicies": ["activemq_secret_read", "redis_secret_read"]}]}, "coreVPC": {"networkCIDR": "*********/16", "maxAZs": 3, "numNATGateways": 0, "allowICMPinDefaultSG": true, "peeringTarget": []}, "dns": {"route53HostedZoneID": "Z06222132KYCTR0G03TIK", "route53HostedZoneName": "prod.getunblocked.com", "aliasRecords": [], "cnameRecords": []}, "certRequests": [{"name": "alb.prod.getunblocked.com", "domainName": "alb.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "admin.prod.getunblocked.com", "domainName": "admin.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "ml.api.prod.getunblocked.com", "domainName": "ml.api.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}], "rdsPostgres": {"databases": [{"cdkResourceName": "main-app", "dbUsername": "postgres", "isPrimary": true, "allowMajorVersionUpgrade": true, "autoMinorVersionUpgrade": true, "enhancedMonitoringInterval": 60, "enablePerformanceInsights": true, "performanceInsightRetention": 31, "auroraPostgresFullVersion": "15.2", "auroraPostgresMajorVersion": "15", "ioOptimized": true, "writerInstanceClass": "r6g", "writerInstanceSize": "2xlarge", "readerInstanceClass": "r6g", "readerInstanceSize": "large", "readerInstanceCount": 1, "transactionTimeoutSeconds": 60, "statementTimeoutSeconds": 180, "workMemMB": 16, "snapshotIdentifier": "rds-backup-dr-11-03-2024", "disableS3Backup": true, "UseExistingCreateS3CrossRegionBackupBucket": true}], "endpointCNAMEPrefix": "56a79300-ee9a-46a3-8307-70a8dd920900", "additionalAllowedCIDRs": ["*********/16", "*********/16", "***********/16"]}, "dynamodb": {"replicaRegions": [], "deletionProtection": true, "tables": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partitionKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}]}, "redis": {"cacheNodeType": "cache.t4g.small", "clusterName": "primary-prod-redis", "autoMinorVersionUpgrade": true, "automaticFailoverEnabled": true, "clusterEnabled": true, "numNodeGroups": 1, "replicasPerNodeGroup": 1, "multiAzEnabled": true, "users": [{"userName": "default", "accessString": "off", "isDefaultUser": true, "secrets": [{"secretName": "redis-default-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-default-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}, {"userName": "unblocked", "accessString": "on ~* +@all", "secrets": [{"secretName": "redis-unblocked-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-unblocked-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}]}, "activeMQ": {"hostInstanceType": "mq.m5.xlarge", "brokerName": "primary-prod-activemq", "autoMinorVersionUpgrade": true, "users": [{"userName": "unblocked", "consoleAccess": true, "secret": {"secretName": "activemq-unblocked-password", "excludeCharacters": " ;+%{}@'\"`/\\#", "excludePunctuation": true, "passwordLength": 20}}], "additionalAllowedCIDRs": ["*********/16", "*********/16", "***********/16"]}, "ecr": {"sourceAccount": "************", "cleanupLambda": {"lambdaPath": "assets/lambda/ecr-cleanup-lambda", "functionName": "ECRCleanupLambda", "handler": "main.handler", "runtime": "PYTHON_3_9", "environment": {"REGION": "us-east-2", "DRYRUN": "false", "IMAGES_TO_KEEP": "100"}, "timeoutInMin": 2, "region": "us-east-2"}}, "ses": {"configurationSet": {"name": "Unblocked-SES-ConfigurationSet", "s3BucketName": "unblocked-ses-kinesis", "iamRoleName": "Unblocked-SES-ConfigurationSet-Role", "kinesisName": "Unblocked-SES-Kinesis-DataStream", "kinesisIamRoleName": "Unblocked-SES-Kinesis-Iam-Role"}}, "iam": {"createK8sDeployerRole": false, "createK8sReaderRole": false, "createCloudWatchReadOnlyRole": false, "createCrossAccountAdminReadOnlyRole": false, "createCrossAccountS3ReadOnlyRole": false}, "httpProxy": {"autoScalingGroup": {"name": "HttpProxyASG", "maxCapacity": 3, "minCapacity": 1, "desiredCapacity": 1, "instanceType": "t4g.small", "machineAmiType": "ARM64", "launchTemplate": {"name": "HttpProxyLaunchTemplate", "instanceType": "t4g.small", "keyPairName": "HttpProxyKeypair", "machineAmiType": "ARM64", "userData": {"type": "linux", "userDataParts": [{"userDataFilePath": "assets/userdata/http-proxy-node.sh"}]}}}, "allowSSH": false}, "sqsDeadLetterQueues": [], "sqsQueues": [], "s3Buckets": [{"name": "external-alb-access-logs-prod", "addAccessLogPolicy": true, "enableVersioning": true, "enableLogRetentionRule": true}, {"name": "confluence-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "jira-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "linear-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "scm-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "pull-request-data-pipeline-sandbox", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "code-ingestion-data-pipeline-sandbox", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "topics-data-pipeline-sandbox", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}], "customerAssets": {"standardS3Bucket": {"name": "user-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, "audioS3Bucket": {"name": "user-audio-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, "blobS3Bucket": {"name": "user-blob-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, "imageS3Bucket": {"name": "user-image-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 180, "deepArchiveAccessTierTimeDays": 365}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, "textS3Bucket": {"name": "user-text-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 180, "deepArchiveAccessTierTimeDays": 365}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, "videoS3Bucket": {"name": "user-video-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}}, "huggingFaceModels": {"disable": true, "apiGateway": {"name": "hf-api_gw", "apiCertName": "hf.api.prod.getunblocked.com", "restApiName": "hf-api", "enableLogs": true}, "models": [{"modelName": "teknium/OpenHermes-2.5-Mistral-7B", "modelTask": "text-generation", "instanceType": "ml.g5.2xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLMistral7BChatSagemakerTrigger", "handler": "app.handler"}, "modelDeepLearningContainer": {"repositoryName": "huggingface-pytorch-tgi-inference", "tag": "2.0.1-tgi1.1.0-gpu-py39-cu118-ubuntu20.04"}, "modelToken": {"name": "HUGGING_FACE_HUB_TOKEN", "secretName": "hf-hub-token"}, "modelEnvironment": {"SM_NUM_GPUS": "1", "MAX_INPUT_LENGTH": "2048", "MAX_TOTAL_TOKENS": "4096", "MAX_BATCH_TOTAL_TOKENS": "4096"}, "modelAutoScale": {"maxRequestsPerSecond": 4, "maxCapacity": 10, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 120, "safetyFactor": 0.05}, "apiGatewayEndpoint": {"name": "ml-mistral-7b-chat-sagemaker-endpoint", "path": "transformers/mistral-7b-chat"}}, {"modelName": "hkunlp/instructor-large", "modelTask": "feature-extraction", "modelDockerImage": {"name": "instructor-large-embedding-model", "directory": "assets/image/machine-learning/instructor-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g4dn.xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLInstructorEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"minCapacity": 2, "maxRequestsPerSecond": 2, "maxCapacity": 20, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 360, "safetyFactor": 0.15}, "apiGatewayEndpoint": {"name": "ml-instructor-sagemaker-endpoint", "path": "embeddings/instructor"}}, {"modelName": "hkunlp/instructor-xl", "modelTask": "feature-extraction", "modelDockerImage": {"name": "instructor-xl-embedding-model", "directory": "assets/image/machine-learning/instructor-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g4dn.xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLInstructorXLEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 5, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 10, "safetyFactor": 0.25}, "modelEnvironment": {"INSTRUCTOR_MODEL": "hkunlp/instructor-xl"}, "apiGatewayEndpoint": {"name": "ml-instructor-xl-sagemaker-endpoint", "path": "embeddings/instructor-xl"}}, {"modelName": "intfloat/e5-mistral-7b-instruct", "modelTask": "feature-extraction", "modelDockerImage": {"name": "e5-mistral-embedding-model", "directory": "assets/image/machine-learning/e5-mistral-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g5.2xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLE5MistralEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 5, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 10, "safetyFactor": 0.25}, "apiGatewayEndpoint": {"name": "ml-e5-mistral-sagemaker-endpoint", "path": "embeddings/e5-mistral"}}, {"modelName": "unblocked/topic-mapping", "modelTask": "topic-mapping", "modelDockerImage": {"name": "unblocked-topic-mapping-model", "directory": "assets/image/machine-learning/topic-mapping-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLTopicMappingSagemakerTrigger", "handler": "app.handler"}, "instanceType": "ml.r5.2xlarge", "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 10, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 120, "safetyFactor": 0.05}, "apiGatewayEndpoint": {"name": "ml-topic-mapping-sagemaker-endpoint", "path": "models/topic-mapping"}}, {"modelName": "TheBloke/Yi-34B-200K-DARE-megamerge-v8-AWQ", "modelTask": "text-generation", "instanceType": "ml.g5.12xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLYi34B200KSagemakerTrigger", "handler": "app.handler"}, "modelDeepLearningContainer": {"repositoryName": "huggingface-pytorch-tgi-inference", "tag": "2.1.1-tgi1.4.0-gpu-py310-cu121-ubuntu20.04"}, "modelToken": {"name": "HUGGING_FACE_HUB_TOKEN", "secretName": "hf-hub-token"}, "modelAutoScale": {"maxRequestsPerSecond": 2, "maxCapacity": 3, "scaleInCoolDownSeconds": 30, "scaleOutCoolDownSeconds": 600, "safetyFactor": 0.1}, "modelEnvironment": {"SM_NUM_GPUS": 4, "MAX_INPUT_LENGTH": 11000, "MAX_BATCH_PREFILL_TOKENS": 11000, "MAX_BATCH_TOTAL_TOKENS": 96000, "MAX_CONCURRENT_REQUESTS": 16, "MAX_TOTAL_TOKENS": 12000, "HF_MODEL_QUANTIZE": "awq", "DISABLE_EXLLAMA": "true", "CUDA_MEMORY_FRACTION": ".85"}, "apiGatewayEndpoint": {"name": "ml-yi-34B-200k-sagemaker-endpoint", "path": "transformers/yi-34B-200K"}}]}, "pullRequestDataPipeline": {"disable": true, "name": "PullRequestDataPipeline", "stateMachine": {"name": "PullRequestDataPipelineStateMachine", "endpointName": "pull-request-data-pipeline", "assetBucket": {"name": "pull-request-data-pipeline-asset", "enableVersioning": true, "enableUserdataRetentionRule": true}, "glueStage": {"name": "PreProcessPullRequestGlueStage", "stage": "Glue", "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["Glue.ConcurrentRunsExceededException", "States.Timeout"]}, "timeoutInMin": 30, "glueJobFilePath": "assets/glue/pull-request-data-pipeline/preprocess/src/glue_etl.py", "glueJobNumberOfWorkers": 16, "glueJobMaxConcurrentRuns": 30, "glueArgumentsJsonPath": "$.PreProcessGlue", "glueResultJsonPath": "$.Result"}, "preProcessStages": [{"name": "PullRequestSummariesStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 720, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "processDockerImage": {"name": "pull-request-data-pipeline-summary", "directory": "assets/image/pull-request-data-pipeline/summaries-processor-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "m5.4xlarge", "processJobNameJsonPath": "$.PullRequestSummaries.ProcessingJobName", "processS3Inputs": [{"inputName": "input", "inputJsonPath": "$.PullRequestSummaries.ProcessInput", "contentType": "application/jsonlines"}], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.PullRequestSummaries.ProcessOutput"}], "processResultJsonPath": "$.Result.PullRequestSummaries", "processEnvironmentJsonPath": "$.PullRequestSummaries.ProcessEnvironment", "processCompletionLambdaStage": {"name": "PullRequestSummariesCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "ConnectFailedException"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.PullRequestSummariesCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "PullRequestSummariesTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.PullRequestSummaries.ProcessOutput", "AMQ_HOST": "ADD_ACTIVE_MQ_HOST_NAME_HERE", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-1", "AMQ_QUEUE": "/queue/pr_summaries_ingestion_completion", "AMQ_EVENT_TYPE": "PullRequestSummariesCompletionEvent"}}}}]}}, "codeIngestionDataPipeline": {"disable": true, "name": "CodeIngestionDataPipeline", "stateMachine": {"name": "CodeIngestionDataPipelineStateMachine", "endpointName": "code-ingestion-data-pipeline", "preProcessStages": [{"name": "CodeIngestionStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 1440, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "processDockerImage": {"name": "code-ingestion-data-pipeline-embeddings", "directory": "assets/image/code-ingestion-data-pipeline/embeddings-processor-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "g4dn.xlarge", "processJobNameJsonPath": "$.CodeIngestion.ProcessingJobName", "processS3Inputs": [], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.CodeIngestion.ProcessOutput"}], "processResultJsonPath": "$.Result.CodeIngestion", "processEnvironmentJsonPath": "$.CodeIngestion.ProcessEnvironment", "processCompletionLambdaStage": {"name": "CodeIngestionCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "ConnectFailedException"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.CodeIngestionCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "CodeIngestionTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.CodeIngestion.ProcessOutput", "AMQ_HOST": "ADD_ACTIVE_MQ_HOST_NAME_HERE", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-1", "AMQ_QUEUE": "/queue/source_code_events", "AMQ_EVENT_TYPE": "CodeIngestionCompletionEvent"}}}}]}}, "topicIngestionDataPipeline": {"disable": true, "name": "TopicIngestionDataPipeline", "stateMachine": {"name": "TopicIngestionDataPipelineStateMachine", "endpointName": "topic-ingestion-data-pipeline", "preProcessStages": [{"name": "TopicIngestionStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 480, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "processDockerImage": {"name": "topic-ingestion-data-pipeline", "directory": "assets/image/topic-ingestion-data-pipeline/cluster-processor-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "c5.4xlarge", "processJobNameJsonPath": "$.TopicIngestion.ProcessingJobName", "processS3Inputs": [], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.TopicIngestion.ProcessOutput"}], "processResultJsonPath": "$.Result.TopicIngestion", "processEnvironmentJsonPath": "$.TopicIngestion.ProcessEnvironment", "processCompletionLambdaStage": {"name": "TopicIngestionCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "ConnectFailedException"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.TopicIngestionCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "TopicIngestionTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.TopicIngestion.ProcessOutput", "AMQ_HOST": "ADD_ACTIVE_MQ_HOST_NAME_HERE", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-1", "AMQ_QUEUE": "/queue/topic_priority_events", "AMQ_EVENT_TYPE": "TopicIngestionCompletionEvent"}}}}]}}, "backup": {"enabled": true, "isBackupDr": true, "drVaultAlias": "alias/rds-backup-dr-prod"}}