import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { Dynamodb } from '../common/dynamodb/config';
import { TagHelpers } from '../common/utils/tag-helpers';

interface DynamodbStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dynamodb: Dynamodb;
}

export interface DynamodbStackProps extends StackProps {
    buildConfig: DynamodbStackConfig;
}

export class DynamodbStack extends Stack {
    readonly tables = new Map<string, dynamodb.TableV2>();

    constructor(scope: Construct, id: string, props: DynamodbStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping DynamodbStack.');
            return;
        }

        const dynamodbCofig = props.buildConfig.dynamodb;
        if (!dynamodbCofig) return;

        for (const tConfig of dynamodbCofig.tables) {
            const table = new dynamodb.TableV2(this, `${tConfig.name}Table`, {
                tableName: tConfig.name,
                tableClass: dynamodb.TableClass.STANDARD,
                partitionKey: tConfig.partitionKey,
                sortKey: tConfig.sortKey,
                deletionProtection: tConfig.deletionProtection,
                removalPolicy: tConfig.removalPolicy,
                replicas: dynamodbCofig.replicaRegions.map((r) => ({ region: r })),
                billing: dynamodb.Billing.onDemand(),
                globalSecondaryIndexes: tConfig.globalIndexes,
                timeToLiveAttribute: tConfig.timeToLiveAttribute,
                encryption: tConfig.enableEncryption ? dynamodb.TableEncryptionV2.dynamoOwnedKey() : undefined,
            });

            if (tConfig.tags && tConfig.tags.length > 0) {
                TagHelpers.addTypedTagsToResources(tConfig.tags, table);
            }

            this.tables.set(tConfig.name, table);
        }
    }
}
