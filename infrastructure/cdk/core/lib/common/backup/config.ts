import { JSONObject, optional, required, array } from 'ts-json-object';

/**
 * Config for AWS Backup (env-driven; placed under the stack's buildConfig.backup).
 * This mirrors how other common/* configs are modeled.
 */

export class BackupPlanSettings extends JSONObject {
    /** Optional friendly name; will be used as the plan name */
    @required
    name?: string;

    /** How long to retain backups in the PRIMARY vault */
    @required
    retentionDays!: number; // 1..35 for RDS/Aurora

    /** Optional DR retention (if omitted, uses retentionDays) */
    @optional
    drRetentionDays?: number;

    /** Cron expression for schedule; default hourly at :00 */
    @required
    scheduleCronExpression?: string;

    /** Resources to include: OR across these tag key/value pairs */
    @required
    selectionTags!: { [key: string]: string };

    @optional(false)
    enableContinuousBackup?: boolean; // PITR

    @optional(8)
    startWindowHours?: number;        // start within

    @optional(168)
    completionWindowHours?: number;   // complete within
}

export class BackupSettings extends <PERSON><PERSON><PERSON><PERSON>bject {
    @optional(false)
    enabled?: boolean; // default off

    @optional(false)
    isBackupDr?: boolean;

    /** 1..35 days for Aurora/RDS */
    @optional(7)
    retentionDays?: number;

    @optional
    selectionTags?: { [key: string]: string };

    /** AWS Backup schedule (use cron format, e.g. "cron(0 2 * * ? *)" or "cron(0 * * * ? *)" for hourly) */
    @optional('cron(0 2 * * ? *)')
    scheduleCronExpression?: string;

    /** Optional KMS aliases for vaults */
    @optional('alias/rds-backup-primary')
    primaryVaultAlias?: string;

    @optional('alias/rds-backup-dr')
    drVaultAlias?: string;

    @optional
    drVaultArn?: string;

    @optional
    @array(BackupPlanSettings)
    plans?: BackupPlanSettings[];

    @optional(false)
    enableContinuousBackup?: boolean;  // PITR default off
    @optional(8)
    startWindowHours?: number;         // start within X hours (default 1)
    @optional(168)
    completionWindowHours?: number;    // optional, finish within X hours
}

