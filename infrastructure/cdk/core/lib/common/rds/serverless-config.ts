import { JSONObject, optional, required, validate, array } from 'ts-json-object';
import { RemovalPolicy } from 'aws-cdk-lib';

export class AuroraServerlessDatabase extends JSONObject {
    @required
    cdkResourceName: string;

    @optional('postgres')
    dbUsername: string;

    @optional(false)
    isPrimary: boolean;

    @optional(false)
    allowMajorVersionUpgrade: boolean;

    @optional(true)
    autoMinorVersionUpgrade: boolean;

    @optional(false)
    enablePerformanceInsights: boolean;

    @optional(undefined)
    performanceInsightRetention?: number;

    @optional(0)
    enhancedMonitoringInterval: number;

    @required
    auroraPostgresFullVersion: string;

    @required
    auroraPostgresMajorVersion: string;

    // Aurora Serverless v2 specific configuration
    @optional(0.5)
    minCapacity: number;

    @optional(4)
    maxCapacity: number;

    @optional(undefined)
    snapshotIdentifier?: string;

    @optional(undefined)
    transactionTimeoutSeconds?: number;

    @optional(undefined)
    statementTimeoutSeconds?: number;

    @optional(undefined)
    workMemMB?: number;

    @optional(RemovalPolicy.RETAIN)
    removalPolicy: RemovalPolicy;

    @optional(true)
    deletionProtection: boolean;

    @optional(true)
    enableBackup: boolean;

    @optional
    tags?: { [key: string]: string }
}

export class AuroraServerless extends JSONObject {
    @required
    @array(AuroraServerlessDatabase)
    databases: Array<AuroraServerlessDatabase>;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any,@typescript-eslint/no-unused-vars
    @validate((serverless: AuroraServerless, key: string, value: any) => {
        if (serverless.databases.filter((obj) => obj.isPrimary).length !== 1) {
            throw new TypeError(`Only one database can be assigned as primary at any given time`);
        }
    })
    @optional(false)
    useExistingS3BackupBucket: boolean;

    @optional([])
    additionalAllowedCIDRs: string[];

    @optional
    s3BackupReplicationTargetRegion?: string;

    @optional
    s3BackupReplicationTargetKMSKeyArn?: string;

    @optional(false)
    disableS3Backup: boolean;

    @optional(30)
    backupRetentionDays: number;

    @required
    endpointCNAMEPrefix: string;

    @optional(true)
    enableCloudWatchAlarms: boolean;

    @optional(true)
    enableSNSNotifications: boolean;
}
