import { InstanceClass, InstanceSize } from 'aws-cdk-lib/aws-ec2';
import { array, JSONObject, optional, required, validate } from 'ts-json-object';

export class RDSPostgresDatabase extends J<PERSON>NObject {
    @required
    cdkResourceName: string;
    @optional('postgres')
    dbUsername: string;
    @optional(false)
    isPrimary: boolean;
    @optional(false)
    allowMajorVersionUpgrade: boolean;
    @optional(false)
    ioOptimized: boolean;
    @optional(true)
    autoMinorVersionUpgrade: boolean;
    @optional(true)
    enablePerformanceInsights: boolean;
    @optional(7)
    performanceInsightRetention: number;
    @optional(0)
    enhancedMonitoringInterval: number;
    @required
    auroraPostgresFullVersion: string;
    @required
    auroraPostgresMajorVersion: string;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @validate((rdsPostgres: RDSPostgresDatabase, key: string, value: any) => {
        // Full list of values:  https://github.com/aws/aws-cdk/blob/master/packages/%40aws-cdk/aws-ec2/lib/instance-types.ts
        // https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Concepts.DBInstanceClass.html#Concepts.DBInstanceClass.SupportAurora
        if (!Object.values(InstanceClass).includes(value)) {
            throw new TypeError(`Invalid ec2 instance class ${value}}`);
        }
    })
    @required
    writerInstanceClass: string;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @validate((rdsPostgres: RDSPostgresDatabase, key: string, value: any) => {
        // Full list of values:  https://github.com/aws/aws-cdk/blob/master/packages/%40aws-cdk/aws-ec2/lib/instance-types.ts
        // https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Concepts.DBInstanceClass.html#Concepts.DBInstanceClass.SupportAurora
        if (!Object.values(InstanceSize).includes(value)) {
            throw new TypeError(`Invalid ec2 instance size ${value}}`);
        }
    })
    @required
    writerInstanceSize: string;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @validate((rdsPostgres: RDSPostgresDatabase, key: string, value: any) => {
        // Full list of values:  https://github.com/aws/aws-cdk/blob/master/packages/%40aws-cdk/aws-ec2/lib/instance-types.ts
        // https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Concepts.DBInstanceClass.html#Concepts.DBInstanceClass.SupportAurora
        if (!Object.values(InstanceClass).includes(value)) {
            throw new TypeError(`Invalid ec2 instance class ${value}}`);
        }
    })
    @required
    readerInstanceClass: string;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    @validate((rdsPostgres: RDSPostgresDatabase, key: string, value: any) => {
        // Full list of values:  https://github.com/aws/aws-cdk/blob/master/packages/%40aws-cdk/aws-ec2/lib/instance-types.ts
        // https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/Concepts.DBInstanceClass.html#Concepts.DBInstanceClass.SupportAurora
        if (!Object.values(InstanceSize).includes(value)) {
            throw new TypeError(`Invalid ec2 instance size ${value}}`);
        }
    })
    @required
    readerInstanceSize: string;
    @required
    readerInstanceCount: number;

    @optional(undefined)
    snapshotIdentifier: string;
    @optional(undefined)
    transactionTimeoutSeconds?: number;
    @optional(undefined)
    statementTimeoutSeconds?: number;
    // https://www.pgmustard.com/blog/work-mem#:~:text=This%20is%20done%20by%20adjusting,starting%20point%20for%20most%20people.
    // 4MB default, recommended 16MB
    @optional(undefined)
    workMemMB?: number;

    @optional
    tags?: { [key: string]: string };
}

export class RDSPostgres extends JSONObject {
    @required
    @array(RDSPostgresDatabase)
    databases: Array<RDSPostgresDatabase>;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any,@typescript-eslint/no-unused-vars
    @validate((rdsPostgres: RDSPostgres, key: string, value: any) => {
        if (rdsPostgres.databases.filter((obj) => obj.isPrimary).length != 1) {
            throw new TypeError(`Only one database can be assigned as primary at any given time`);
        }
    })
    @optional(false)
    UseExistingCreateS3CrossRegionBackupBucket: boolean;
    @optional([])
    additionalAllowedCIDRs: string[];
    @optional
    s3BackupReplicationTargetRegion: string;
    @optional
    s3BackupReplicationTargetKMSKeyArn: string;
    @optional(false)
    disableS3Backup: boolean;
    @optional(30)
    backupRetentionDays: number;
    // CNAME format: {endpointCNAMEPrefix}-{region}-{w,r}.{env}.getunblocked.com
    // e.g 3c33e81e-0055-45ed-99e3-d604f6fc016c-us-west-2-w.dev.getunblocked.com
    // Do NOT use a descriptive prefix eg. db, rds, database
    @required
    endpointCNAMEPrefix: string;
}
