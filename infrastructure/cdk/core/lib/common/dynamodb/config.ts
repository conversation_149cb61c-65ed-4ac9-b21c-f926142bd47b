import { JSONObject, required, optional, custom, array } from 'ts-json-object';
import { RemovalPolicy } from 'aws-cdk-lib';
import { AttributeType, ProjectionType } from 'aws-cdk-lib/aws-dynamodb';
import { Tag } from '../tag/config';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function cdkEnumValueLookup(obj: any, key: string, value: string, lookupType: any, lookupTypeName: string): any {
    const enumValue = value.toUpperCase();
    if (!Object.keys(lookupType).includes(enumValue)) {
        throw new TypeError(`Invalid value ${value} for ${lookupTypeName}`);
    }
    return lookupType[enumValue as keyof typeof lookupType];
}

export class DynamodbKey extends JSONObject {
    @required
    name: string;

    // Accepted values: BINARY, NUMBER, STRING
    @custom((d: DynamodbKey, k: string, v: string) => {
        return cdkEnumValueLookup(d, k, v, AttributeType, 'AttributeType');
    })
    @required
    type: AttributeType;
}

export class DynamodbGlobalIndex extends JSONObject {
    @required
    indexName: string;

    @required
    partitionKey: DynamodbKey;

    @optional
    sortKey: DynamodbKey;

    @optional
    projectionType: ProjectionType;

    @optional
    @array([])
    nonKeyAttributes: string[];
}

export class DynamodbTable extends JSONObject {
    @required
    name: string;

    @required
    partitionKey: DynamodbKey;

    @optional
    sortKey: DynamodbKey;

    // Accepted values: RETAIN_ON_UPDATE_OR_DELETE, RETAIN, DESTROY, SNAPSHOT
    @custom((d: DynamodbTable, k: string, v: string) => {
        return cdkEnumValueLookup(d, k, v, RemovalPolicy, 'RemovalPolicy');
    })
    @required
    removalPolicy: RemovalPolicy;

    @optional(true)
    deletionProtection: boolean;

    @optional(true)
    enableEncryption: boolean;

    @optional(undefined)
    timeToLiveAttribute: string;

    @optional([])
    @array(DynamodbGlobalIndex)
    globalIndexes: Array<DynamodbGlobalIndex>;

    @optional([])
    @array(Tag)
    tags: Tag[]
}

export class Dynamodb extends JSONObject {
    @optional(undefined)
    replicaRegions: string[];

    @optional([])
    @array(DynamodbTable)
    tables: Array<DynamodbTable>;
}
