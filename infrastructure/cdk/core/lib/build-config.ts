import { array, JSONObject, optional, required } from 'ts-json-object';
import { CertRequest } from './common/acm/config';
import { AwsSecret } from './common/asm/config';
import { CloudFront } from './common/cloudfront/config';
import { AwsSecurityAlarms } from './common/cloudwatch/config';
import { CustomerHTTPProxyConfig } from './common/ec2/config';
import { AuroraServerless } from './common/rds/serverless-config';
import { TransitGateway } from './common/transit-gateway/config';
import { DataPipeline } from './data-pipelines/config';
import { Dns } from './common/dns/config';
import { Dynamodb } from './common/dynamodb/config';
import { BastionHost, GradleBuildCacheNode, HttpProxy } from './ec2/config';
import { Ecr } from './common/ecr/config';
import { Eks } from './eks/config';
import { Redis } from './common/elasticache/config';
import { Iam } from './common/iam/config';
import { ECSMachineLearningModels, SagemakerMachineLearningModels } from './machine-learning/config';
import { ActiveMQMesh } from './common/mq/config';
import { RDSPostgres } from './common/rds/config';
import { CustomerAssets } from './s3/config';
import { StaticSite } from './static-sites/config';
import { VPCConfig } from './common/vpc/config';
import { WebAclCloudFront } from './wafv2/config';
import { Batch } from './common/batch/config';
import { S3Bucket } from './common/s3/config';
import { EFSFileSystem } from './common/efs/config';
import { OpenSearch } from './common/opensearch/config';
import { BackupSettings } from './common/backup/config'

export class AwsEnvAccount extends JSONObject {
    @required
    awsAccountID: string;
    @required
    rootRegion: string;
    @required
    targetRegion: string;
    @required
    awsOrgRootAccountID: string;
    @optional('arn:aws:organizations::************:organization/o-f79h5qd0ig')
    awsOrgArn: string;
    @optional(['us-west-2, us-east-1'])
    allowedRegions: string[];
    @required
    notificationEmail: string;
    @optional('<EMAIL>')
    opsNotificationEmail: string;
    @optional(false)
    coldSite: boolean;
    @optional(90)
    globalLogRetentionDays: number;
}

export class CdkAppInfo extends JSONObject {
    @required
    app: string;
    @required
    environment: string;
    @required
    version: string;
    @required
    build: string;
}

/**
 * This class is used for parsing regular application
 * env configs only
 */
export class BuildConfig extends JSONObject {
    @required
    awsEnvAccount: AwsEnvAccount;

    @required
    cdkAppInfo: CdkAppInfo;

    @optional
    dns: Dns;

    @optional([])
    @array(CertRequest)
    certRequests: Array<CertRequest>;

    @required
    eks: Eks;

    @optional
    coreVPC: VPCConfig;

    @optional
    eksVPC: VPCConfig;

    @optional
    rdsPostgres: RDSPostgres;

    @optional
    auroraServerless: AuroraServerless;

    @optional
    dynamodb: Dynamodb;

    @required
    ecr: Ecr;

    @optional
    redis: Redis;

    @optional
    activeMQMesh: ActiveMQMesh;

    @optional(undefined)
    openSearch?: OpenSearch;

    @optional
    iam: Iam;

    @optional(undefined)
    testBatchSubmitPipeline?: DataPipeline;

    @optional
    pullRequestDataPipeline: DataPipeline;

    @optional
    codeIngestionDataPipeline: DataPipeline;

    @optional
    topicIngestionDataPipeline: DataPipeline;

    @optional([])
    @array(Batch)
    batches: Array<Batch>;

    @optional([])
    @array(S3Bucket)
    s3Buckets: Array<S3Bucket>;

    @optional
    sagemakerMachineLearningModels: SagemakerMachineLearningModels;

    @optional
    ecsMachineLearningModels: ECSMachineLearningModels;

    @required
    customerAssets: CustomerAssets;

    @optional([])
    @array(AwsSecret)
    secrets: Array<AwsSecret>;

    @optional
    httpProxy: HttpProxy;

    @optional([])
    @array(EFSFileSystem)
    efsFileSystems: Array<EFSFileSystem>;

    @optional([])
    @array(CustomerHTTPProxyConfig)
    customerHTTPProxy: Array<CustomerHTTPProxyConfig>;

    @optional
    backup: BackupSettings;
}

/**
 *  This class is used for parsing Standard region configs only
 *  Some AWS Services (CloudFront, WAF) have restricted availability
 *  and can consume resources in Standard (us-east-1) Region only
 */
export class StandardBuildConfig extends JSONObject {
    @required
    awsEnvAccount: AwsEnvAccount;

    @required
    dns: Dns;

    @required
    cdkAppInfo: CdkAppInfo;

    @optional([])
    @array(CertRequest)
    certRequests: Array<CertRequest>;

    @optional
    webAclCloudFront: WebAclCloudFront;

    @optional([])
    @array(StaticSite)
    staticSites: Array<StaticSite>;

    @required
    cloudFront: CloudFront;

    @optional
    sendGridCloudFront: CloudFront;

    @optional
    docsCloudFront: CloudFront;

    @optional
    subdomainRedirectCloudFront: CloudFront;
}

export class MgtBuildConfig extends JSONObject {
    @required
    awsEnvAccount: AwsEnvAccount;

    @required
    cdkAppInfo: CdkAppInfo;

    @optional
    dns: Dns;

    @optional
    iam: Iam;

    @optional
    awsSecurityAlarms: AwsSecurityAlarms;
}

/**
 *  This class is used for parsing sec-ops env
 *  configs only
 */
export class SecOpsBuildConfig extends JSONObject {
    @required
    awsEnvAccount: AwsEnvAccount;

    @required
    dns: Dns;

    @required
    cdkAppInfo: CdkAppInfo;

    @required
    coreVPC: VPCConfig;

    @required
    ecr: Ecr;

    @optional([])
    @array(CertRequest)
    certRequests: Array<CertRequest>;

    @optional([])
    @array(EFSFileSystem)
    efsFileSystems: Array<EFSFileSystem>;

    @required
    eks: Eks;

    @required
    iam: Iam;

    @required
    bastionHost: BastionHost;

    @required
    gradleBuildCacheNode: GradleBuildCacheNode;

    @optional([])
    @array(S3Bucket)
    s3Buckets: Array<S3Bucket>;

    @optional
    transitGateway: TransitGateway;
}
