import { Stack, StackProps, Duration, RemovalP<PERSON>y, Size, Tags } from 'aws-cdk-lib';
import * as route53 from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';
import { BuildConfig } from '../build-config';
import { SubnetType, SecurityGroup, Peer, Port } from 'aws-cdk-lib/aws-ec2';
import { RDSPostgresDatabase } from '../common/rds/config';
import { NetworkStack } from '../vpc/network-stack';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as events from 'aws-cdk-lib/aws-events';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as path from 'path';
import { S3BucketConstruct } from '../common/s3-bucket-construct';
import { EnumHelpers } from '../common/utils/enum-helpers';
import { BucketEncryption, IBucket } from 'aws-cdk-lib/aws-s3';
import * as ec2 from 'aws-cdk-lib/aws-ec2';

export class DatabaseStack extends Stack {
    readonly rdsPostgres = new Map<string, rds.DatabaseCluster>();
    readonly rdsPostgresSG: SecurityGroup;
    readonly backupBucket: IBucket;
    readonly snsTopic: sns.Topic;
    readonly rdsS3BackupRole: iam.Role;

    constructor(
        scope: Construct,
        id: string,
        props: StackProps,
        buildConfig: BuildConfig,
        private networkStack: NetworkStack
    ) {
        super(scope, id, props);
        this.addDependency(this.networkStack);

        // Backup bucket is created in both hot and cold site
        if (!buildConfig.rdsPostgres.UseExistingCreateS3CrossRegionBackupBucket) {
            this.backupBucket = this.createBackupS3Bucket(buildConfig).s3Bucket;
        } else {
            const bucketName = `unblocked-postgres-backup-${buildConfig.cdkAppInfo.environment}-${buildConfig.awsEnvAccount.targetRegion}`;
            this.backupBucket = s3.Bucket.fromBucketName(this, bucketName, bucketName);
        }

        if (buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping database creation since target region is a cold site.');
            return;
        }

        if (!buildConfig.rdsPostgres.disableS3Backup) {
            // Create S3 backup IAM role
            this.rdsS3BackupRole = this.createrdsS3BackupRole(
                buildConfig.awsEnvAccount.targetRegion,
                this.backupBucket.bucketArn
            );
        }

        this.rdsPostgresSG = new SecurityGroup(this, 'rdsPostgresSG', {
            vpc: this.networkStack.coreVpc,
            securityGroupName: 'rdsPostgres',
            description: 'security group for a rds postgres cluster',
        });

        for (const cidr of buildConfig.rdsPostgres.additionalAllowedCIDRs) {
            this.rdsPostgresSG.addIngressRule(Peer.ipv4(cidr), Port.tcp(5432), `allow inbound traffic from ${cidr}`);
        }

        // Setup notifications for Cluster events
        this.snsTopic = this.createSNSTopic(buildConfig);
        this.createEventSubscription();

        for (const db of buildConfig.rdsPostgres.databases) {
            // Enhanced RDS Monitoring
            let monitoringInterval = undefined;
            if (db.enhancedMonitoringInterval > 0) {
                monitoringInterval = Duration.seconds(db.enhancedMonitoringInterval);
            }

            const baseDatabaseProps = {
                engine: rds.DatabaseClusterEngine.auroraPostgres({
                    version: rds.AuroraPostgresEngineVersion.of(
                        db.auroraPostgresFullVersion,
                        db.auroraPostgresMajorVersion
                    ),
                }),
                parameters: this.getDbParameters(db),
                defaultDatabaseName: 'maindb',
                deletionProtection: true,
                iamAuthentication: true,
                monitoringInterval: monitoringInterval,
                writer: rds.ClusterInstance.provisioned('writer', {
                    instanceType: new ec2.InstanceType(`${db.writerInstanceClass}.${db.writerInstanceSize}`),
                    allowMajorVersionUpgrade: db.allowMajorVersionUpgrade,
                    autoMinorVersionUpgrade: db.autoMinorVersionUpgrade,
                    isFromLegacyInstanceProps: true,
                    enablePerformanceInsights: db.enablePerformanceInsights,
                    performanceInsightRetention: db.performanceInsightRetention,
                    publiclyAccessible: false,
                    //ec2.InstanceType.of(ec2.InstanceClass.R6G, ec2.InstanceSize.XLARGE4),
                }),
                readers: this.readerInstanceConfigs(db),
                backup: {
                    retention: Duration.days(buildConfig.rdsPostgres.backupRetentionDays),
                },
                vpc: this.networkStack.coreVpc,
                vpcSubnets: {
                    subnetType: SubnetType.PRIVATE_ISOLATED,
                },
                securityGroups: [this.rdsPostgresSG],
                storageEncrypted: true,
                storageType: db.ioOptimized ? rds.DBClusterStorageType.AURORA_IOPT1 : rds.DBClusterStorageType.AURORA,
                instanceUpdateBehaviour: rds.InstanceUpdateBehaviour.ROLLING,
            };

            let rdsPostgres: rds.DatabaseCluster;
            if (db.snapshotIdentifier) {
                rdsPostgres = new rds.DatabaseClusterFromSnapshot(this, db.cdkResourceName, {
                    ...baseDatabaseProps,
                    snapshotIdentifier: db.snapshotIdentifier,
                    snapshotCredentials: rds.SnapshotCredentials.fromGeneratedSecret(db.dbUsername),
                });
            } else {
                rdsPostgres = new rds.DatabaseCluster(this, db.cdkResourceName, {
                    ...baseDatabaseProps,
                    credentials: rds.Credentials.fromGeneratedSecret(db.dbUsername),
                });
            }
            this.rdsPostgres.set(db.cdkResourceName, rdsPostgres);

            // Cluster tagging
            if (db.tags) {
                for (const [k, v] of Object.entries(db.tags)) {
                    if (k && v) {
                        Tags.of(rdsPostgres).add(k, v, {
                            includeResourceTypes: ['AWS::RDS::DBCluster'],
                        });
                    }
                }
            }

            this.exportValue(rdsPostgres.clusterIdentifier);

            // Create resource alarms for DB cluster
            this.createDatabaseAlarms(buildConfig, rdsPostgres, db.cdkResourceName);

            // Setup nightly backup and export Lambda function
            if (!buildConfig.rdsPostgres.disableS3Backup) {
                this.setupBackupJobs(buildConfig, db.cdkResourceName, rdsPostgres.clusterIdentifier);
            }

            // Create CNAME for primary cluster
            if (db.isPrimary) {
                this.createCNAMERecords(
                    buildConfig,
                    rdsPostgres.clusterEndpoint.hostname,
                    rdsPostgres.clusterReadEndpoint.hostname
                );
            }
        }
    }

    private getDbParameters(db: RDSPostgresDatabase): { [p: string]: string } | undefined {
        const parameters: { [key: string]: string } = {
            log_autovacuum_min_duration: '0',
            log_connections: 'on',
            log_disconnections: 'on',
            log_error_verbosity: 'default',
            log_lock_waits: 'on',
            log_min_duration_statement: '0',
            log_parameter_max_length_on_error: '1024',
            log_rotation_size: '1000000',
            log_statement: 'none',
            log_statement_stats: 'off',
            log_temp_files: '0',
        };

        // recommended:
        // https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/proactive-insights.idle-txn.html
        // https://postgresqlco.nf/doc/en/param/idle_in_transaction_session_timeout/
        if (db.transactionTimeoutSeconds) {
            // default is 1 day
            parameters['idle_in_transaction_session_timeout'] = Duration.seconds(db.transactionTimeoutSeconds)
                .toMilliseconds()
                .toString();
        }

        // potentially hairy:
        // https://postgresqlco.nf/doc/en/param/lock_timeout/
        if (db.statementTimeoutSeconds) {
            // default is 0 (undefined)
            parameters['statement_timeout'] = Duration.seconds(db.statementTimeoutSeconds).toMilliseconds().toString();
        }

        if (db.workMemMB) {
            // By default, it is in KB
            parameters['work_mem'] = `${Size.mebibytes(db.workMemMB).toKibibytes()}`;
        }

        return parameters;
    }

    private readerInstanceConfigs(db: RDSPostgresDatabase): rds.IClusterInstance[] | undefined {
        const readersInstanceConfigs = [];
        for (let i = 0; i < db.readerInstanceCount; i++) {
            readersInstanceConfigs.push(
                rds.ClusterInstance.provisioned(`reader${i}`, {
                    instanceType: new ec2.InstanceType(`${db.readerInstanceClass}.${db.readerInstanceSize}`),
                    allowMajorVersionUpgrade: db.allowMajorVersionUpgrade,
                    autoMinorVersionUpgrade: db.autoMinorVersionUpgrade,
                    isFromLegacyInstanceProps: true,
                    enablePerformanceInsights: db.enablePerformanceInsights,
                    performanceInsightRetention: db.performanceInsightRetention,
                    publiclyAccessible: false,
                })
            );
        }

        return readersInstanceConfigs.length > 0 ? readersInstanceConfigs : undefined;
    }

    /*
     * Create S3 bucket to hold database backups
     */
    private createBackupS3Bucket(buildConfig: BuildConfig): S3BucketConstruct {
        return new S3BucketConstruct(this, 'unblocked-postgres-backup', {
            id: 'unblocked-postgres-backups',
            awsEnvAccount: buildConfig.awsEnvAccount,
            cdkAppInfo: buildConfig.cdkAppInfo,
            addAccessLogPolicy: false,
            name: 'unblocked-postgres-backup',
            enableVersioning: true,
            bucketKeyEnabled: true,
            bucketEncryption: EnumHelpers.findEnumType(BucketEncryption, 'KMS'),
            publicReadAccess: false,
            transferAcceleration: false,
            removalPolicy: RemovalPolicy.RETAIN,
            enableUserdataRetentionRule: false,
            enableBackupRetentionPolicyRule: true,
            autoDeleteObjects: false,
            replicationTargetRegion: buildConfig.rdsPostgres.s3BackupReplicationTargetRegion,
            replicationTargetKMSKeyArn: buildConfig.rdsPostgres.s3BackupReplicationTargetKMSKeyArn,
        });
    }

    /*
     * Setup a Lambda function to trigger a DB snapshot and export to S3 every night
     */
    private createrdsS3BackupRole(region: string, bucketArn: string): iam.Role {
        // Role giving access to Lambda job as well as RDS service to create and export backups
        const rdsS3BackupRole = new iam.Role(this, `PostgresBackupToS3${region}-lambda`, {
            roleName: `PostgresBackupToS3${region}-lambda`,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('lambda.amazonaws.com'),
                new iam.ServicePrincipal('export.rds.amazonaws.com')
            ),
            description: `Role assumed by lambda job for backing up postgres to S3`,
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
            inlinePolicies: {
                RDSBackup: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: ['*'],
                            actions: [
                                'rds:DescribeDBSnapshots',
                                'rds:DescribeDBClusterSnapshots',
                                'rds:DescribeDBClusterSnapshotAttributes',
                                'rds:DescribeExportTasks',
                                'rds:CopyDBClusterSnapshot',
                                'rds:DeleteDBClusterSnapshot',
                                'rds:StartExportTask',
                            ],
                        }),
                        new iam.PolicyStatement({
                            resources: [this.backupBucket.encryptionKey?.keyArn || '', 'arn:aws:kms:*:*:alias/aws/rds'],
                            actions: [
                                'kms:Encrypt',
                                'kms:Decrypt',
                                'kms:GenerateDataKey',
                                'kms:GenerateDataKeyWithoutPlaintext',
                                'kms:ReEncryptFrom',
                                'kms:ReEncryptTo',
                                'kms:CreateGrant',
                                'kms:DescribeKey',
                                'kms:RetireGrant',
                            ],
                        }),
                        new iam.PolicyStatement({
                            resources: [bucketArn, `${bucketArn}/*`],
                            actions: [
                                's3:HeadObject',
                                's3:PutObject',
                                's3:GetObject',
                                's3:ListBucket',
                                's3:DeleteObject',
                                's3:PutObjectAcl',
                                's3:PutObjectTagging',
                                's3:DeleteObjectTagging',
                                's3:ListMultipartUploadParts',
                                's3:AbortMultipartUpload',
                                's3:CreateBucket',
                                's3:GetBucketLocation',
                            ],
                        }),
                    ],
                }),
            },
        });

        rdsS3BackupRole.addToPolicy(
            new iam.PolicyStatement({
                resources: [rdsS3BackupRole.roleArn],
                actions: ['iam:PassRole'],
            })
        );

        return rdsS3BackupRole;
    }

    /*
     * Setup a Lambda function to trigger a DB snapshot and export to S3 every night
     */
    private setupBackupJobs(buildConfig: BuildConfig, cdkResrouceName: string, clusterIdentifier: string) {
        // This is a default key created by AWS for RDS, we use it to make snapshot copy less painful
        const keyArn = `arn:aws:kms:${buildConfig.rdsPostgres.s3BackupReplicationTargetRegion}:${buildConfig.awsEnvAccount.awsAccountID}:alias/aws/rds`;

        // Lambda function to backup RDS snapshots to S3 as well as copying them to RDS snapshots in our cold site
        const rdsBackupExportLambdaFunction = new lambda.Function(
            this,
            `PostgresNightlyBackupToS3${buildConfig.awsEnvAccount.targetRegion}-${cdkResrouceName}`,
            {
                functionName: `PostgresNightlyBackupToS3${buildConfig.awsEnvAccount.targetRegion}-${cdkResrouceName}`,
                runtime: lambda.Runtime.PYTHON_3_13,
                memorySize: 128,
                timeout: Duration.minutes(15),
                handler: 'main.lambda_handler',
                code: lambda.Code.fromAsset(
                    path.join(__dirname, '../../assets/lambda/rds-aurora-postgres-backup-lambda')
                ),
                environment: {
                    REGION: buildConfig.awsEnvAccount.targetRegion,
                    ACCOUNT: buildConfig.awsEnvAccount.awsAccountID,
                    CLUSTER_NAME: clusterIdentifier,
                    BUCKET_NAME: this.backupBucket.bucketName,
                    IAM_ROLE_ARN: this.rdsS3BackupRole.roleArn,
                    S3_KMS_KEY_ARN: this.backupBucket.encryptionKey?.keyArn || '',
                    RDS_KMS_KEY_ARN: keyArn,
                },
                role: this.rdsS3BackupRole,
            }
        );

        // Run once an hour
        const eventRule = new events.Rule(this, 'scheduleRule', {
            schedule: events.Schedule.cron({ minute: '1' }),
        });
        eventRule.addTarget(new targets.LambdaFunction(rdsBackupExportLambdaFunction));
    }

    /*
     * SNS topic to notify AWS admins of various cluster events
     */
    private createSNSTopic(buildConfig: BuildConfig): sns.Topic {
        const topic = new sns.Topic(this, 'RDSNotificationTopic', {
            displayName: 'RDS Postgres Cluster Notifications',
        });

        // Send emails to admins
        topic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));
        return topic;
    }

    /*
     * Report Cluster events (e.g backup failures) to SNS topic
     */
    private createEventSubscription() {
        // Cluster-level events
        new rds.CfnEventSubscription(this, 'RDSClusterEvents', {
            snsTopicArn: this.snsTopic.topicArn,
            enabled: true,
            subscriptionName: 'RDSClusterEvents',
            sourceType: 'db-cluster',
            eventCategories: ['failure', 'failover', 'maintenance'],
        });

        // Instance-level events (just for backups + failures)
        new rds.CfnEventSubscription(this, 'RDSInstanceEvents', {
            snsTopicArn: this.snsTopic.topicArn,
            enabled: true,
            subscriptionName: 'RDSInstanceEvents',
            sourceType: 'db-instance',
            eventCategories: ['backup', 'failure'],
        });
    }

    private createDatabaseAlarms(buildConfig: BuildConfig, rdsPostgres: rds.DatabaseCluster, cdkResrouceName: string) {
        new cloudwatch.Alarm(this, `RDSCPUUtilization-${cdkResrouceName}`, {
            metric: rdsPostgres.metricCPUUtilization(),
            threshold: 80,
            evaluationPeriods: 1,
            alarmName: `CPUUtilization-${cdkResrouceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} CPU utilization exceeded 90%`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Alarm threshold is configured using prod cluster metrics
        // With our current instance size, 50K write IOPS over 5 mins results in
        // 60% CPU usage
        new cloudwatch.Alarm(this, `RDSVolumeReadIOPs-${cdkResrouceName}`, {
            metric: rdsPostgres.metricVolumeReadIOPs(),
            threshold: 50000,
            evaluationPeriods: 2,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            alarmName: `RDSVolumeReadIOPs-${cdkResrouceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} volume read IOPS exceeded 1000`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Alarm threshold is configured using prod cluster metrics
        // With our current instance size, 132K write IOPS over 5 mins results in
        // 85% CPU usage
        new cloudwatch.Alarm(this, `RDSVolumeWriteIOPs-${cdkResrouceName}`, {
            metric: rdsPostgres.metricVolumeWriteIOPs(),
            threshold: 2400000,
            evaluationPeriods: 2,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            alarmName: `RDSVolumeWriteIOPs-${cdkResrouceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} volume write IOPS exceeded 1800`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        new cloudwatch.Alarm(this, `RDSFreeLocalStorage-${cdkResrouceName}`, {
            metric: rdsPostgres.metricFreeLocalStorage(),
            threshold: 2147483648, // => 2GB
            comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
            evaluationPeriods: 1,
            alarmName: `RDSFreeLocalStorage-${cdkResrouceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} free local storage below 20000 bytes`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        new cloudwatch.Alarm(this, `RDSFreeableMemory-${cdkResrouceName}`, {
            metric: rdsPostgres.metricFreeableMemory(),
            threshold: 1073741824, // => 1GB
            comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
            evaluationPeriods: 1,
            alarmName: `RDSFreeableMemory-${cdkResrouceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} freeable memory below 1GB`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }

    private createCNAMERecords(buildConfig: BuildConfig, writerEndpoint: string, readerEndpoint: string) {
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'MyZone', {
            zoneName: buildConfig.dns.route53HostedZoneName,
            hostedZoneId: buildConfig.dns.route53HostedZoneID,
        });

        const region = buildConfig.awsEnvAccount.targetRegion;
        new route53.CnameRecord(this, `cname-primary-rds-record-writer-endpoint`, {
            domainName: writerEndpoint,
            zone: hostedZone,
            // Need to verify if this shows up in DNS lookups before setting it
            //comment: `cname record pointing to ${record.domainName}`,
            recordName: `${buildConfig.rdsPostgres.endpointCNAMEPrefix}-${region}-w`,
            ttl: Duration.minutes(1),
        });
        new route53.CnameRecord(this, `cname-primary-rds-record-reader-endpoint`, {
            domainName: readerEndpoint,
            zone: hostedZone,
            // Need to verify if this shows up in DNS lookups before setting it
            //comment: `cname record pointing to ${record.domainName}`,
            recordName: `${buildConfig.rdsPostgres.endpointCNAMEPrefix}-${region}-r`,
            ttl: Duration.minutes(1),
        });
    }
}
