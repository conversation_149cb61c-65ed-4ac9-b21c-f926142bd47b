import { Stack, StackProps, Duration, RemovalPolicy, Size, Tags } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { AuroraServerless, AuroraServerlessDatabase } from '../common/rds/serverless-config';
import { NetworkStack } from '../vpc/network-stack';
import { S3BucketConstruct } from '../common/s3-bucket-construct';
import { EnumHelpers } from '../common/utils/enum-helpers';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as path from 'path';
import { BucketEncryption } from 'aws-cdk-lib/aws-s3';

interface AuroraServerlessStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    auroraServerless: AuroraServerless;
    dns: {
        route53HostedZoneName: string;
        route53HostedZoneID: string;
    };
}

export interface AuroraServerlessStackProps extends StackProps {
    buildConfig: AuroraServerlessStackConfig;
    networkStack: NetworkStack;
}

export class AuroraServerlessStack extends Stack {
    readonly clusters = new Map<string, rds.DatabaseCluster>();
    readonly securityGroup: ec2.SecurityGroup;
    readonly backupBucket?: s3.IBucket;
    readonly snsTopic?: sns.Topic;
    readonly backupRole?: iam.Role;

    constructor(scope: Construct, id: string, props: AuroraServerlessStackProps) {
        super(scope, id, props);
        this.addDependency(props.networkStack);

        const { buildConfig, networkStack } = props;
        const { auroraServerless } = buildConfig;

        if (buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping Aurora Serverless stack creation - cold site.');
            return;
        }

        if (!auroraServerless || !auroraServerless.databases || auroraServerless.databases.length === 0) {
            console.log('No Aurora Serverless databases configured - creating empty stack');
            return;
        }

        // Create shared resources
        this.securityGroup = this.createSecurityGroup(networkStack, auroraServerless);

        if (!auroraServerless.disableS3Backup) {
            this.backupBucket = this.createOrGetBackupBucket(buildConfig);
            this.backupRole = this.createBackupRole(buildConfig);
        }

        if (auroraServerless.enableSNSNotifications) {
            this.snsTopic = this.createSNSTopic(buildConfig);
            this.createEventSubscription();
        }

        // Create database clusters
        this.createDatabaseClusters(buildConfig, networkStack);
    }

    private createSecurityGroup(networkStack: NetworkStack, config: AuroraServerless): ec2.SecurityGroup {
        const sg = new ec2.SecurityGroup(this, 'AuroraServerlessSG', {
            vpc: networkStack.coreVpc,
            securityGroupName: 'aurora-serverless-sg',
            description: 'Security group for Aurora Serverless v2 clusters',
        });

        // Add ingress rules for additional CIDRs
        config?.additionalAllowedCIDRs.forEach((cidr) => {
            sg.addIngressRule(ec2.Peer.ipv4(cidr), ec2.Port.tcp(5432), `Allow inbound from ${cidr}`);
        });

        return sg;
    }

    private createOrGetBackupBucket(buildConfig: AuroraServerlessStackConfig): s3.IBucket {
        if (buildConfig.auroraServerless.useExistingS3BackupBucket) {
            const bucketName = `aurora-serverless-backup-${buildConfig.cdkAppInfo.environment}-${buildConfig.awsEnvAccount.targetRegion}`;
            return s3.Bucket.fromBucketName(this, 'ExistingBackupBucket', bucketName);
        }

        return this.createBackupS3Bucket(buildConfig).s3Bucket;
    }

    private createBackupS3Bucket(buildConfig: AuroraServerlessStackConfig): S3BucketConstruct {
        return new S3BucketConstruct(this, 'aurora-serverless-backup', {
            id: 'aurora-serverless-backups',
            awsEnvAccount: buildConfig.awsEnvAccount,
            cdkAppInfo: buildConfig.cdkAppInfo,
            addAccessLogPolicy: false,
            name: 'aurora-serverless-backup',
            enableVersioning: true,
            bucketKeyEnabled: true,
            bucketEncryption: EnumHelpers.findEnumType(BucketEncryption, 'KMS'),
            publicReadAccess: false,
            transferAcceleration: false,
            removalPolicy: RemovalPolicy.RETAIN,
            enableUserdataRetentionRule: false,
            enableBackupRetentionPolicyRule: true,
            autoDeleteObjects: false,
            replicationTargetRegion: buildConfig.auroraServerless.s3BackupReplicationTargetRegion,
            replicationTargetKMSKeyArn: buildConfig.auroraServerless.s3BackupReplicationTargetKMSKeyArn,
        });
    }

    private createBackupRole(buildConfig: AuroraServerlessStackConfig): iam.Role {
        const role = new iam.Role(this, 'AuroraServerlessBackupRole', {
            roleName: `AuroraServerlessBackup-${buildConfig.awsEnvAccount.targetRegion}`,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('lambda.amazonaws.com'),
                new iam.ServicePrincipal('export.rds.amazonaws.com')
            ),
            description: 'Role for Aurora Serverless backup operations',
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
        });

        // Add backup permissions
        role.addToPolicy(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'rds:DescribeDBSnapshots',
                    'rds:DescribeDBClusterSnapshots',
                    'rds:DescribeDBClusterSnapshotAttributes',
                    'rds:DescribeExportTasks',
                    'rds:CopyDBClusterSnapshot',
                    'rds:DeleteDBClusterSnapshot',
                    'rds:StartExportTask',
                ],
                resources: ['*'],
            })
        );

        if (this.backupBucket) {
            role.addToPolicy(
                new iam.PolicyStatement({
                    effect: iam.Effect.ALLOW,
                    actions: [
                        's3:HeadObject',
                        's3:PutObject',
                        's3:GetObject',
                        's3:ListBucket',
                        's3:DeleteObject',
                        's3:PutObjectAcl',
                    ],
                    resources: [this.backupBucket.bucketArn, `${this.backupBucket.bucketArn}/*`],
                })
            );

            // KMS permissions for backup encryption
            if (this.backupBucket.encryptionKey) {
                role.addToPolicy(
                    new iam.PolicyStatement({
                        effect: iam.Effect.ALLOW,
                        actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:GenerateDataKey', 'kms:DescribeKey'],
                        resources: [this.backupBucket.encryptionKey.keyArn],
                    })
                );
            }
        }

        return role;
    }

    private createSNSTopic(buildConfig: AuroraServerlessStackConfig): sns.Topic {
        const topic = new sns.Topic(this, 'AuroraServerlessNotifications', {
            displayName: 'Aurora Serverless v2 Notifications',
        });

        topic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));

        return topic;
    }

    private createEventSubscription(): void {
        if (!this.snsTopic) return;

        new rds.CfnEventSubscription(this, 'AuroraServerlessEvents', {
            snsTopicArn: this.snsTopic.topicArn,
            enabled: true,
            subscriptionName: 'AuroraServerlessEvents',
            sourceType: 'db-cluster',
        });
    }

    private createDatabaseClusters(buildConfig: AuroraServerlessStackConfig, networkStack: NetworkStack): void {
        const { auroraServerless } = buildConfig;

        auroraServerless?.databases.forEach((dbConfig) => {
            const cluster = this.createCluster(buildConfig, networkStack, dbConfig);
            this.clusters.set(dbConfig.cdkResourceName, cluster);

            // Cluster tagging
            if (dbConfig.tags) {
                for (const [k, v] of Object.entries(dbConfig.tags)) {
                    if (k && v) {
                        Tags.of(cluster).add(k, v, {
                            includeResourceTypes: ['AWS::RDS::DBCluster'],
                        });
                    }
                }
            }

            // Create alarms if enabled
            if (auroraServerless.enableCloudWatchAlarms) {
                this.createClusterAlarms(buildConfig, cluster, dbConfig);
            }

            // Setup backup if enabled
            if (auroraServerless && !auroraServerless.disableS3Backup && dbConfig.enableBackup) {
                this.setupBackupJob(buildConfig, dbConfig, cluster);
            }

            // Create DNS records for primary cluster
            if (dbConfig.isPrimary) {
                this.createDNSRecords(buildConfig, cluster);
            }
        });
    }

    private createCluster(
        buildConfig: AuroraServerlessStackConfig,
        networkStack: NetworkStack,
        dbConfig: AuroraServerlessDatabase
    ): rds.DatabaseCluster {
        const monitoringInterval =
            dbConfig.enhancedMonitoringInterval > 0 ? Duration.seconds(dbConfig.enhancedMonitoringInterval) : undefined;

        const clusterProps = {
            engine: rds.DatabaseClusterEngine.auroraPostgres({
                version: rds.AuroraPostgresEngineVersion.of(
                    dbConfig.auroraPostgresFullVersion,
                    dbConfig.auroraPostgresMajorVersion
                ),
            }),
            serverlessV2MinCapacity: dbConfig.minCapacity,
            serverlessV2MaxCapacity: dbConfig.maxCapacity,
            parameters: this.getDatabaseParameters(dbConfig),
            defaultDatabaseName: 'maindb',
            deletionProtection: dbConfig.deletionProtection,
            iamAuthentication: true,
            monitoringInterval,
            writer: rds.ClusterInstance.serverlessV2('writer', {
                allowMajorVersionUpgrade: dbConfig.allowMajorVersionUpgrade,
                autoMinorVersionUpgrade: dbConfig.autoMinorVersionUpgrade,
                enablePerformanceInsights: dbConfig.enablePerformanceInsights,
                performanceInsightRetention: dbConfig.enablePerformanceInsights
                    ? dbConfig.performanceInsightRetention
                    : undefined,
                publiclyAccessible: false,
            }),
            backup: {
                retention: Duration.days(buildConfig.auroraServerless.backupRetentionDays),
            },
            vpc: networkStack.coreVpc,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
            },
            securityGroups: [this.securityGroup],
            storageEncrypted: true,
            removalPolicy: dbConfig.removalPolicy,
        };

        let cluster: rds.DatabaseCluster;
        if (dbConfig.snapshotIdentifier) {
            cluster = new rds.DatabaseClusterFromSnapshot(this, dbConfig.cdkResourceName, {
                ...clusterProps,
                snapshotIdentifier: dbConfig.snapshotIdentifier,
                snapshotCredentials: rds.SnapshotCredentials.fromGeneratedSecret(dbConfig.dbUsername),
            });
        } else {
            cluster = new rds.DatabaseCluster(this, dbConfig.cdkResourceName, {
                ...clusterProps,
                credentials: rds.Credentials.fromGeneratedSecret(dbConfig.dbUsername),
            });
        }

        return cluster;
    }

    private getDatabaseParameters(dbConfig: AuroraServerlessDatabase): { [key: string]: string } {
        const parameters: { [key: string]: string } = {
            log_connections: 'on',
            log_disconnections: 'on',
            log_lock_waits: 'on',
            log_min_duration_statement: '0',
            log_statement: 'none',
        };

        if (dbConfig.transactionTimeoutSeconds) {
            parameters['idle_in_transaction_session_timeout'] = Duration.seconds(dbConfig.transactionTimeoutSeconds)
                .toMilliseconds()
                .toString();
        }

        if (dbConfig.statementTimeoutSeconds) {
            parameters['statement_timeout'] = Duration.seconds(dbConfig.statementTimeoutSeconds)
                .toMilliseconds()
                .toString();
        }

        if (dbConfig.workMemMB) {
            parameters['work_mem'] = `${Size.mebibytes(dbConfig.workMemMB).toKibibytes()}`;
        }

        return parameters;
    }

    private createClusterAlarms(
        buildConfig: AuroraServerlessStackConfig,
        cluster: rds.DatabaseCluster,
        dbConfig: AuroraServerlessDatabase
    ): void {
        if (!this.snsTopic) return;

        // CPU Utilization Alarm
        new cloudwatch.Alarm(this, `CPUUtilization-${dbConfig.cdkResourceName}`, {
            metric: cluster.metricCPUUtilization(),
            threshold: 80,
            evaluationPeriods: 1,
            alarmName: `AuroraServerless-CPU-${dbConfig.cdkResourceName}`,
            alarmDescription: `CPU utilization exceeded 80% for ${dbConfig.cdkResourceName}`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // WriteIOPs Alarm
        new cloudwatch.Alarm(this, `AuroraRDSVolumeWriteIOPs-${dbConfig.cdkResourceName}`, {
            metric: cluster.metricVolumeWriteIOPs(),
            threshold: 2400000,
            evaluationPeriods: 1,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            alarmName: `AuroraRDSVolumeWriteIOPs-${dbConfig.cdkResourceName}`,
            alarmDescription: `Aurora RDS Postgres in ${buildConfig.cdkAppInfo.environment} volume write IOPS exceeded 1800`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // ReadIOPs Alarm
        new cloudwatch.Alarm(this, `AuroraRDSVolumeReadIOPs-${dbConfig.cdkResourceName}`, {
            metric: new cloudwatch.Metric({
                namespace: 'AWS/RDS',
                metricName: 'ReadIOPS',
                dimensionsMap: {
                    DBClusterIdentifier: cluster.clusterIdentifier,
                },
                statistic: 'Average',
                period: Duration.minutes(1),
            }),
            threshold: 50000,
            evaluationPeriods: 1,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            alarmName: `AuroraRDSVolumeReadIOPs-${dbConfig.cdkResourceName}`,
            alarmDescription: `Aurora RDS Postgres in ${buildConfig.cdkAppInfo.environment} volume read IOPS exceeded 1800`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        new cloudwatch.Alarm(this, `RDSFreeableMemory-${dbConfig.cdkResourceName}`, {
            metric: cluster.metricFreeableMemory(),
            threshold: 200000000, // < 200MB
            comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
            evaluationPeriods: 1,
            alarmName: `AuroraRDSFreeableMemory-${dbConfig.cdkResourceName}`,
            alarmDescription: `AuroraRDS Postgres in ${buildConfig.cdkAppInfo.environment} freeable memory below 1GB`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        new cloudwatch.Alarm(this, `AuroraRDSFreeLocalStorage-${dbConfig.cdkResourceName}`, {
            metric: cluster.metricFreeLocalStorage(),
            threshold: 2147483648, // => 2GB
            comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
            evaluationPeriods: 1,
            alarmName: `AuroraRDSFreeLocalStorage-${dbConfig.cdkResourceName}`,
            alarmDescription: `RDS Postgres in ${buildConfig.cdkAppInfo.environment} free local storage below 20000 bytes`,
            actionsEnabled: true,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Local storage Alarm
        new cloudwatch.Alarm(this, `AuroraTempStorageThroughput-new-${dbConfig.cdkResourceName}`, {
            metric: new cloudwatch.Metric({
                namespace: 'AWS/RDS',
                metricName: 'TempStorageThroughput',
                dimensionsMap: {
                    DBClusterIdentifier: cluster.clusterIdentifier,
                },
                statistic: 'Average',
                period: Duration.minutes(1),
            }),
            threshold: 209715200, // => 200MB
            evaluationPeriods: 3,
            alarmName: `AuroraTempStorageThroughput-new-${dbConfig.cdkResourceName}`,
            alarmDescription: `Aurora Postgres in ${dbConfig.cdkResourceName} free local storage below 204800 bytes`,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
            actionsEnabled: true,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Database connections alarm
        new cloudwatch.Alarm(this, `DatabaseConnections-${dbConfig.cdkResourceName}`, {
            metric: cluster.metricDatabaseConnections({
                period: Duration.minutes(5),
            }),
            threshold: 80,
            evaluationPeriods: 2,
            alarmName: `AuroraServerless-Connections-${dbConfig.cdkResourceName}`,
            alarmDescription: `Database connections exceeded 80 for ${dbConfig.cdkResourceName}`,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Serverless v2 capacity alarm
        new cloudwatch.Alarm(this, `ServerlessCapacity-${dbConfig.cdkResourceName}`, {
            metric: new cloudwatch.Metric({
                namespace: 'AWS/RDS',
                metricName: 'ServerlessDatabaseCapacity',
                dimensionsMap: {
                    DBClusterIdentifier: cluster.clusterIdentifier,
                },
                period: Duration.minutes(5),
                statistic: 'Maximum',
            }),
            threshold: dbConfig.maxCapacity * 0.8,
            evaluationPeriods: 3,
            alarmName: `AuroraServerless-Capacity-${dbConfig.cdkResourceName}`,
            alarmDescription: `Serverless capacity approaching maximum for ${dbConfig.cdkResourceName}`,
            treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
        }).addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }

    private setupBackupJob(
        buildConfig: AuroraServerlessStackConfig,
        dbConfig: AuroraServerlessDatabase,
        cluster: rds.DatabaseCluster
    ): void {
        if (!this.backupBucket || !this.backupRole) return;

        const backupLambda = new lambda.Function(this, `BackupLambda-${dbConfig.cdkResourceName}`, {
            functionName: `AuroraServerlessBackup-${buildConfig.awsEnvAccount.targetRegion}-${dbConfig.cdkResourceName}`,
            runtime: lambda.Runtime.PYTHON_3_11,
            handler: 'main.lambda_handler',
            code: lambda.Code.fromAsset(path.join(__dirname, '../../assets/lambda/aurora-serverless-backup')),
            timeout: Duration.minutes(15),
            memorySize: 256,
            role: this.backupRole,
            environment: {
                CLUSTER_IDENTIFIER: cluster.clusterIdentifier,
                BUCKET_NAME: this.backupBucket.bucketName,
                REGION: buildConfig.awsEnvAccount.targetRegion,
                ACCOUNT_ID: buildConfig.awsEnvAccount.awsAccountID,
            },
        });

        // Schedule backup job
        const backupRule = new events.Rule(this, `BackupRule-${dbConfig.cdkResourceName}`, {
            schedule: events.Schedule.cron({ minute: '0', hour: '2' }), // 2 AM daily
            description: `Daily backup for ${dbConfig.cdkResourceName}`,
        });

        backupRule.addTarget(new targets.LambdaFunction(backupLambda));
    }

    private createDNSRecords(buildConfig: AuroraServerlessStackConfig, cluster: rds.DatabaseCluster): void {
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'HostedZone', {
            zoneName: buildConfig.dns.route53HostedZoneName,
            hostedZoneId: buildConfig.dns.route53HostedZoneID,
        });

        const region = buildConfig.awsEnvAccount.targetRegion;
        const prefix = buildConfig.auroraServerless.endpointCNAMEPrefix;

        // Writer endpoint
        new route53.CnameRecord(this, 'WriterEndpointCNAME', {
            zone: hostedZone,
            recordName: `${prefix}-${region}-w`,
            domainName: cluster.clusterEndpoint.hostname,
            ttl: Duration.minutes(1),
        });

        // Reader endpoint
        new route53.CnameRecord(this, 'ReaderEndpointCNAME', {
            zone: hostedZone,
            recordName: `${prefix}-${region}-r`,
            domainName: cluster.clusterReadEndpoint.hostname,
            ttl: Duration.minutes(1),
        });
    }
}
