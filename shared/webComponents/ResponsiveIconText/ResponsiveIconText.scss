@use '../styles/layout' as *;

.responsive_icon_text {
    display: inline-flex;
    container-type: inline-size;
    min-width: 0;

    .responsive_icon_text__container {
        display: inline-grid;
        grid-template-columns: auto 1fr;
        align-items: center;
        gap: $spacer-8;
        transition: grid-template-columns 100ms ease-in-out;
        width: 100%;
    }

    .responsive_icon_text__content {
        white-space: nowrap;
        text-align: start;
        transition: opacity 100ms ease-in-out;
        // Delay text appearing when expanding
        transition-delay: 0ms, 0ms; // Default no delay for collapsing
    }

    .responsive_icon_text__icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
    }

    // When container is large enough, add delay for text appearing
    @container (min-width: 61px) {
        .responsive_icon_text__content {
            transition-delay: 100ms, 100ms; // Delay text appearing when expanding
        }
    }

    // Collapse when container is too small (based on icon size + minimal padding)
    @container (max-width: 60px) {
        .responsive_icon_text__container {
            grid-template-columns: auto 0;
        }
        .responsive_icon_text__content {
            opacity: 0;
            transition-delay: 0ms, 0ms; // No delay when collapsing
        }
    }

    @container (max-width: 50px) {
        .responsive_icon_text__content {
            opacity: 0;
        }
    }
}
