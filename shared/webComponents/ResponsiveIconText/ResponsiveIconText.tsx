import classNames from 'classnames';
import React, { CSSProperties, forwardRef, HTMLAttributes, ReactNode, Ref } from 'react';

import {
    Icon,
    IconCustomSize,
    IconSrc,
    isCustomProportionalSize,
    isCustomSize,
    Props as IconProps,
} from '../Icon/Icon';

import './ResponsiveIconText.scss';

export interface ResponsiveIconTextProps extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
    icon: IconSrc | React.ReactNode;
    iconSize?: IconCustomSize;
    iconProps?: Omit<IconProps, 'icon'>;
    children: ReactNode;
}

const ResponsiveIconTextBase = (props: ResponsiveIconTextProps, ref: Ref<HTMLDivElement>) => {
    const { icon, iconProps, iconSize, children, className, ...remaining } = props;

    const classes = classNames('responsive_icon_text', className);

    const iconElement = React.isValidElement(icon) ? (
        icon
    ) : (
        <Icon icon={icon as IconSrc} size={iconSize} {...iconProps} />
    );

    let baseStyle: CSSProperties | undefined = undefined;

    if (iconSize && isCustomProportionalSize(iconSize)) {
        baseStyle = { minWidth: `${iconSize}px`, minHeight: `${iconSize}px` };
    } else if (iconSize && isCustomSize(iconSize)) {
        const width = typeof iconSize.width === 'number' ? `${iconSize.width}px` : iconSize.width;
        const height = typeof iconSize.height === 'number' ? `${iconSize.height}px` : iconSize.height;
        baseStyle = { minWidth: width, minHeight: height };
    }

    return (
        <div ref={ref} className={classes} {...remaining} style={baseStyle}>
            <div className="responsive_icon_text__container">
                <span className="responsive_icon_text__icon">{iconElement}</span>
                <span className="responsive_icon_text__content">{children}</span>
            </div>
        </div>
    );
};

export const ResponsiveIconText = forwardRef(ResponsiveIconTextBase);
