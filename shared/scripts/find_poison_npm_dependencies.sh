# Checks for unsafe/poisoned NPM dependencies.

# For more information:
# https://github.com/chalk/chalk/issues/656#issuecomment-3266880534

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PATTERN="_0x112fa8"

# List the folders relative to the script here
FOLDERS=(
  "$SCRIPT_DIR/../../node_modules"
  "$SCRIPT_DIR/../../desktop/node_modules"
)

if grep -r -- "$PATTERN" "${FOLDERS[@]}"; then
    echo "❌ Poison dependencies found"
    exit 1
else
    echo "✅ Dependencies OK"
    exit 0
fi