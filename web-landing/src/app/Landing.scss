@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use './landing-mixin' as *;
@use './landing-colors' as *;

@import '@shared/webComponents/styles/reset';
@import '@web/styles/base/typography';

html,
body,
.landing {
    height: 100%;
    color: $landing-dark-purple-100;
}

:root {
    @include inject-theme-colors;
}

body {
    overflow: auto;
}

.landing {
    background: $landing-bg;
    background-size: 100%;

    .landing__hero {
        h1 {
            @include hero-header;
        }

        p {
            @include landing-description;
        }
    }

    .landing__description {
        font-size: 18px;
        line-height: 24px;
        letter-spacing: -0.6px;

        @include breakpoint(md) {
            font-size: 26px;
            line-height: 32px;
            letter-spacing: -0.377px;
        }
    }

    h1 {
        font-size: 32px;
        line-height: 36px;
        font-weight: 600;
        margin-bottom: 12px;
        letter-spacing: -0.07rem;
        @include landing-header-gradient;

        @include breakpoint(md) {
            font-size: 56px;
            line-height: 70px;
            letter-spacing: -0.145rem;
        }
    }

    h2 {
        font-size: 28px;
        font-weight: $font-weight-bold;
        line-height: 32px;
        margin-bottom: 16px;
        letter-spacing: -0.966px;

        @include breakpoint(md) {
            font-size: 40px;
            font-weight: 600;
            line-height: 48px;
            letter-spacing: -1.38px;
        }
    }

    h3 {
        font-size: 26px;
        font-weight: $font-weight-bold;
        line-height: 34px;
        letter-spacing: -0.377px;
        margin-bottom: 16px;

        @include breakpoint(md) {
            font-size: 28px;
            font-weight: $font-weight-bold;
            line-height: 34px;
            letter-spacing: -0.0245em;
        }
    }

    h4 {
        font-weight: 600;
        font-size: 21px;
        line-height: 24px;
        letter-spacing: -0.305px;
        margin-bottom: 16px;

        @include breakpoint(md) {
            font-size: 24px;
            line-height: 32px;
            letter-spacing: -0.348px;
        }
    }

    h5 {
        font-weight: 400;
        font-size: 19px;
        line-height: 24px;
    }

    h6 {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
    }

    p {
        font-weight: 300;
        color: themed($text-secondary);
        font-size: 16px;
        line-height: 24px;
        letter-spacing: -0.261px;

        @include breakpoint(md) {
            font-size: 18px;
            line-height: 24px;
        }

        @include breakpoint(xl) {
            font-size: 19px;
            line-height: 24px;
            letter-spacing: -0.276px;
        }
    }

    li {
        font-weight: 400;
        color: themed($text-secondary);
        font-size: 16px;
        line-height: 22px;
        letter-spacing: -0.232px;

        @include breakpoint(md) {
            font-size: 17px;
            line-height: 26px;
            font-weight: 300;
            letter-spacing: -0.015em;
        }

        @include breakpoint(lg) {
            font-size: 19px;
            line-height: 26px;
        }
    }

    .button {
        &.button__large {
            border-radius: $border-radius-8;
        }
    }

    .landing__container {
        @include flex-center-center;
        @include flex-column;
        @include landing-animation;

        row-gap: $size-24;
        max-width: $landing-max-content-width;
        padding: $landing-padding $size-24;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
    }

    .landing__title {
        @include landing-title;
        @include landing-header-gradient;
    }

    .landing__secondary_title {
        @include landing-title;
    }

    .landing__content {
        @include landing-content;

        & > p {
            @include landing-content;
        }
    }

    .landing__button_container {
        margin-top: $spacer-32;
    }

    .landing__button {
        @include landing-button;

        min-width: $landing-button-min-width;
    }

    .landing__spinner {
        &.spinner__border {
            border: $size-2 solid $astronaut;
            border-right-color: transparent;
            height: $size-12;
            width: $size-12;
        }
    }

    .landing__spacer {
        height: 0;
        width: 100%;
        border-top: $border-width $border-style $white-20;
    }

    .hero {
        @include flex-column-center;

        display: block;
        padding: 40px 0;
        position: relative;
        background: #d3cce3; /* fallback for old browsers */
        background: linear-gradient(to bottom right, #f7f6ff 36%, #f0dfff 100%);
        border-bottom: 1px solid $light-purple-10;

        h1 {
            background: linear-gradient(356deg, #362783, #7b6dc2);
            /* stylelint-disable-next-line property-no-vendor-prefix */
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        p {
            font-size: 18px;
            line-height: 24px;
            color: themed($text-secondary);
            letter-spacing: -0.015em;
            max-width: 737px;
            margin: 0 auto 16px;

            @include breakpoint(md) {
                font-size: 20px;
                line-height: 27px;
            }

            @include breakpoint(lg) {
                font-size: 22px;
                line-height: 30px;
            }
        }

        .hero__buttons {
            display: flex;
            justify-content: center;

            gap: $spacer-16;
            margin: 28px auto 32px;
        }

        // The hero button has specific overrides to match the larger font sizes of the text in this section
        .hero__button {
            @include flex-column;

            flex: 1;
            gap: $spacer-2;
            padding: $spacer-12 $spacer-8;
            width: 170px;

            @include breakpoint(sm) {
                flex: none;
                width: 170px;
            }

            &.button__outline {
                &:hover {
                    background-color: $white-70;
                }
            }
        }

        .hero__button__header {
            @include hero-button-header;

            font-weight: $font-weight-semibold;
        }

        .hero__button__subheader {
            @include hero-button-subheader;

            opacity: 0.7;
        }

        .section__text {
            max-width: 910px;
            text-align: center;
            margin: 0 auto;
            padding: 0 16px;
        }
    }

    .divider {
        height: 1px;
        background: linear-gradient(
            90deg,
            rgb(256 246 255 / 100%) 0%,
            rgb(222 229 237 / 100%) 50%,
            rgb(256 246 255 / 100%) 100%
        );
        border: none;
        margin: 0;
    }
}

// styles from https://github.com/NextChapterSoftware/NextChapterSoftware.github.io/blob/main/index.html
.landing__index {
    height: 100%;
    width: 100%;
    background-color: black;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);

    .landing__index__content {
        max-width: 800px;
        font-family: Helvetica, Arial, sans-serif;
        color: #f3e8ee;
        text-align: center;
        margin: 0 auto;
        padding: 0 24px;

        & > img {
            margin: 32px auto;
            width: 100%;
        }

        & > h3 {
            font-size: 0.6em;
            letter-spacing: 1px;
        }
    }
}
