@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'media' as *;
@use 'theme' as *;
@use 'colors' as *;
@use 'animations' as *;

$home-bg: #faf6ff;
$landing-bg: #f6f4ff;
$landing-title-size: 50px;
$landing-weight: 700;
$landing-padding: 60px;
$landing-max-width: 700px;
$landing-md-content-width: 1280px;
$landing-max-content-width: 1440px;
$landing-button-min-width: 160px;
$landing-container-max-width: 1024px;
$landing-container-wide-width: 1300px;

@mixin landing-title($font-size: $landing-title-size, $mobile-font-size: $size-32, $color: $dark-purple-100) {
    font-size: $font-size;
    line-height: $lh-condensed;
    font-weight: $landing-weight;
    letter-spacing: -1px;
    color: $color;

    @media (max-width: $mobile) {
        font-size: $mobile-font-size;
    }
}

@mixin landing-content($font-size: $size-22, $mobile-font-size: $size-20, $color: $dark-gray-100) {
    color: $color;
    font-size: $font-size;
    line-height: $lh-expanded;

    b {
        color: $color;
    }

    @media (max-width: $mobile) {
        font-size: $mobile-font-size;
    }
}

@mixin landing-button {
    @include shadow-border-medium;

    box-sizing: content-box;
    border-radius: $border-width-4;
    font-size: $font-size-16;
    font-weight: $font-weight-semibold;
    padding: $size-16;
}

@mixin landing-animation {
    animation: fade-up 0.5s ease-in;
}

@mixin landing-header-gradient($gradient: 356deg) {
    background: linear-gradient($gradient, #362783, #7b6dc2);
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

@mixin landing-secondary-background-gradient($position: top, $opacity: 0.15) {
    background: radial-gradient(circle at $position, rgba(171, 0, 255, $opacity), #f9f2fe);
}

@mixin landing-tertiary-background-gradient() {
    background: linear-gradient(to left, #f9f4f6, #fbebd7, #f9f4f6);
}

@mixin hero-button-header {
    font-size: 16px;
    letter-spacing: -0.015em;

    @include breakpoint(md) {
        font-size: 19px;
    }
}

@mixin hero-button-subheader {
    font-size: 14px;
    letter-spacing: -0.015em;
    display: none;
    font-weight: $font-weight-normal;

    @include breakpoint(sm) {
        display: block;
    }

    @include breakpoint(lg) {
        font-size: 16px;
    }
}

@mixin hero-button-styling {
    font-size: 16px;
    letter-spacing: -0.015em;

    // @include breakpoint(md) {
    //     font-size: 20px;
    // }

    @include breakpoint(lg) {
        font-size: 19px;
    }
}

@mixin landing-box-shadow($color) {
    box-shadow: 0 2px 18px -2px $color;
}

@keyframes button-enable {
    from {
        color: #aea4ec;
    }
    to {
        color: $indigo-100;
    }
}

@mixin hero-header {
    font-size: 32px;
    line-height: 36px;
    letter-spacing: -0.04em;
    font-weight: 600;
    color: $dark-purple-100;

    @include breakpoint(md) {
        font-size: 56px;
        line-height: 58px;
        letter-spacing: -0.145rem;
    }
}

@mixin hero-description {
    font-size: 20px;
    line-height: 26px;
    letter-spacing: -0.6px;
    font-weight: $font-weight-light;
    color: $dark-gray-100;

    @include breakpoint(md) {
        font-size: 36px;
        line-height: 44px;
        letter-spacing: -1.08px;
    }
}

@mixin landing-description {
    font-size: 20px;
    line-height: 26px;
    letter-spacing: -0.6px;
    font-weight: $font-weight-light;
    color: $dark-gray-100;

    @include breakpoint(md) {
        font-size: 26px;
        line-height: 32px;
        letter-spacing: -0.377px;
    }
}

@mixin homepage-cta {
    width: 205px;
    padding: $spacer-14 $spacer-16;
    font-size: $font-size-19;
    white-space: nowrap;
}

@mixin section-header {
    font-size: 30px;
    line-height: 34px;
    letter-spacing: -1.2px;
    @include breakpoint(md) {
        font-size: 52px;
        line-height: 56px;
        letter-spacing: -2.08px;
    }
}

@mixin section-description {
    font-size: $font-size-18;
    line-height: $font-size-24;
    letter-spacing: -0.6px;

    @include breakpoint(md) {
        font-size: 26px;
        line-height: 32px;
        letter-spacing: -0.377px;
    }
}

@mixin purple-radial-gradient($opacity: 0.4) {
    content: '';
    position: absolute;
    border-radius: $border-radius-circle;
    width: 100%;
    aspect-ratio: 1/1;
    background: radial-gradient(
        49.95% 49.95% at 48.82% 50.05%,
        rgba(173 0 255 / 26%) 0%,
        rgba(173 0 255 / 7%) 66.35%,
        rgba(173 0 255 / 0%) 100%
    );
    opacity: $opacity;
}

@mixin radial-bottom-border {
    content: '';
    position: absolute;
    height: 1.5px;
    width: 50%;
    left: calc(50% - 25%);
    bottom: 0;
    background: rgb(171 0 255 / 20%);
    mask-image: linear-gradient(to right, transparent, black 25%, black 75%, transparent);
}
