@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;
@use 'animations' as *;
@use '../../landing-mixin' as *;
@import '@web/styles/button-mixin';

.landing_nav_outlet {
    background-image: none;
}

.home_container {
    @include flex-column;

    overflow: hidden;
    .hero__banner_image {
        width: 100%;
        height: auto;
        max-height: 500px;
        object-fit: contain;
        aspect-ratio: 684/578;
    }
}

.home_hero {
    @include flex-column-center;
    padding: $spacer-64 $spacer-15 $spacer-0;
    background: radial-gradient(77.23% 50% at 50% 100%, rgba(173 0 255 / 16%) 0%, rgba(173 0 255 / 2%) 100%), #f3ecfb;
    mask-image: linear-gradient(black 90%, transparent);

    @include breakpoint(md) {
        padding: $spacer-80 $spacer-0 $spacer-0;
    }
    .content_wrapper {
        max-width: 1000px;
        text-align: center;
        padding: 0;
        @include breakpoint(md) {
            padding: 0 92px;
        }
        h1 {
            color: $dark-purple-100;
            font-size: $font-size-32;
            line-height: $line-height-36;
            letter-spacing: -1.28px;
            margin-bottom: $spacer-8;

            @include breakpoint(md) {
                font-size: $font-size-64;
                line-height: 64px;
                letter-spacing: -2.56px;
                margin-bottom: $spacer-20;
            }
        }
        p {
            @include landing-description;
            margin-bottom: $spacer-36;
        }
    }
}

.primary_cta_wrapper {
    display: flex;
    gap: $spacer-24;
    justify-content: center;
    align-items: center;
    flex-direction: column-reverse;
    @include breakpoint(md) {
        flex-direction: row;
    }
    button {
        @include homepage-cta;
    }
}

.feature_section_wrapper {
    background: linear-gradient(180deg, rgba(255 255 255 / 0%) 86.49%, rgba(173 0 255 / 6%) 99.98%);
    position: relative;
    &:before {
        @include purple-radial-gradient;
        display: none;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 1200px;

        @include breakpoint(lg) {
            display: block;
        }
    }

    .home_section__content {
        gap: $spacer-48;

        @include breakpoint(md) {
            gap: $spacer-0;
        }

        @include breakpoint(lg) {
            gap: $spacer-32;
        }

        @include breakpoint(xl) {
            gap: $spacer-64;
        }
    }

    .home_section__header {
        h2 {
            @include section-header;
        }
        p {
            @include section-description;
        }
    }

    #integration-section-0 {
        .main_feature_section_content_video {
            aspect-ratio: 607 / 436;
        }
    }
}

.use_case_section_wrapper {
    background: linear-gradient(0deg, rgba(253 250 255 / 0%) 45.07%, rgba(173 0 255 / 6%) 94.78%);
    position: relative;
    &:after {
        @include radial-bottom-border;
    }

    .use_case_block_container {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: $spacer-16;
    }
}

.security_section_wrapper {
    &:after {
        @include radial-bottom-border;
    }

    .home_section__content {
        position: relative;
        &:before {
            @include purple-radial-gradient(0.6);
            width: 768px;
            top: 50%;
            left: 100%;
            transform: translate(-50%, -50%);
            @include breakpoint(md) {
                left: 50%;
            }
            @include breakpoint(lg) {
                width: 1012px;
                left: 0;
            }
        }
    }
    .security_section {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: $spacer-32;
        padding: $spacer-0;
        h2 {
            @include section-header;
        }

        p {
            @include section-description;
        }
        @include breakpoint(md) {
            padding: 0 96px;
        }
        @include breakpoint(lg) {
            flex-direction: row;
            text-align: left;
            gap: $spacer-64;

            > div {
                width: 50%;
            }

            .security_section_image {
                text-align: center;
            }
        }

        .security_section_content {
            @include flex-column-center;
            gap: 8px;
            @include breakpoint(md) {
                gap: 16px;
            }
            @include breakpoint(lg) {
                gap: 24px;
                align-items: start;
            }
        }

        .security_section_image {
            grid-column: 6 / span 6;
            grid-row: 1;
            img {
                max-width: 100%;
            }
        }

        .trust_center_link {
            padding: $spacer-16 $spacer-13;
            font-size: $font-size-19;
            display: inline-block;
            text-decoration: none;
            text-align: center;
            width: fit-content;
            transition: background-color 0.06s ease-in;
            border-radius: $border-radius-8;
            @include tertiary-button;
        }
    }
}

.what_is_unblocked_section {
    padding: $spacer-0 $spacer-32;
    background: linear-gradient(180deg, rgba(251 246 255 / 0%) 71.93%, $magenta-8a 99.96%);

    @include breakpoint(md) {
        padding: 0;
    }

    &.alternate_home_section_container .home_section__content {
        max-width: 100%;
        padding: $spacer-64 0;
    }

    .what_is_unblocked_heading {
        text-align: center;
        max-width: 800px;
        @include breakpoint(md) {
            padding: $spacer-0 96px;
        }

        h2 {
            @include section-header;
        }

        p {
            @include section-description;
        }

        .primary_cta_wrapper {
            margin: $spacer-24 0;
            @include breakpoint(md) {
                margin: $spacer-48 0 $spacer-32;
            }
            @include breakpoint(lg) {
                margin: $spacer-48 0 $spacer-36;
            }
        }
    }

    .logo_bar_marquee {
        padding: $spacer-48 $spacer-0;
    }
    .logo_bar_grid {
        margin: 24px 0;
        width: 100%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: $spacer-16;
        @include breakpoint(md) {
            gap: $spacer-32;
        }
    }

    .logo_brand_wrapper {
        @include flex-column-center;
        gap: $spacer-6;

        .logo_brand_image {
            height: 40px;
            @include breakpoint(md) {
                height: 53px;
            }

            @include breakpoint(lg) {
                height: 80px;
            }
        }

        .logo_brand_text {
            color: themed($text-tertiary);
            text-align: center;
            font-size: $font-size-12;
            line-height: 14px;
            letter-spacing: -0.174px;

            @include breakpoint(md) {
                line-height: 16px;
            }

            @include breakpoint(lg) {
                font-size: 19px;
                line-height: 24px;
                letter-spacing: -0.276px;
            }
        }
    }
}

.home_quotes_section {
    background: linear-gradient(0deg, rgba(255 255 255 / 0%) 0.04%, $magenta-8a 100.03%);

    .home_section__background {
        top: -23px;
        left: 50%;
        transform: translatex(-50%);
        @include breakpoint(md) {
            transform: none;
            left: inherit;
        }
    }
}

.home__quotes {
    .home_quotes__grid {
        display: flex;
        flex-flow: column wrap;
        align-items: center;
        max-width: $landing-container-wide-width;

        margin: $spacer-24 auto 0;
        padding: 0 $spacer-24;
        gap: $spacer-12;

        @include breakpoint(md) {
            padding: 0 $spacer-48;
            gap: $spacer-24;
            max-height: calc(375px * 6);

            .quote_content {
                max-width: 35%;
            }
        }

        @media screen and (width >= 800px) {
            max-height: calc(330px * 6);

            .quote_content {
                max-width: 37%;
            }
        }

        @media screen and (width >= 870px) {
            max-height: calc(300px * 6);

            .quote_content {
                max-width: 38%;
            }
        }

        @include breakpoint(lg) {
            max-height: calc(420px * 4);

            .quote_content {
                max-width: 23%;
            }
        }

        @media screen and (width >= 1075px) {
            max-height: calc(380px * 4);

            .quote_content {
                max-width: 24%;
            }
        }

        @media screen and (width >= 1140px) {
            max-height: calc(320px * 4);

            .quote_content {
                max-width: 24%;
            }
        }

        @include breakpoint(xl) {
            max-height: calc(360px * 4);

            .quote_content {
                max-width: 26%;
            }
        }
    }
}
