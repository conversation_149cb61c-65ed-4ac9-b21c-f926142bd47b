import Link from 'next/link';

import { FeatureSection } from '@landing/components/AlternateHomepage/Sections/FeatureSection/FeatureSection';
import { customerQuotes } from '@landing/components/CustomerQuotes';
import { DashboardButton } from '@landing/components/DashboardButton';
import { DemoButton } from '@landing/components/DemoButton';
import { EmphasisText } from '@landing/components/EmphasisText/EmphasisText';
import { InfoSectionStepContent } from '@landing/components/InfoSection/InfoSection';
import { Marquee } from '@landing/components/Marquee/Marquee';
import { QuoteBlock } from '@landing/components/QuoteSection/QuoteBlock';
import { QuotesContainer } from '@landing/components/QuoteSection/QuotesContainer';
import { QuoteSection } from '@landing/components/QuoteSection/QuoteSection';
import { HomeSection } from '@landing/components/Section/HomeSection';
import { UseCaseBlock, UseCaseProps } from '@landing/components/UseCases/UseCaseBlock';
import { Breakpoints } from '@web/components/Breakpoint/Breakpoints';

import avatar from '@clientAssets/landing/avatar.svg';
import barChart from '@clientAssets/landing/barChart.svg';
import featureMCP from '@clientAssets/landing/feature-MCP.json';
import prAgentFailureFeature from '@clientAssets/landing/feature-pr-agent-failure.json';
import qaFeature from '@clientAssets/landing/feature-qa.json';
import heroIllustration from '@clientAssets/landing/hero-illustration.png';
import bitbucket from '@clientAssets/landing/logobar/Bitbucket-icon.svg';
import claude from '@clientAssets/landing/logobar/claude-code-icon.svg';
import cursor from '@clientAssets/landing/logobar/cursor-icon.svg';
import github from '@clientAssets/landing/logobar/github-icon.svg';
import gitlab from '@clientAssets/landing/logobar/Gitlab-Icon.svg';
import jetbrains from '@clientAssets/landing/logobar/IntelliJ_IDEA-icon.svg';
import msTeams from '@clientAssets/landing/logobar/MSteams-icon.svg';
import slack from '@clientAssets/landing/logobar/Slack-icon.svg';
import unblocked from '@clientAssets/landing/logobar/unblocked-icon.svg';
import vscode from '@clientAssets/landing/logobar/vscode-icon.svg';
import webIcon from '@clientAssets/landing/logobar/web-icon.svg';
import windsurf from '@clientAssets/landing/logobar/windsurf-icon.svg';
import auditboard from '@clientAssets/landing/logos/auditboard.svg';
import aura from '@clientAssets/landing/logos/aura.svg';
import clio from '@clientAssets/landing/logos/clio.svg';
import coalition from '@clientAssets/landing/logos/coalition.svg';
import cribl from '@clientAssets/landing/logos/cribl.svg';
import drata from '@clientAssets/landing/logos/drata.svg';
import fingerprint from '@clientAssets/landing/logos/fingerprint.svg';
import hyperproof from '@clientAssets/landing/logos/hyperproof.svg';
import iheart from '@clientAssets/landing/logos/iheart.svg';
import lattice from '@clientAssets/landing/logos/lattice.svg';
import plaid from '@clientAssets/landing/logos/plaid.svg';
import ritchiebros from '@clientAssets/landing/logos/ritchiebros.svg';
import travelperk from '@clientAssets/landing/logos/travelperk.svg';
import usertesting from '@clientAssets/landing/logos/usertesting.svg';
import soc2security from '@clientAssets/landing/soc2security.png';
import upArrow from '@clientAssets/landing/upArrow.svg';

import './alternateHomepage.scss';

const marqueeLogos = [
    { src: coalition.src, alt: 'Coalition' },
    { src: fingerprint.src, alt: 'Fingerprint' },
    { src: drata.src, alt: 'Drata' },
    { src: travelperk.src, alt: 'Travel Perk' },
    { src: lattice.src, alt: 'Lattice' },
    { src: cribl.src, alt: 'Cribl' },
    { src: aura.src, alt: 'Aura' },
    { src: clio.src, alt: 'Clio' },
    { src: hyperproof.src, alt: 'Hyperproof' },
    { src: auditboard.src, alt: 'Auditboard' },
    { src: usertesting.src, alt: 'User Testing' },
    { src: iheart.src, alt: 'iHeart' },
    { src: plaid.src, alt: 'Plaid' },
    { src: ritchiebros.src, alt: 'Ritchie Bros' },
];

const logoBarMarquee = [
    { src: gitlab.src, alt: 'GitLab' },
    { src: unblocked.src, alt: 'macOS App' },
    { src: claude.src, alt: 'Claude' },
    { src: slack.src, alt: 'Slack' },
    { src: msTeams.src, alt: 'Teams' },
    { src: github.src, alt: 'GitHub' },
    { src: vscode.src, alt: 'VS Code' },
    { src: bitbucket.src, alt: 'Bitbucket' },
    { src: windsurf.src, alt: 'Windsurf' },
    { src: webIcon.src, alt: 'Web' },
    { src: cursor.src, alt: 'Cursor' },
    { src: jetbrains.src, alt: 'JetBrains IDEs' },
];

const integrationsSection: InfoSectionStepContent = {
    title: 'Get Instant, Trusted Answers',
    content: (
        <>
            <p>
                Unblocked surfaces the tribal knowledge buried in Slack, GitHub, Jira, and Confluence - now developers
                get accurate answers they need in seconds without pinging teammates or breaking focus.
            </p>
        </>
    ),
    lottieObj: qaFeature,
};
const keepShippingSection: InfoSectionStepContent = {
    title: 'Keep shipping when builds break',
    content: (
        <>
            <p>
                PR Failure Agent explains failing builds in plain language, points you to the root cause, and suggests
                the next step.
            </p>
            <p>
                <b>Coming soon:</b> a Code Review Agent aware of your coding standards and team expectations that
                highlights issues proactively so reviews go faster and quality stays high.
            </p>
        </>
    ),
    lottieObj: prAgentFailureFeature,
};

const toolsAndContextSection: InfoSectionStepContent = {
    title: 'Give people and tools the right context',
    content: (
        <>
            <p>
                Unblocked’s MCP server feeds AI-powered tools (like Cursor and Claude Code) real-time context from your
                codebase, docs, Slack, Jira, and more. The result? Dramatically more accurate code suggestions, fewer
                hallucinations, and faster, production-ready development.
            </p>
        </>
    ),
    lottieObj: featureMCP,
};

const useCaseOnboard: UseCaseProps = {
    link: '/use-cases/onboarding/',
    block: {
        icon: barChart.src,
        header: <span>Faster Onboarding</span>,
        description: <span>Ramp up new developers in days instead of months.</span>,
    },
};

const useCaseSupport: UseCaseProps = {
    link: '/use-cases/internal-support/',
    block: {
        icon: upArrow.src,
        header: <span>Automated Support</span>,
        description: <span>Handle internal questions at scale without adding overhead.</span>,
    },
};

const useCaseAutonomy: UseCaseProps = {
    link: '/use-cases/legacy-code/',
    block: {
        icon: avatar.src,
        header: <span>Greater Team Autonomy</span>,
        description: <span>Keep your team productive and self- sufficient as your team grows.</span>,
    },
};

const marqueeLogoBar = logoBarMarquee.map((logo, idx) => (
    <div className={'logo_brand_wrapper'} key={logo.alt}>
        <img className={'logo_brand_image'} src={logo.src} alt={logo.alt} key={idx} />
        <span className={'logo_brand_text'}>{logo.alt}</span>
    </div>
));

export default function AlternateHomepage() {
    return (
        <div className="home_container">
            <div className="home_hero">
                <div className="content_wrapper">
                    <h1>
                        Less time digging.
                        <br />
                        <EmphasisText variant="indigoGradient">More time doing.</EmphasisText>
                    </h1>
                    <p>
                        Cut onboarding time, reduce interruptions, by surfacing the context locked across GitHub, Slack,
                        Jira, and Confluence (and more)
                    </p>
                    <div className="primary_cta_wrapper">
                        <DemoButton size="large" variant="tertiary">
                            Book a Demo
                        </DemoButton>
                        <DashboardButton size="large">Get Started for Free</DashboardButton>
                    </div>
                </div>
                <img fetchPriority="high" className="hero__banner_image" src={heroIllustration.src} />
            </div>
            <Marquee>
                {marqueeLogos.map((logo, idx) => (
                    <img src={logo.src} alt={logo.alt} key={idx} />
                ))}
            </Marquee>
            <HomeSection
                className={'feature_section_wrapper'}
                alternateHomeSection
                header={
                    <h2>
                        Make tribal knowledge <EmphasisText variant="indigoGradient">self-serve.</EmphasisText>
                    </h2>
                }
                subheader={<p>The context your team needs to move fast — from first commits to production fixes.</p>}
            >
                {[integrationsSection, keepShippingSection, toolsAndContextSection].map((i, index) => (
                    <FeatureSection
                        key={index}
                        id={`integration-section-${index}`}
                        reverseOrientation={index % 2 === 1}
                        content={i}
                    />
                ))}
            </HomeSection>
            <HomeSection
                className={'use_case_section_wrapper'}
                alternateHomeSection
                header={<h2>Where teams succeed with Unblocked</h2>}
                subheader={<p>Unblocked solves real use cases that keep teams efficient as they grow.</p>}
            >
                <div className={'use_case_block_container'}>
                    {[useCaseOnboard, useCaseSupport, useCaseAutonomy].map((usecase, index) => (
                        <UseCaseBlock key={index} link={usecase.link} block={usecase.block} />
                    ))}
                </div>
            </HomeSection>

            <HomeSection className={'security_section_wrapper'} alternateHomeSection>
                <div className={'security_section'}>
                    <div className={'security_section_content'}>
                        <h2>
                            Unblocked checks all your{' '}
                            <EmphasisText variant="indigoGradient">security boxes</EmphasisText>
                        </h2>
                        <div className={'security_section_description'}>
                            <p>Unblocked has been designed with security in mind from day one.</p>
                            <p>
                                Learn more about our <a href="/security">approach to security</a> or visit our trust
                                center to review our SOC 2 and security documentation.
                            </p>
                        </div>
                        <Link className={'trust_center_link'} href={'https://trust.getunblocked.com/'} target="_blank">
                            Visit our trust center
                        </Link>
                    </div>
                    <div className={'security_section_image'}>
                        <img src={soc2security.src} alt="soc 2 security" />
                    </div>
                </div>
            </HomeSection>

            <HomeSection alternateHomeSection className={'what_is_unblocked_section'}>
                <div className="what_is_unblocked_heading">
                    <h2>Get answers where you work</h2>
                    <p>
                        Unblocked integrates with your everyday apps, so you can get answers without context switching.
                    </p>
                    <div className="primary_cta_wrapper">
                        <DemoButton size="large" variant="tertiary">
                            Book a Demo
                        </DemoButton>
                        <DashboardButton size="large">Get Started for Free</DashboardButton>
                    </div>
                </div>
                <Breakpoints
                    xs={<div className={'logo_bar_grid'}>{marqueeLogoBar}</div>}
                    md={<Marquee className={'logo_bar_marquee'}>{marqueeLogoBar}</Marquee>}
                />
            </HomeSection>

            <QuotesContainer
                className={'home_quotes_section'}
                header={<>Teams across the globe &#128156; Unblocked</>}
                id="quotes"
            >
                <Breakpoints
                    xs={<QuoteSection quotes={customerQuotes} />}
                    md={
                        <div className="home_quotes__grid">
                            {customerQuotes.map((quote, idx) => (
                                <QuoteBlock quote={quote} key={idx} />
                            ))}
                        </div>
                    }
                />
            </QuotesContainer>
        </div>
    );
}
