import classNames from 'classnames';

import './Marquee.scss';

interface MarqueeProps {
    children: React.ReactNode;
    className?: string;
}

export function Marquee({ children, className }: MarqueeProps) {
    return (
        <div className={classNames('marquee', className)}>
            <div className="marquee__group">{children}</div>
            <div aria-hidden="true" className="marquee__group">
                {children}
            </div>
        </div>
    );
}
