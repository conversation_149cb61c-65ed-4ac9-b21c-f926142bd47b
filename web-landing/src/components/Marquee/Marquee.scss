@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;

.marquee {
    --size: clamp(10rem, 1rem + 40vmin, 30rem);
    --gap: calc(var(--size) / 8);
    --duration: 60s;
    --scroll-start: 0;
    --scroll-end: calc(-100% - var(--gap));
    padding: $spacer-32;
    width: 100%;
    display: flex;
    overflow: hidden;
    user-select: none;
    gap: var(--gap);

    mask-image: linear-gradient(
        var(--mask-direction, to right),
        hsl(0 0% 0% / 0),
        hsl(0 0% 0% / 1) 20%,
        hsl(0 0% 0% / 1) 80%,
        hsl(0 0% 0% / 0)
    );
    .marquee__group {
        flex-shrink: 0;
        @include flex-center-around;
        gap: var(--gap);
        min-width: 100%;
        animation: scroll-x var(--duration) linear infinite;
    }
    @media (prefers-reduced-motion: reduce) {
        .marquee__group {
            animation-play-state: paused;
        }
    }
    @keyframes scroll-x {
        from {
            transform: translateX(var(--scroll-start));
        }
        to {
            transform: translateX(var(--scroll-end));
        }
    }
}
