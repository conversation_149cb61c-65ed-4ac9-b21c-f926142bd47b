import classNames from 'classnames';
import { forwardRef, Ref } from 'react';

import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';

import './HomeSection.scss';

interface SectionProps {
    className?: string;
    id?: string;
    header?: React.ReactNode;
    subheader?: React.ReactNode;
    children: React.ReactNode;
    background?: IconSrc;
    dark?: boolean;
    alternateHomeSection?: boolean;
}

const HomeSectionBase = (
    { className, id, header, subheader, children, background, dark, alternateHomeSection }: SectionProps,
    ref: Ref<HTMLDivElement>
) => {
    const classes = classNames({
        home_section_container: true,
        'home_section_container--dark': dark,
        [`${className}`]: !!className,
        alternate_home_section_container: alternateHomeSection,
    });

    return (
        <section className={classes} id={id} ref={ref}>
            {background ? <Icon className="home_section__background" icon={background} /> : null}
            {header ? <div className="home_section__header">{header}</div> : null}
            {subheader ? <div className="home_section__subheader">{subheader}</div> : null}
            <div className="home_section__content">{children}</div>
        </section>
    );
};

export const HomeSection = forwardRef(HomeSectionBase);
