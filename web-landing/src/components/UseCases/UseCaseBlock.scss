@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

@use '../../app/landing-mixin.scss' as *;

.use_case_block_wrapper {
    border-radius: $border-radius-10;
    border: solid $spacer-1 transparent;
    box-shadow: -2px 2px 12px 0 rgba(70, 22, 107, 0.1);
    background-image: linear-gradient(#f6f5ff, #f6f5ff), linear-gradient(to top right, #ab00ff05 2%, #ab00ff 100%);
    background-origin: border-box;
    background-clip: content-box, border-box;
    width: 100%;
    text-decoration: none;
    transition: box-shadow 0.3s ease-in-out;

    &:hover {
        background-image: linear-gradient(#fbfaff, #fbfaff), linear-gradient(to top right, #ab00ff05 2%, #ab00ff 100%);
        text-decoration: none;
        box-shadow: -2px 2px 16px 0 rgba(173, 0, 255, 0.25);
    }

    @include breakpoint(sm) {
        max-width: 380px;
    }
}
.use_case_block {
    text-align: center;
    padding: $spacer-24 $spacer-16;
    @include flex-column;
    gap: $spacer-8;

    @include breakpoint(md) {
        width: 320px;
        text-align: left;
        padding: $spacer-32;
    }
    .use_case_block__header {
        color: var(--indigo-100, #3e2dda);
        font-size: $font-size-18;
        line-height: $line-height-32;
        letter-spacing: -0.261px;
        align-items: center;
        justify-content: center;
        gap: $spacer-8;
        display: flex;
        @include breakpoint(lg) {
            font-size: 26px;
            letter-spacing: -0.377px;
        }

        @include breakpoint(md) {
            justify-content: start;
            font-size: $font-size-24;
            letter-spacing: -0.348px;
        }
    }

    .use_case_block__description {
        @include landing-content($size-19, $size-16);
        font-weight: $font-weight-light;
    }
}
