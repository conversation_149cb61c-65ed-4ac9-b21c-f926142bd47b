import classNames from 'classnames';
import Link from 'next/link';

import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';

import { faArrowRight } from '@fortawesome/pro-solid-svg-icons/faArrowRight';

import './UseCaseBlock.scss';

export interface UseCaseBlock {
    icon: IconSrc;
    header: React.ReactNode;
    description: React.ReactNode;
}

export interface UseCaseProps {
    block: UseCaseBlock;
    className?: string;
    link?: string;
}

type WrapperProps = {
    href?: string;
    className?: string;
    children: React.ReactNode;
};

const Wrapper: React.FC<WrapperProps> = ({ href, className, children }: WrapperProps) =>
    href ? (
        <Link href={href} className={className}>
            {children}
        </Link>
    ) : (
        <div className={className}>{children}</div>
    );

export const UseCaseBlock = ({ block, className, link }: UseCaseProps) => {
    const { icon, header, description } = block;
    return (
        <Wrapper href={link} className={classNames('use_case_block_wrapper', className)}>
            <div className={'use_case_block'}>
                <div className="use_case_block__icon">
                    <Icon icon={icon} size={60} />
                </div>
                <div className="use_case_block__header">
                    {header} <Icon icon={faArrowRight} />
                </div>
                <div className="use_case_block__description">{description}</div>
            </div>
        </Wrapper>
    );
};
