import classNames from 'classnames';
import { ReactNode } from 'react';

import { CssBreakpoints } from '@web/components/Breakpoint/CssBreakpoints';

import { StepsLarge } from './StepsLarge';
import { StepsMedium } from './StepsMedium';
import { StepsMobile } from './StepsMobile';

import './InfoSection.scss';

export interface InfoSectionStepContent {
    title: ReactNode;
    content: ReactNode;
    videoUrl?: string;
    videoUrlWebm?: string;
    mobileVideoUrl?: string;
    mobileVideoUrlWebm?: string;
    videoPoster?: string;
    lottieObj?: object | string; // can be an object or a URL string to the lottie animation
}

interface Props {
    title: ReactNode;
    subtitle?: ReactNode;
    id: string;
    steps: InfoSectionStepContent[];
    orientation: 'videoOnRight' | 'videoOnLeft';
    className?: string;
}

export function InfoSection({ id, className, ...props }: Props) {
    return (
        <div className={classNames('info_section', className)} id={id}>
            <CssBreakpoints
                xs={<StepsMobile {...props} id={id} />}
                sm={<StepsMobile {...props} id={id} />}
                md={<StepsMedium {...props} />}
                lg={<StepsMedium {...props} />}
                xl={<StepsLarge {...props} />}
            />
        </div>
    );
}
