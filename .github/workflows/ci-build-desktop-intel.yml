name: Build Electron app - intel

on:
  workflow_call:
    inputs:
      run-number:
        description: "GitHub run number"
        required: true
        type: string
      app-version:
        description: "Version of the app"
        required: true
        type: string
      artifact-name:
        description: "Name of the desktop artifact to upload"
        required: true
        type: string
      ref:
        description: "Branch, tag or SHA to checkout (default branch if not specified)"
        required: false
        type: string
    secrets:
      mac-os-certificate:
        description: "Developer ID application certificate"
        required: true
      mac-os-certificate-pwd:
        description: "Developer ID application certificate password"
        required: true
      asc-auth-key:
        description: "ASC Auth Key"
        required: true
      asc-key-id:
        description: "ASC Key ID"
        required: true
      asc-key-issuer:
        description: "ASC Key Issuer"
        required: true
      sentry-auth-token:
        description: "Sentry auth token"
        required: true
      fontawesome-npm-auth-token:
        description: "Font awesome npm auth token"
        required: true
      gh-personal-access-token:
        description: "GH PAT including private submodule repos"
        required: true
      mac-os-installer-certificate:
        description: "Developer ID installer certificate"
        required: true
      mac-os-installer-certificate-pwd:
        description: "Developer ID installer certificate password"
        required: true

# hack for https://github.com/actions/cache/issues/810#issuecomment-1222550359
env:
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3

jobs:
  build:
    name: desktop-build-intel
    timeout-minutes: 45
    runs-on: macos-13
    permissions:
      actions: write
    defaults:
      run:
        working-directory: ./desktop

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
          submodules: recursive
          token: ${{ secrets.gh-personal-access-token }}

      - uses: actions/setup-node@v4
        with:
          node-version: 16
          cache: ""

      - name: Set up JDK
        uses: actions/setup-java@v4.7.1
        with:
          java-version: ${{ vars.DOCKER_JAVA_VERSION }}
          distribution: ${{ vars.DOCKER_JAVA_DISTRO }}
        timeout-minutes: 10

      - name: Install Protoc
        uses: arduino/setup-protoc@v3
        with:
          version: "25.x"
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Keychain
        id: setup-keychain
        env:
          MACOS_CERTIFICATE: ${{ secrets.mac-os-certificate }}
          MACOS_CERTIFICATE_PWD: ${{ secrets.mac-os-certificate-pwd }}
          ASC_AUTHKEY: ${{ secrets.asc-auth-key }}
        run: ci_scripts/ci-setup-keychain.sh

      - name: setup python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: setup appdmg
        run: |
          python3 -m pip install setuptools
          npm install -g appdmg@0.6.6

      - name: Install shared dependencies
        run: cd .. && npm ci
        env:
          FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.fontawesome-npm-auth-token }}

      - name: Install electron dependencies
        run: npm ci
        env:
          FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.fontawesome-npm-auth-token }}

      - name: Check dependencies
        run: cd ../shared/scripts && ./find_poison_npm_dependencies.sh

      # Updates package.json's version and productNumber values
      - name: Update package version
        run: npm pkg set productNumber=${{ github.run_number }} && npm version ${{ inputs.app-version }}

      - name: Run build
        env:
          APPLE_API_KEY: ${{ steps.setup-keychain.outputs.APPLE_API_KEY}}
          ASC_KEY_ID: ${{ secrets.asc-key-id }}
          ASC_KEY_ISSUER: ${{ secrets.asc-key-issuer }}
        run: PRODUCT_NUMBER=${{ github.run_number }} PRODUCT_VERSION=${{ inputs.plugin-version }} npm run make:prod-intel

      - name: Upload intel ZIP
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.artifact-name }}-intel-zip
          path: ${{github.workspace}}/desktop/out/make/zip/darwin/x64/Unblocked-darwin-x64-${{ inputs.app-version }}.zip

  package:
    name: package-build-intel
    needs:
      - build
    timeout-minutes: 10
    runs-on: macos-13
    permissions:
      actions: write
    defaults:
      run:
        working-directory: ./installer

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
          submodules: recursive
          token: ${{ secrets.gh-personal-access-token }}

      - name: Download intel artifact ZIP
        uses: actions/download-artifact@v4
        with:
          name: desktop-artifact-${{ github.run_number }}-intel-zip
          path: ${{github.workspace}}/installer/desktop/intel

      - name: Extract intel desktop
        run: unzip ${{github.workspace}}/installer/desktop/intel/Unblocked-darwin-x64-${{ inputs.app-version }}.zip -d ${{github.workspace}}/installer/desktop/intel

      - name: Create intel pkg
        env:
          MACOS_CERTIFICATE: ${{ secrets.mac-os-installer-certificate }}
          MACOS_CERTIFICATE_PWD: ${{ secrets.mac-os-installer-certificate-pwd }}
          ASC_AUTHKEY: ${{ secrets.asc-auth-key }}
          ASC_KEY_ID: ${{ secrets.asc-key-id }}
          ASC_KEY_ISSUER: ${{ secrets.asc-key-issuer }}
        run: ./desktop_script_intel.sh

      - name: Upload intel
        uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.artifact-name }}-intel
          path: ${{github.workspace}}/installer/Unblocked\ intel.pkg
