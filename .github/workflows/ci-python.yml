name: Python

on:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/ci-python*'
      - 'python/**'
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  check-for-python-changes:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Paths Changes Filter
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - '.github/workflows/ci-python*'
              - 'python/**'

  list-libs:
    runs-on: ubuntu-latest
    name: List Libraries
    needs:
      - check-for-python-changes
    if: ${{ needs.check-for-python-changes.outputs.files == 'true' }}
    outputs:
      folders: ${{ steps.get-directory-names.outputs.directory-names }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get directory names
        id: get-directory-names
        working-directory: ./python/libs/
        run: echo "directory-names=$(find * -type d -maxdepth 0 | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

      - name: Echo
        run: echo ${{ steps.get-directory-names.outputs.directory-names }}

  build:
    timeout-minutes: 45
    needs:
      - list-libs
    name: python-build
    runs-on: ubuntu-22.04-4core
    strategy:
      matrix:
        folder: ${{ fromJson(needs.list-libs.outputs.folders )}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: jfrog/setup-jfrog-cli@v4
        env:
          JF_ENV_1: ${{ secrets.JFROG_SECRET_CONFIG_TOKEN }}

      - name: Check JFrog Artifactory Connectivity
        run: jf rt ping

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: 3.11

      - name: Install and configure Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.5.1
          virtualenvs-create: false
          virtualenvs-in-project: false
          installer-parallel: true

      - name: Setup JFrog Poetry Configuration
        env:
          JFROG_PASSWORD: ${{ secrets.JFROG_PASSWORD }}
        run: poetry config  http-basic.jfrog-server "<EMAIL>" "$JFROG_PASSWORD"

      - name: Run Poetry Install
        working-directory: ./python/libs/${{ matrix.folder }}
        run: poetry install --no-interaction

      - name: Install pip requirements
        working-directory: ./python/libs/${{ matrix.folder }}
        run: if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Run Poetry Build
        working-directory: ./python/libs/${{ matrix.folder }}
        run: poetry build --no-interaction

      - name: Run Poetry Test
        working-directory: ./python/libs/${{ matrix.folder }}
        run: poetry run pytest --suppress-no-test-exit-code

      - name: Run Code Formatting
        working-directory: ./python/libs/${{ matrix.folder }}
        run: poetry run black .
