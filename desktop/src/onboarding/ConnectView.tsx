import { useStream } from '@shared/stores/DataCacheStream';
import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { IntegrationImg } from '@shared/webComponents/IntegrationImg/IntegrationImg';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { DashboardUrls, getProviderDisplayName } from '@shared/webUtils';
import { getProviderOrgNode } from '@shared/webUtils/ProviderUtils';

import { faArrowUpRightFromSquare } from '@fortawesome/pro-regular-svg-icons/faArrowUpRightFromSquare';

import { ConnectTeamStreamTraits } from './ConnectTeamTypes';

import './ConnectView.scss';
export const ConnectView = () => {
    const connectTeamState = useStream(
        () => ClientWorkspace.instance().getStream(ConnectTeamStreamTraits, { $case: 'connectTeam' }),
        [],
        { $case: 'loading' }
    );

    if (connectTeamState.$case === 'loading') {
        return <Loading />;
    }

    const { provider, installUrl } = connectTeamState;

    const subtitle = installUrl
        ? `Continue to ${getProviderDisplayName(provider)} to grant access to the ${getProviderOrgNode(provider)} and repositories you want Unblocked to have access to.`
        : 'Grant access to the Workspace and repositories you want Unblocked to have access to.';

    const buttonContent = installUrl ? `Grant access in ${getProviderDisplayName(provider)}` : 'Grant access';

    const navigationUrl = installUrl ? installUrl : DashboardUrls.onboardingCreateTeam();

    return (
        <div className="connect_view">
            <div className="connect_view_body">
                <IntegrationImg provider={connectTeamState.provider} iconSize={76} />
                <h1>Grant access to your repositories</h1>
                <p>{subtitle}</p>
                <Button
                    size="large"
                    icon={faArrowUpRightFromSquare}
                    iconLocation="end"
                    onClick={() =>
                        ClientWorkspace.instance().handleAction({
                            $case: 'openUrl',
                            url: navigationUrl,
                        })
                    }
                >
                    {buttonContent}
                </Button>
            </div>
        </div>
    );
};
