import { getNotificationState, openNotificationSettings } from 'unblocked-native';

import { App } from '@desktop/App';
import { createValueStream } from '@shared/stores/ValueStream';
import { LazyValue } from '@shared/webUtils/LazyValue';
import { logger } from '@shared/webUtils/log';

import { NotificationSetupCommands, NotificationSetupState } from './NotificationSetupTypes';

const log = logger('NotificationSetupStream');

export class NotificationSetupStream {
    static readonly instance = LazyValue(() => new NotificationSetupStream());

    private valueStream = createValueStream<NotificationSetupState>({ $case: 'loading' });
    readonly stream = this.valueStream.stream;

    private userHasConfigured = false;

    constructor() {
        void this.load();
    }

    async processCommand(command: NotificationSetupCommands) {
        switch (command.$case) {
            case 'openNotificationPreferences':
                await this.openNotificationPreferences();
                break;

            case 'skipNotificationSetup':
                await this.skipNotificationSetup();
                break;
        }
    }

    private async openNotificationPreferences() {
        try {
            // Open notifications and wait
            await openNotificationSettings({
                awaitUntilPreferencesCloses: true,
                awaitUntilNotificationsEnabled: true,
            });
        } catch (error) {
            log.error('Error occurred opening notification settings', error);
        }

        this.userHasConfigured = true;
        await this.load();

        // Pop our UI back to the top after the settings UI is done
        App.instance().activate();
    }

    private async skipNotificationSetup() {
        this.userHasConfigured = true;
        await this.load();
    }

    private async load() {
        const isEnabled = (await getNotificationState()) === 'enabled';
        const userHasConfigured = isEnabled || this.userHasConfigured;
        this.valueStream.updateValue({ $case: 'loaded', isEnabled, userHasConfigured });
    }
}
