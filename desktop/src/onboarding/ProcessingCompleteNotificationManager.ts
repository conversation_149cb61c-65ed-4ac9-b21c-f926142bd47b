import { Stream } from 'xstream';

import { Team } from '@shared/api/generatedApi';

import { App } from '@desktop/App';
import { HomeStateStream } from '@desktop/home/<USER>';
import { DesktopNotifications } from '@desktop/notifications/DesktopNotifications';
import { filterReady } from '@shared/stores/StreamOperators';
import { TeamStore } from '@shared/stores/TeamStore';
import { LazyValue } from '@shared/webUtils/LazyValue';

export class ProcessingCompleteNotificationManager {
    static instance = LazyValue(() => new ProcessingCompleteNotificationManager());

    private processingTeamIds = new Set<string>();

    constructor() {
        Stream.combine(
            HomeStateStream.instance.selectedTeamIdStream,
            TeamStore.teamsAvailableForSelection.compose(filterReady).map((state) => state.value)
        ).subscribe({
            next: ([selectedTeamId, teams]) => this.processUpdates(selectedTeamId, teams),
        });
    }

    private processUpdates(selectedTeamId: string | undefined, teams: Team[]) {
        const teamMap = new Map(teams.map((team) => [team.id, team]));

        // Check for selected teams that are in the processing state
        if (selectedTeamId && teamMap.get(selectedTeamId)?.isProcessing) {
            this.processingTeamIds.add(selectedTeamId);
        }

        // Check the condition of all monitored teams
        [...this.processingTeamIds].forEach((teamId) => {
            const team = teamMap.get(teamId);

            // Team is removed -- happens on logout
            if (!team) {
                this.processingTeamIds.delete(teamId);
            }

            // Team is doen processing
            else if (!team.isProcessing) {
                this.processingTeamIds.delete(teamId);
                this.notifyProcessingComplete(teamId);
            }
        });
    }

    private notifyProcessingComplete(teamId: string) {
        void DesktopNotifications.instance.sendNotification('Unblocked', 'Processing Complete.  Continue setup!', () =>
            this.onNotificationClick(teamId)
        );
    }

    private onNotificationClick(teamId: string) {
        HomeStateStream.instance.selectTeam(teamId);
        App.instance().activate();
    }
}

export function InitializeProcessingCompleteNotifications() {
    ProcessingCompleteNotificationManager.instance();
}
