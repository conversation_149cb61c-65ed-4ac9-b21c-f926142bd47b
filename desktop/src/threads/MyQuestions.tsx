import classNames from 'classnames';
import { MouseEvent, useCallback, useMemo } from 'react';

import { Team } from '@shared/api/generatedApi';
import { ThreadInfoAggregate } from '@shared/api/generatedExtraApi';

import { MyQuestionsStreamTraits } from '@desktop/components/ClientWorkspace/MyQuestionsStreamTraits';
import { SidebarRow } from '@shared/ide/components/SidebarRow/SidebarRow';
import { useStream } from '@shared/stores/DataCacheStream';
import { getStore } from '@shared/stores/getStore';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { ContextMenuItem } from '@shared/webComponents/ContextMenuProvider/ContextMenuItem';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { EditThreadTitleDialog } from '@shared/webComponents/ThreadView/EditThreadTitleDialog';
import { ArrayUtils } from '@shared/webUtils';
import { CopyToClipboard } from '@shared/webUtils/CopyUtils';
import { GetIconForThread } from '@shared/webUtils/InsightIcons';
import { sortByDaysAgo } from '@shared/webUtils/Insights/InsightSortUtils';
import { canEditThreadTitle } from '@shared/webUtils/ThreadUtils';

import './MyQuestions.scss';

interface Props {
    team: Team;
    selectedThreadId?: string;
    selectThread: (threadId: string) => void;
    unreadOnly?: boolean;
}

export const MyQuestions = (props: Props) => {
    const { unreadOnly } = props;

    return (
        <div className="my_questions_sidebar">
            <MyQuestionsContainer {...props} unreadsOnly={unreadOnly} />
        </div>
    );
};

type ContainerProps = Props & { unreadsOnly?: boolean };

const MyQuestionsContainer = ({ team, selectedThreadId, selectThread, unreadsOnly }: ContainerProps) => {
    const state = useStream(
        () =>
            ClientWorkspace.instance()
                .getStream(MyQuestionsStreamTraits, { $case: 'myQuestions', teamId: team.id, unreadsOnly })
                .startWith({ $case: 'loading' }),
        [team, unreadsOnly]
    );

    if (!state) {
        return <div>Missing questions State - {team.id}</div>;
    }

    switch (state.$case) {
        case 'loading':
            return (
                <div className="my_questions my_questions--loading">
                    <Loading centerAlign delayed />
                </div>
            );
        case 'ready':
            return (
                <MyQuestionsContent
                    threads={state.value}
                    selectThread={selectThread}
                    selectedThreadId={selectedThreadId}
                />
            );
    }
};

const MyQuestionsContent = ({
    threads,
    selectThread,
    selectedThreadId,
}: {
    threads: ThreadInfoAggregate[];
    selectThread: (threadId: string) => void;
    selectedThreadId?: string;
}) => {
    const hasUnreads = useMemo((): boolean => {
        return !!threads.find((v) => v.unread?.isUnread);
    }, [threads]);

    const [unreadThreads, readThreads] = ArrayUtils.divide(threads, (threadInfo) => !!threadInfo.unread?.isUnread);

    const threadsByDateFormat = useMemo(
        () => sortByDaysAgo(readThreads, (threadInfo) => threadInfo.thread.lastMessageCreatedAt),
        [readThreads]
    );
    const className = classNames({
        my_questions: true,
        'my_questions--empty': !threads.length,
    });
    return (
        <div className={className}>
            {!threads.length && <div className="my_questions__empty_message">No historical questions</div>}
            {!!unreadThreads.length && (
                <MyQuestionSection
                    key={`Unreads`}
                    header="Unreads"
                    threads={unreadThreads}
                    selectThread={selectThread}
                    selectedThreadId={selectedThreadId}
                    addUnreadSpace={hasUnreads}
                />
            )}
            {threadsByDateFormat.map(([header, threads], idx) => (
                <MyQuestionSection
                    key={`${idx}-${header}`}
                    header={header}
                    threads={threads}
                    selectThread={selectThread}
                    selectedThreadId={selectedThreadId}
                    addUnreadSpace={hasUnreads}
                />
            ))}
        </div>
    );
};

interface SectionProps {
    header: React.ReactNode;
    threads: ThreadInfoAggregate[];
    selectThread: (threadId: string) => void;
    selectedThreadId?: string;
    addUnreadSpace: boolean;
}
const MyQuestionSection = (props: SectionProps) => {
    const { header, threads } = props;

    if (!threads.length) {
        return null;
    }

    return (
        <div className="my_questions__section">
            <div className="my_questions__section__header">{header}</div>
            <div className="my_questions__section__list">
                {threads.map((threadInfo) => {
                    return <MyQuestionRow key={threadInfo.thread.id} threadInfo={threadInfo} {...props} />;
                })}
            </div>
        </div>
    );
};

interface RowProps {
    threadInfo: ThreadInfoAggregate;
    selectThread: (threadId: string) => void;
    selectedThreadId?: string;
    addUnreadSpace: boolean;
}

const MyQuestionRow = ({ threadInfo, selectThread, selectedThreadId, addUnreadSpace }: RowProps) => {
    const { openModal } = useModalContext();

    const lastMessage = useMemo(() => {
        return ArrayUtils.lastOrUndefined(threadInfo.messages);
    }, [threadInfo]);
    const items: ContextMenuItem[] = useMemo(() => {
        const threadUrl = threadInfo.thread.links.dashboardUrl;
        const isUnread = threadInfo.unread?.isUnread;
        const toggleUnread = () => {
            void ClientWorkspace.instance().handleAction({
                $case: 'updateThreadUnread',
                teamId: threadInfo.thread.teamId,
                threadId: threadInfo.thread.id,
                updateThreadRequest: {
                    latestReadMessage: threadInfo.unread?.isUnread ? threadInfo.unread?.latestMessage : undefined,
                },
            });
        };
        return ArrayUtils.compact<ContextMenuItem>([
            {
                item: {
                    id: 'copyThreadLink',
                    title: 'Copy Link',
                },
                callback: () => CopyToClipboard(threadUrl),
            },
            {
                item: {
                    id: 'toggleUnread',
                    title: isUnread ? 'Mark as Read' : 'Mark as Unread',
                },
                callback: toggleUnread,
            },
            {
                item: {
                    id: 'divider',
                    title: '',
                    itemType: 'separator',
                },
            },
            canEditThreadTitle(threadInfo)
                ? {
                      item: {
                          id: 'editTitle',
                          title: 'Edit Title',
                      },
                      callback: () => {
                          openModal(
                              <EditThreadTitleDialog
                                  title={threadInfo.thread.title}
                                  onSave={async (title) => {
                                      await getStore(ThreadStoreTraits, {
                                          threadId: threadInfo.thread.id,
                                          teamId: threadInfo.thread.teamId,
                                          title: threadInfo.thread.title,
                                      }).updateTitle(title);
                                  }}
                              />
                          );
                      },
                  }
                : undefined,
            {
                item: {
                    id: 'archive',
                    title: 'Archive Discussion',
                },
                callback: () =>
                    getStore(ThreadStoreTraits, {
                        threadId: threadInfo.thread.id,
                        teamId: threadInfo.thread.teamId,
                        title: threadInfo.thread.title,
                    }).confirmArchive(),
            },
        ]);
    }, [threadInfo, openModal]);

    const selectInsightCallback = useCallback(() => {
        const threadId = threadInfo.thread.id;
        selectThread(threadId);
    }, [threadInfo, selectThread]);

    const triggerMenu = useMemo(() => {
        return ClientWorkspace.instance().contextMenuProvider?.menu;
    }, []);

    const onContextMenu = useCallback(
        async (event: MouseEvent) => {
            if (!triggerMenu) {
                return;
            }
            event.preventDefault();
            event.stopPropagation();
            const requestItems = items.map((v) => v.item);
            const response = await triggerMenu({
                items: requestItems,
            });

            const matchingItem = items.find((v) => v.item.id === response?.responseId);

            if (matchingItem) {
                matchingItem.callback?.();
            }
        },
        [triggerMenu, items]
    );

    const isUnread = addUnreadSpace ? (threadInfo.unread?.isUnread ?? true) : undefined;

    return (
        <SidebarRow
            key={threadInfo.thread.id}
            icon={GetIconForThread(threadInfo)}
            iconSize="mediumLarge"
            title={threadInfo.thread.title}
            content={lastMessage?.messageContent}
            onClick={selectInsightCallback}
            onContextMenu={onContextMenu}
            isSelected={threadInfo.thread.id === selectedThreadId}
            tabIndex={0}
            isUnread={isUnread}
        />
    );
};
