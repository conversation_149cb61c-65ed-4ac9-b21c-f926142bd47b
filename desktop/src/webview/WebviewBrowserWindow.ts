import { BrowserWindow, BrowserWindowConstructorOptions, screen } from 'electron';

import { MainWindowManager } from '@desktop/MainWindow';
import { environment } from '@shared/config';
import { UnblockedEnvProperty } from '@shared/config/UnblockedEnvProperty';
import { UrlHelper } from '@shared/webUtils';

import { WebviewContentController } from './WebviewContentController';
import { WindowArgs, WindowArgsQueryParam, WindowType } from './WebviewTypes';

type BrowserWindowConstructorSizeOptions =
    // The window defines the view content size
    | {
          sizeToContent?: never;
      }

    // The view content defines the window size
    | {
          sizeToContent?: true;

          // These values don't make sense when sizing to content,
          // so are disabled
          width?: never;
          height?: never;
          maxWidth?: never;
          maxHeight?: never;
          minWidth?: never;
          minHeight?: never;
          resizable?: never;
          maximizable?: never;
          fullscreenable?: never;
      };

type BrowserWindowConstructorPositionOptions =
    // Don't center on main window -- this allows arbitrary positioning
    | {
          centerOnMainWindow?: never;

          // Allow centering in the middle of the screen
          centerOnScreen?: boolean;

          // Electron's built-in centering does not work.  Use centerOnScreen instead
          center?: never;
      }

    // Center on main window -- positioning is not allowed
    | {
          centerOnMainWindow: true;
          center?: never;
          centerOnScreen?: never;
          x?: never;
          y?: never;
      };

type WebviewBrowserWindowOptions = Omit<BrowserWindowConstructorOptions, 'show' | 'webPreferences'> &
    BrowserWindowConstructorSizeOptions &
    BrowserWindowConstructorPositionOptions & {
        windowType: WindowType;
    };

// This allows TypeScript to pick up the magic constants that's auto-generated by Forge's Webpack
// plugin that tells the Electron app where to look for the Webpack-bundled app code (depending on
// whether you're running in development or production).
declare const MAIN_WINDOW_WEBPACK_ENTRY: string;
declare const MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY: string;

export class WebviewBrowserWindow extends BrowserWindow {
    private controller: WebviewContentController;

    private readyToShow = false;
    private hasSizeSet = false;
    private centerOnMainWindow = false;
    private centerOnScreen = false;

    constructor({ sizeToContent, centerOnMainWindow, centerOnScreen, ...options }: WebviewBrowserWindowOptions) {
        const baseOptions: BrowserWindowConstructorOptions = {
            ...options,
            webPreferences: {
                preload: MAIN_WINDOW_PRELOAD_WEBPACK_ENTRY,
            },

            show: false, // Wait until ready-to-show triggers
        };

        // Size to content
        if (sizeToContent) {
            baseOptions.resizable = false;
            baseOptions.fullscreenable = false;
            baseOptions.maximizable = false;
        }

        super(baseOptions);

        this.centerOnMainWindow = centerOnMainWindow ?? false;
        this.centerOnScreen = centerOnScreen ?? false;

        // Size to window
        if (!sizeToContent) {
            this.hasSizeSet = true;
        }

        const onContentSizeChanged = sizeToContent
            ? (width: number, height: number) => {
                  this.onContentSizeChanged(width, height);
              }
            : undefined;

        this.controller = new WebviewContentController(this, onContentSizeChanged);

        this.webContents.send(UnblockedEnvProperty, environment.name);

        const windowArgs: WindowArgs = {
            type: options.windowType,
            sizeToContent,
        };

        const windowArgsJson = JSON.stringify(windowArgs);
        const windowArgsQueryValue = encodeURIComponent(windowArgsJson);

        const rendererUrl = UrlHelper.AddUrlSearchParams(
            MAIN_WINDOW_WEBPACK_ENTRY,
            new URLSearchParams([[WindowArgsQueryParam, windowArgsQueryValue]])
        );

        void this.loadURL(rendererUrl);

        this.once('ready-to-show', () => {
            this.readyToShow = true;
            this.showIfConfigurationComplete();
        });

        this.on('closed', () => this.controller.dispose());
    }

    private onContentSizeChanged(width: number, height: number) {
        this.setContentSize(width, height);
        this.hasSizeSet = true;

        this.showIfConfigurationComplete();
    }

    private getCenterOnMainWindowLocation(): [number, number] | undefined {
        const mainWindow = MainWindowManager.instance.mainWindow;
        if (!mainWindow) {
            return undefined;
        }
        const mainBounds = mainWindow.getBounds();
        const windowSize = this.getSize();

        // Calculate the position for the child window
        const childX = Math.floor(mainBounds.x + (mainBounds.width - windowSize[0]) / 2);
        const childY = Math.floor(mainBounds.y + (mainBounds.height - windowSize[1]) / 2);
        return [childX, childY];
    }

    private getCenterOnScreenLocation(): [number, number] {
        const display = screen.getDisplayMatching(this.getBounds());
        const workArea = display.workArea;
        const [windowWidth, windowHeight] = this.getSize();

        // Center on display
        return [
            workArea.x + Math.floor((workArea.width - windowWidth) / 2),
            workArea.y + Math.floor((workArea.height - windowHeight) / 2),
        ];
    }

    private showIfConfigurationComplete() {
        if (!this.hasSizeSet || !this.readyToShow) {
            return;
        }

        if (this.centerOnMainWindow) {
            const location = this.getCenterOnMainWindowLocation();
            if (location) {
                this.setPosition(location[0], location[1]);
            }
        }

        if (this.centerOnScreen) {
            const [x, y] = this.getCenterOnScreenLocation();
            this.setPosition(x, y);
        }

        this.show();
    }
}
