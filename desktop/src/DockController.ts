import dayjs from 'dayjs';
import { app } from 'electron';

import { MainWindowManager } from './MainWindow';

/**
 * Controls showing and hiding the MacOS dock icon.
 * There is a bug in the dock where showing, then hiding the icon quickly will result in the icon getting
 * stuck in the open state.  This class works around that by ensuring that we always wait a full second
 * between showing the dock and hiding it.
 */
export class DockController {
    private isShown?: boolean; // Undefined by default -- ensures first call to 'show' applies value
    private hideTimer?: ReturnType<typeof setTimeout>;
    private nextSafeHideTime = dayjs(0);

    constructor() {
        MainWindowManager.instance.isVisible.subscribe({
            next: (shouldShow) => {
                if (this.isShown !== shouldShow) {
                    this.isShown = shouldShow;

                    if (shouldShow) {
                        this.doShow();
                    } else {
                        this.doHide();
                    }
                }
            },
        });
    }

    private doShow() {
        // We can always show the dock icon at any time
        // Clear any existing hide timer and show the dock icon
        if (this.hideTimer) {
            clearTimeout(this.hideTimer);
            this.hideTimer = undefined;
        }

        this.nextSafeHideTime = dayjs().add(1, 's');
        void app.dock?.show();
    }

    private doHide() {
        // If it's not safe to hide the icon yet, set a timer that will trigger hiding it
        const now = dayjs();
        if (now.isBefore(this.nextSafeHideTime)) {
            this.hideTimer = setTimeout(() => {
                app.dock?.hide();
                this.hideTimer = undefined;
            }, this.nextSafeHideTime.diff(now));
        }

        // otherwise, we can hide now
        else {
            app.dock?.hide();
        }
    }
}
