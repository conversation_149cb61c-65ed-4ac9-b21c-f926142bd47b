import classNames from 'classnames';
import { ReactNode, useCallback } from 'react';

import { IndeterminateProgressBar } from '@desktop/components/IndeterminateProcessBar/IndeterminateProgressBar';
import { useOnceWhen } from '@shared/hooks/useOnceWhen';
import { useStream } from '@shared/stores/DataCacheStream';
import { useStore } from '@shared/stores/useStore';
import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { BrandIcons } from '@shared/webUtils/BrandIcons';
import { DocsUrl } from '@shared/webUtils/DocsUrl';

import { faCircleCheck } from '@fortawesome/pro-duotone-svg-icons/faCircleCheck';
import { faCircleExclamation } from '@fortawesome/pro-duotone-svg-icons/faCircleExclamation';

import { IDEPluginInstallations, IDEPluginInstallerStoreTraits } from './IDEPluginInstallerTypes';
import { InstallResults } from './InstallResults';

import './InstallIDEPlugins.scss';

type InstallIDEPluginsSize = 'small' | 'large';

interface Props {
    icon: IconSrc;
    iconClass?: string;
    title?: string;
    subtitle?: ReactNode;
    children?: ReactNode;
    showContactSupport?: boolean;
    size: InstallIDEPluginsSize;
    onContinue?: () => void;
}

function InstallIDEGroups({ results }: { results: IDEPluginInstallations }) {
    return (
        <div className="install_ide_plugins__result_groups">
            {!!results.mcpPlugins.length && (
                <InstallResults title="Unblocked MCP Server installation status" results={results.mcpPlugins} />
            )}
            {!!results.idePlugins.length && (
                <InstallResults title="Unblocked IDE plugin installation status" results={results.idePlugins} />
            )}
        </div>
    );
}

function InstallPluginState({
    icon,
    iconClass,
    title,
    subtitle,
    children,
    showContactSupport,
    size,
    onContinue,
}: Props) {
    const iconSize = size === 'large' ? 80 : 64;
    const buttonSize = size === 'large' ? 'large' : 'standard';
    const showButtons = showContactSupport || !!onContinue;
    return (
        <div className={classNames('install_ide_plugins', `install_ide_plugins__${size}`)}>
            <div className="install_ide_plugins__header">
                <Icon className={classNames('install_ide_plugins__icon', iconClass)} icon={icon} size={iconSize} />
                {title && <h2 className="install_ide_plugins__title">{title}</h2>}
                {subtitle && <div className="install_ide_plugins__subtitle">{subtitle}</div>}
                {showButtons && (
                    <div className="install_ide_plugins__buttons">
                        {showContactSupport && (
                            <Button
                                variant="secondary"
                                onClick={() => ClientWorkspace.instance().handleAction({ $case: 'contactSupport' })}
                                size={buttonSize}
                            >
                                Contact Support
                            </Button>
                        )}
                        {onContinue && (
                            <Button onClick={onContinue} size={buttonSize}>
                                Continue
                            </Button>
                        )}
                    </div>
                )}
            </div>
            {children && <div className="install_ide_plugins__content">{children}</div>}
        </div>
    );
}

export function InstallIDEPlugins({ size, onContinue }: { size: InstallIDEPluginsSize; onContinue?: () => void }) {
    const store = useStore(IDEPluginInstallerStoreTraits, {});
    const installState = useStream(() => store.stream, [store], { $case: 'idle' });

    // Begin installation when this view is opened
    useOnceWhen(true, () => store.startInstall());

    const showMcpDocs = useCallback(
        () =>
            ClientWorkspace.instance().handleAction({
                $case: 'openUrl',
                url: DocsUrl.mcp(),
                newWindow: true,
            }),
        []
    );

    if (installState.$case === 'idle' && !installState.lastResult) {
        return <Loading />;
    }

    if (installState.$case === 'running') {
        return (
            <InstallPluginState icon={BrandIcons.unblocked} size={size}>
                <div className="install_ide_plugins__progress">
                    <IndeterminateProgressBar />
                    <div>Downloading and installing Unblocked plugins...</div>
                </div>
            </InstallPluginState>
        );
    }

    if (!installState.lastResult) {
        return <Loading />;
    }

    switch (installState.lastResult.$case) {
        case 'success':
            return (
                <InstallPluginState
                    icon={faCircleCheck}
                    iconClass="install_ide_plugins__icon__success"
                    title="Unblocked Plugins Installed Successfully."
                    size={size}
                    onContinue={onContinue}
                >
                    <InstallIDEGroups results={installState.lastResult} />
                </InstallPluginState>
            );

        case 'error':
            return (
                <InstallPluginState
                    icon={faCircleExclamation}
                    iconClass="install_ide_plugins__icon__error"
                    title="An error occurred during installation"
                    subtitle="Reach out to the Unblocked team for assistance."
                    showContactSupport
                    size={size}
                    onContinue={onContinue}
                >
                    <InstallIDEGroups results={installState.lastResult} />
                </InstallPluginState>
            );

        case 'noIdes':
            return (
                <InstallPluginState
                    icon={faCircleExclamation}
                    iconClass="install_ide_plugins__icon__error"
                    title="Unblocked could not detect an IDE on your Mac."
                    subtitle={
                        <>
                            Download Visual Studio Code, a JetBrains IDE, or one of the supported{' '}
                            <a onClick={showMcpDocs} href="#">
                                MCP Server IDEs
                            </a>{' '}
                            before trying again.
                        </>
                    }
                    showContactSupport
                    size={size}
                    onContinue={onContinue}
                />
            );
    }
}
