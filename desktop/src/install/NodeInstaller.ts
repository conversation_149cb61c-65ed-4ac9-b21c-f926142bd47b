import fs from 'fs/promises';
import os from 'os';
import path from 'path';
import semver from 'semver';

import { FileArchive } from '@shared/ide/utils/FileArchive';
import { GetPlatformArch, GetPlatformType, PlatformArch } from '@shared/ide/utils/OSUtils';
import { Runner } from '@shared/node/process';
import { LazyValue } from '@shared/webUtils';

import { DownloadFileAndRun } from './FileDownloader';

interface NodeInstallationInfo {
    binPath: string;
}

export class NodeInstaller {
    static instance = LazyValue(() => new NodeInstaller());
    private nodeVersion = 'v22.15.0';

    private infoPromise?: Promise<NodeInstallationInfo>;

    // Ensures the desired node is installed -- returns the installation location.
    async ensureInstalled(): Promise<NodeInstallationInfo> {
        // Glob promises so we don't install twice at the same time
        if (this.infoPromise) {
            return this.infoPromise;
        }

        this.infoPromise = this.doEnsureInstalled();

        // Once the promise completes, clear it
        void this.infoPromise.finally(() => (this.infoPromise = undefined));

        return this.infoPromise;
    }

    private async doEnsureInstalled(): Promise<NodeInstallationInfo> {
        const existingInstallation = await this.getExistingInstallation();
        if (existingInstallation) {
            return existingInstallation;
        }

        return await this.install();
    }

    private async getExistingInstallation(): Promise<NodeInstallationInfo | undefined> {
        const binPath = this.installBinary;

        try {
            const { stdout: version } = await Runner.run(binPath, ['--version']);
            if (semver.gte(version, this.nodeVersion)) {
                return { binPath }; // Good version is already installed -- use it
            }
        } catch {}
    }

    private async install(): Promise<NodeInstallationInfo> {
        const url = `https://nodejs.org/download/release/${this.nodeVersion}/node-${this.nodeVersion}-${this.packageSuffix}.${this.packageExtension}`;

        await DownloadFileAndRun(url, `node-installer.${this.packageExtension}`, async (downloadedFilePath) => {
            await FileArchive.uncompressAndRun(downloadedFilePath, async (extractedPath) => {
                try {
                    await fs.rm(this.installDir, { recursive: true, force: true });
                } catch {}

                const extractedNodeFolder = path.join(extractedPath, `node-${this.nodeVersion}-${this.packageSuffix}`);

                await fs.cp(extractedNodeFolder, this.installDir, { recursive: true });
            });
        });

        return { binPath: this.installBinary };
    }

    private get installDir() {
        return path.join(os.homedir(), '.unblocked', 'agent', 'node');
    }

    private get installBinary() {
        switch (os.platform()) {
            case 'win32':
                return path.join(this.installDir, 'node.exe');

            default:
                return path.join(this.installDir, 'bin', 'node');
        }
    }

    private get packageSuffix(): string {
        switch (GetPlatformArch()) {
            case PlatformArch.linuxX64:
                return 'linux-x64';
            case PlatformArch.linuxArm64:
                return 'linux-arm64';
            case PlatformArch.macArm64:
                return 'darwin-arm64';
            case PlatformArch.macX64:
                return 'darwin-x64';
            case PlatformArch.winX64:
                return 'win-x64';
            case PlatformArch.win32:
                return 'win-x86';
            case PlatformArch.winArm64:
                return 'win-arm64';
            default:
                throw new Error('Unsupported platform');
        }
    }

    private get packageExtension(): string {
        const platformType = GetPlatformType();
        return platformType === 'windows' ? 'zip' : 'tar.gz';
    }
}
