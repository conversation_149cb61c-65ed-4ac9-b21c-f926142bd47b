import { app } from 'electron';
import fs from 'original-fs';
import path from 'path';

import { ElectronFetchImpl } from '@desktop/api/BaseApi';
import { DownloadFile as BaseDownloadFile } from '@shared/node/util/FileDownloader';
import { logger } from '@shared/webUtils/log';

const log = logger('FileDownloader');

/**
 * Downloads a file to a temporary location.
 * @param url The URL to download
 * @param tmpFileBaseName The base name for the file to temporarily store
 * @returns The full path to the downloaded file, on completion
 */
export async function DownloadFile(url: string, tmpFileBaseName: string): Promise<string> {
    const filePath = path.join(app.getPath('temp'), tmpFileBaseName);
    await BaseDownloadFile(url, filePath, { fsApi: fs, fetchApi: ElectronFetchImpl });
    return filePath;
}

/**
 * Downloads a file to a temporary location, and runs the given code on the downloaded file.
 * The file is removed after the code is run.
 * @param url The URL to download
 * @returns The full path to the downloaded file, on completion
 */
export async function DownloadFileAndRun<ResultT>(
    url: string,
    tmpFileBaseName: string,
    runFn: (path: string) => Promise<ResultT>
): Promise<ResultT> {
    const tmpPath = await DownloadFile(url, tmpFileBaseName);

    try {
        return await runFn(tmpPath);
    } finally {
        try {
            await fs.promises.rm(tmpPath);
        } catch (error) {
            log.warn('Could not remove temporary file', error);
        }
    }
}
