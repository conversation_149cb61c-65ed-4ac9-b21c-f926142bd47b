import { API } from '@shared/api';

import { ValueStream } from '@shared/stores/NewValueStream';
import { IDEPluginInstalledState } from '@shared/stores/PersonOnboardingTypes';
import { createValueStream } from '@shared/stores/ValueStream';
import { LazyValue, PromiseUtils } from '@shared/webUtils';
import { logger } from '@shared/webUtils/log';

import { IDEPluginInstallations, IDEPluginInstallerStreamState } from './IDEPluginInstallerTypes';
import { InstallIDEPluginsWindow } from './InstallIDEPluginsWindow';
import { InstallJetBrainsPlugin } from './JetBrainsPluginInstaller';
import { McpInstaller } from './McpServerInstaller';
import { InstallVSCodePlugin } from './VSCodePluginInstaller';

const log = logger('IDEPluginInstaller');

/**
 * Class responsible for IDE plugin installation
 */
export class IDEPluginInstaller {
    static instance = LazyValue(() => new IDEPluginInstaller());

    private valueStream = createValueStream<IDEPluginInstallerStreamState>({ $case: 'idle' });
    readonly stream = this.valueStream.stream;

    private hasInstalledValueStream = new ValueStream<IDEPluginInstalledState>('uninstalled');
    readonly hasInstalledStream = this.hasInstalledValueStream.readOnlyStream;

    private shouldInstallValueStream = new ValueStream(true);
    readonly shouldInstallStream = this.shouldInstallValueStream.readOnlyStream;

    // Install without waiting for the result
    async startInstall() {
        void this.install();
    }

    async completeInstall() {
        this.shouldInstallValueStream.value = false;
    }

    // Launch install UI
    async launchInstall() {
        InstallIDEPluginsWindow.instance().open();
    }

    /**
     * Install plugins into all installed IDEs
     */
    async install() {
        if (this.valueStream.getCurrentValue()?.$case === 'running') {
            return;
        }

        this.valueStream.updateValue({ $case: 'running' });

        try {
            const installedInfos = await this.doInstall();

            // No IDEs detected
            if (installedInfos.idePlugins.length === 0 && installedInfos.mcpPlugins.length === 0) {
                this.valueStream.updateValue({ $case: 'idle', lastResult: { $case: 'noIdes' } });
            }

            // Error occurred while processing
            else if (
                installedInfos.idePlugins.some((info) => info.result === 'failure') ||
                installedInfos.mcpPlugins.some((info) => info.result === 'failure')
            ) {
                this.valueStream.updateValue({ $case: 'idle', lastResult: { $case: 'error', ...installedInfos } });
            }

            // Installation successful
            else {
                this.valueStream.updateValue({ $case: 'idle', lastResult: { $case: 'success', ...installedInfos } });
            }
        } catch (error) {
            // Unexpected failure
            log.error('Error occurred installing IDE plugins', error);
            this.valueStream.updateValue({
                $case: 'idle',
                lastResult: { $case: 'error', idePlugins: [], mcpPlugins: [] },
            });
        } finally {
            this.hasInstalledValueStream.value = 'installed';
        }
    }

    private async doInstall(): Promise<IDEPluginInstallations> {
        // This isn't really a warning, but this forces the log to be sent to
        // logz.io
        log.warn('Beginning IDE plugin installation');

        const versionResult = await API.versions.getLatestVersionInfo();
        const versionInfo = versionResult.versions?.[0];

        if (!versionInfo) {
            throw new Error('No VersionInfo');
        }

        const idePluginInstallations = PromiseUtils.allToCompletion([
            InstallVSCodePlugin(versionInfo),
            InstallJetBrainsPlugin(versionInfo),
        ]);

        const mcpInstallations = McpInstaller.install();

        const [nestedIdeResults, mcpResults] = await PromiseUtils.allToCompletion([
            idePluginInstallations,
            mcpInstallations,
        ]);

        const ideResults = nestedIdeResults.flat();

        // This isn't really a warning, but this forces the log to be sent to
        // logz.io
        log.warn('Completed IDE plugin installation');

        return {
            idePlugins: ideResults,
            mcpPlugins: mcpResults,
        };
    }

    async close() {
        InstallIDEPluginsWindow.instance().close();
    }
}
