import { screen } from 'electron';
import contextMenu from 'electron-context-menu';
import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { ValueStream } from '@shared/stores/NewValueStream';
import { createValueStream } from '@shared/stores/ValueStream';
import { WindowFocusStream } from '@shared/stores/WindowFocusStream';

import { App } from './App';
import { HomeStateStream } from './home/<USER>';
import { SidebarState } from './MainWindowStoreTraits';
import { UserPreferencesStream } from './prefs/UserPreferencesStream';
import { InitializeShortcuts } from './ShortCut';
import { UserSettings } from './UserSettings';
import { WebviewBrowserWindow } from './webview/WebviewBrowserWindow';
import { WindowPositionCache } from './WindowPositionCache';

const MAIN_WINDOW_CLEANUP_TIME = 3 * 60000; // 3 minutes

const DEFAULT_HEIGHT = 700;
const DEFAULT_WIDTH = 620;
const MIN_HEIGHT = 500;
const MIN_WIDTH = 450;

const SIDEBAR_EXPANDED_KEY = 'SIDEBAR_EXPANDED_KEY';
const SIDEBAR_SIZE_KEY = 'SIDEBAR_SIZE_KEY';
const SIDEBAR_DEFAULT_SIZE = 325;
const SIDEBAR_MIN_SIZE = 200;
const SIDEBAR_MAX_SIZE = 400;

export class MainWindowManager {
    private static instance_: MainWindowManager | undefined;
    static get instance(): MainWindowManager {
        if (!MainWindowManager.instance_) {
            MainWindowManager.instance_ = new MainWindowManager();
        }

        return MainWindowManager.instance_;
    }

    private windowPositionCache = new WindowPositionCache();

    private _mainWindow: WebviewBrowserWindow | undefined;
    private mainWindowCleanupTimer: ReturnType<typeof setTimeout> | undefined;

    private alwaysForeground = false;

    // Only allow truly closing the main window when shutting down the app
    private allowWindowClose = false;

    // Sidebar status
    private sidebarState: SidebarState = {
        size: UserSettings.instance.getNumberSetting(SIDEBAR_SIZE_KEY) ?? SIDEBAR_DEFAULT_SIZE,
        minSize: SIDEBAR_MIN_SIZE,
        maxSize: SIDEBAR_MAX_SIZE,
        isExpanded: UserSettings.instance.getBoolSetting(SIDEBAR_EXPANDED_KEY) ?? false,
    };
    private _sidebarStream = createValueStream<SidebarState>(this.sidebarState);
    readonly sidebarStream: Stream<SidebarState> = this._sidebarStream.stream.remember();

    private lastExpansionRightAligned = false;

    private isVisible_ = new ValueStream(false);
    readonly isVisible: Stream<boolean> = this.isVisible_;

    private isFullScreen_ = new ValueStream(false);
    readonly isFullScreen: Stream<boolean> = this.isFullScreen_;

    constructor() {
        // Track changes to the 'always foreground' user preference -- when this is changed,
        // we need to restart the main window, so it starts with the correct panel state.
        UserPreferencesStream.instance()
            .stream.map((preferences) => preferences.alwaysForeground)
            .compose(dropRepeats())
            .subscribe({
                next: (alwaysForeground) => {
                    this.alwaysForeground = alwaysForeground;
                    this.recreateMainWindow();
                },
            });

        HomeStateStream.instance.stream
            .map((state) => state.$case)
            .compose(dropRepeats())
            .subscribe({
                next: (state) => {
                    switch (state) {
                        case 'configureNotifications':
                        case 'installIDEPlugins':
                        case 'tutorial':
                        case 'processing':
                            this.setOnboardingMode();
                            break;
                        default:
                            this.setMainMode();
                    }
                },
            });
    }

    get mainWindow() {
        return this._mainWindow;
    }

    public openMainWindow() {
        clearTimeout(this.mainWindowCleanupTimer);
        this.mainWindowCleanupTimer = undefined;

        const currentCursor = screen.getCursorScreenPoint();
        const targetScreen = screen.getDisplayNearestPoint(currentCursor);

        // If the window already exists, show and focus it
        if (this._mainWindow) {
            const mainWindowScreen = screen.getDisplayMatching(this._mainWindow.getBounds());
            if (mainWindowScreen.id !== targetScreen.id) {
                this.windowPositionCache.restore(this._mainWindow, this.sidebarState, targetScreen.id);
            }
            this._mainWindow.show();
        }

        // Otherwise create the window and position it correctly
        else {
            const newMainWindow = this.createMainWindow();
            this.windowPositionCache.restore(newMainWindow, this.sidebarState, targetScreen.id);
        }

        this.isVisible_.value = true;
    }

    // If the window is full-screened, exit full-screen.
    // If not, close the window
    public exitFullscreenOrClose() {
        if (this._mainWindow?.fullScreen) {
            this._mainWindow.fullScreen = false;
            return;
        }

        this.closeMainWindow();
    }

    public closeMainWindow() {
        if (this.mainWindowCleanupTimer) {
            this.mainWindowCleanupTimer = undefined;
        }

        if (this._mainWindow) {
            this.windowPositionCache.store(this._mainWindow, this.sidebarState);
        }

        this._mainWindow?.hide();
        this.mainWindowCleanupTimer = setTimeout(() => {
            const isVisible = this.mainWindow?.isVisible() ?? false;
            if (!isVisible) {
                HomeStateStream.instance.reset();
            }
        }, MAIN_WINDOW_CLEANUP_TIME);

        this.isVisible_.value = false;
    }

    private recreateMainWindow() {
        if (!this._mainWindow) {
            return;
        }

        this.allowWindowClose = true;
        this.closeMainWindow(); // Record screen coordinates and state
        this._mainWindow.close();
        this._mainWindow = undefined;
        this.allowWindowClose = false;
        this.openMainWindow();
    }

    get isFocused() {
        return this._mainWindow?.isFocused() ?? false;
    }

    private createMainWindow(): WebviewBrowserWindow {
        // Create the browser window.
        const newMainWindow = new WebviewBrowserWindow({
            windowType: { $case: 'main' },
            height: DEFAULT_HEIGHT,
            width: DEFAULT_WIDTH,
            minHeight: MIN_HEIGHT,
            minWidth: this.getMinimumWidth(),
            resizable: true,
            vibrancy: 'fullscreen-ui',

            minimizable: true,
            maximizable: true,
            fullscreenable: true,

            // Ensure app is always in the foreground, above other windows
            type: this.alwaysForeground ? 'panel' : undefined,

            // Configure title bar properties so we get the correct effect
            titleBarStyle: 'hidden',
            trafficLightPosition: { x: 18, y: 18 },
        });

        contextMenu({
            showSearchWithGoogle: false,
            showLookUpSelection: false,
        });

        // Setup event subscriptions

        InitializeShortcuts(newMainWindow);

        newMainWindow.on('resized', () => this.onResized());

        newMainWindow.on('close', (evt) => {
            if (!App.instance().allowShutdown && !this.allowWindowClose) {
                evt.preventDefault();
            }
            this.closeMainWindow();
        });

        // Forward focus state to the FocusStream
        // In normal window mode, we will track focus/blur events.  This handles both
        // window showing and hiding, and focus state,.
        if (!this.alwaysForeground) {
            newMainWindow.on('focus', () => WindowFocusStream.updateFocusedState(true));
            newMainWindow.on('blur', () => WindowFocusStream.updateFocusedState(false));
        }

        // In "always on top" mode, we will track window show/hide events.  Focus and Blur
        // events don't work correctly for always-on-top windows in MacOS, and when the window is
        // always on top, we only want to drop focus when the window is closed
        else {
            newMainWindow.on('show', () => WindowFocusStream.updateFocusedState(true));
            newMainWindow.on('hide', () => WindowFocusStream.updateFocusedState(false));
        }

        newMainWindow.on('enter-full-screen', () => (this.isFullScreen_.value = true));
        newMainWindow.on('leave-full-screen', () => (this.isFullScreen_.value = false));

        this._mainWindow = newMainWindow;
        return newMainWindow;
    }

    private onResized() {
        // When the window size changes, we need to update the maximum sidebar size so the main content won't
        // get squished
        if (this._mainWindow) {
            const maxSidebar = Math.min(this._mainWindow.getBounds().width - MIN_WIDTH, SIDEBAR_MAX_SIZE);
            this.updateSidebarState({ maxSize: maxSidebar });
        }
    }

    private updateSidebarState(updateState: Partial<SidebarState>) {
        this.sidebarState = Object.assign(this.sidebarState, updateState);
        this._sidebarStream.updateValue(this.sidebarState);
    }

    async setSidebarExpanded(expanded: boolean) {
        if (this.sidebarState.isExpanded !== expanded) {
            UserSettings.instance.setBoolSetting(SIDEBAR_EXPANDED_KEY, expanded);
            this.updateSidebarState({ isExpanded: expanded });
            this.onExpandedChanged();
        }
    }

    async setSidebarSize(size: number) {
        if (this.sidebarState.size !== size) {
            UserSettings.instance.setNumberSetting(SIDEBAR_SIZE_KEY, size);
            this.updateSidebarState({ size: size });
            this.updateMinimumWidth();
        }
    }

    toggleExpanded() {
        void this.setSidebarExpanded(!this.sidebarState.isExpanded);
    }

    setOnboardingMode() {
        void this.setSidebarExpanded(false);
        this._mainWindow?.setSize(DEFAULT_WIDTH, DEFAULT_HEIGHT);
        this._mainWindow?.setResizable(false);
    }

    setMainMode() {
        this._mainWindow?.setResizable(true);
    }

    private getMinimumWidth() {
        return this.sidebarState.isExpanded ? MIN_WIDTH + this.sidebarState.size : MIN_WIDTH;
    }

    private updateMinimumWidth() {
        this._mainWindow?.setMinimumSize(this.getMinimumWidth(), MIN_HEIGHT);
    }

    private onExpandedChanged() {
        const { isExpanded, size } = this.sidebarState;
        const mainWindow = MainWindowManager.instance.mainWindow;
        if (!mainWindow) {
            return;
        }

        // Update the minimum width to reflect the new expansion state
        this.updateMinimumWidth();

        const windowBounds = mainWindow.getBounds();
        const screenBounds = screen.getDisplayMatching(windowBounds).bounds;
        const windowRight = windowBounds.x + windowBounds.width;
        const screenRight = screenBounds.x + screenBounds.width;

        // Expanding...
        if (isExpanded) {
            // If we're expanding past the right side of the screen, expand out to the left
            if (windowRight + size > screenRight) {
                mainWindow.setBounds({ x: windowBounds.x - size, width: windowBounds.width + size }, true);

                this.lastExpansionRightAligned = true;
            }

            // ... otherwise expand out to the right
            else {
                mainWindow.setBounds({ width: windowBounds.width + size }, true);
            }
        }

        // Contracting...
        else {
            // If we previously expanded to the left, and we haven't changed location much,
            // contract the left side of the window
            if (this.lastExpansionRightAligned && windowRight > screenRight - size) {
                mainWindow.setBounds({ x: windowBounds.x + size, width: windowBounds.width - size }, true);
                this.lastExpansionRightAligned = false;
            }

            // Otherwise contract the right side of the window
            else {
                mainWindow.setBounds({ width: windowBounds.width - size }, true);
            }
        }
    }
}
