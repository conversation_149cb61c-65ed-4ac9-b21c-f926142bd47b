import { app, Menu, powerMonitor, shell } from 'electron';
import dropRepeats from 'xstream/extra/dropRepeats';

import { isDeveloperBuild } from '@shared/config';
import { HubServer } from '@shared/HubAPIServer/HubAPIServer';
import { HubAPIService } from '@shared/HubAPIServer/HubAPIService';
import { configureIDELogger } from '@shared/ide/utils/IDELogger';
import { AuthStore } from '@shared/stores/AuthStore';
import { FocusActionStream } from '@shared/stores/FocusActionStream';
import { PersonStore } from '@shared/stores/PersonStore';
import { PreAuthLoginStore } from '@shared/stores/PreAuthLoginStore';
import { TeamStore } from '@shared/stores/TeamStore';
import { ArrayUtils } from '@shared/webUtils';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';
import { DesktopAppUrls } from '@shared/webUtils/DesktopAppUrls';
import { LazyValue } from '@shared/webUtils/LazyValue';
import { logger } from '@shared/webUtils/log';

import { ConfigureAPI } from './api/BaseApi';
import { initElectronClientWorkspaceProvider } from './components/ClientWorkspace/ElectronClientWorkspace';
import { RegisterStoreProxies } from './components/ClientWorkspace/RegisterStoreProxies';
import { RegisterStreamProxies } from './components/ClientWorkspace/RegisterStreamProxies';
import { InitPlanExpiryDialog } from './components/PlanExpiryDialog/ShowPlanExpiryDialog';
import { DockController } from './DockController';
import { HomeStateStream } from './home/<USER>';
import { InitializeSelfUpdater } from './install/DesktopAppSelfUpdater';
import { InstallIDEPluginsWindow } from './install/InstallIDEPluginsWindow';
import { MainWindowManager } from './MainWindow';
import { McpUpsellWindow } from './McpUpsell/McpUpsellWindow';
import { HasCustoverStream } from './migration/HasCutoverStream';
import { InitializeUnreadNotifications } from './notifications/UnreadNotifications';
import { LogNotificationSetting } from './NotificationSettingLogger';
import { InitializeProcessingCompleteNotifications } from './onboarding/ProcessingCompleteNotificationManager';
import { PreferencesWindowManager } from './prefs/PrefsWindow';
import { UserPreferencesStream } from './prefs/UserPreferencesStream';
import { initializeGlobalLaunchShortcuts } from './ShortCut';
import { ShortcutBindingToAccelerator } from './ShortCutTypes';
import { InitializeTray } from './Tray';

const log = logger('App');

export class App {
    public static instance = LazyValue(() => new App());

    private _allowShutdown = false;
    private dockController = new DockController();

    constructor() {
        // Called when app is ready to run
        app.on('ready', async () => {
            ConfigureAPI();
            configureIDELogger('desktop');
            initElectronClientWorkspaceProvider();
            RegisterStreamProxies();
            RegisterStoreProxies();

            try {
                await AuthStore.get().setupAuth();
            } catch (e) {
                log.error(e); // Catch and ignore auth failures
            }

            InitializeTray();
            initializeGlobalLaunchShortcuts();
            InitializeSelfUpdater();
            HubServer.instance.start();
            InitializeUnreadNotifications();
            InitializeProcessingCompleteNotifications();

            const isCutover = app.commandLine.hasSwitch('cutover');
            HasCustoverStream.instance().update(isCutover);

            if (this.shouldDisplayOnStartup) {
                this.activate();
            } else {
                this.quit();
            }

            // When any app requests activation via the hub API, activate
            HubAPIService.activateClient.subscribe({
                next: () => this.activate(),
            });

            // Forward the selected team to the hub API
            // Note: this is a bit of a hack.  It would be better if the HubAPIService
            // could subscribe to the CurrentTeamStore directly to forward this state
            HomeStateStream.instance.selectedTeamIdStream.subscribe({
                next: (teamId) => HubAPIService.setSelectedTeamId(teamId),
            });

            // Show MCP upsell events
            McpUpsellWindow.instance().initialize();
        });

        // Prevent quitting through Cmd+Q, dock icon context menu, etc
        // Instead of quitting, we will appear to quit by closing the window and
        // hiding the dock icon.
        app.on('before-quit', (e) => {
            if (!this._allowShutdown) {
                e.preventDefault();
                this.quit();
            }
        });

        // When the app is activated (click on dock, focus via Cmd+tab), ensure
        // the main window is created
        app.on('activate', () => this.activate());

        // When login completes successfully, re-foreground the app.
        // Note that we only re-foreground when we log in with team memberships.  If we log in
        // and the user has no team memberships, they will need to connect teams in the dashboard,
        // so we won't foreground the app
        PreAuthLoginStore.instance().authCompletedStream.subscribe({
            next: ({ hasTeams }) => {
                if (hasTeams) {
                    this.activate();
                }
            },
        });

        // This is raised on logout, shutdown, or restart, to inform us that we need to close.
        // We will shut down immediately when this occurs.
        powerMonitor.on('shutdown', () => {
            this.shutdown();
        });

        UserPreferencesStream.instance()
            .stream.map((preferences) => ShortcutBindingToAccelerator(preferences.shortcutBinding))
            .compose(dropRepeats())
            .subscribe({ next: () => this.updateMenu() });

        this.updateMenu();

        app.on('open-url', (event, url) => this.onOpenUrl(url));
        app.setAsDefaultProtocolClient(DesktopAppUrls.protocol);

        InitPlanExpiryDialog();

        LogNotificationSetting();
    }

    private get shouldDisplayOnStartup(): boolean {
        const wasOpenedAtLogin = app.getLoginItemSettings().wasOpenedAtLogin;

        // We don't want to show on startup if the '--hide' commandline argument is provided
        // (happens during upgrade), or if we started on login.
        return !app.commandLine.hasSwitch('hide') && !wasOpenedAtLogin;
    }

    /**
     * True if the app is allowing shutdown, false otherwise.
     * This will only be true when the app is being explicitly quit via the Tray icon.
     */
    get allowShutdown(): boolean {
        return this._allowShutdown;
    }

    /**
     * Activate the app.  This will ensure the app is focused and the main window and dock are displayed.
     */
    activate() {
        app.focus({ steal: true });
        MainWindowManager.instance.openMainWindow();
    }

    /**
     * "Quit" the app.  This will close the main window and dock icon, making the app appear as if it has
     * been quit, however the system tray and all other background services will continue running.
     */
    quit() {
        MainWindowManager.instance.closeMainWindow();
    }

    /**
     * Shut down the app.  This will shut the app and all associated processes down.
     */
    shutdown() {
        this._allowShutdown = true;
        HubServer.instance.stop();
        app.quit();
    }

    private updateMenu() {
        Menu.setApplicationMenu(this.mainMenu);
    }

    private get mainMenu() {
        return Menu.buildFromTemplate([
            // Application menu
            {
                role: 'appMenu',
                submenu: [
                    { role: 'about' },

                    { type: 'separator' },

                    {
                        label: 'Install IDE Plugins',
                        click: () => InstallIDEPluginsWindow.instance().open(),
                    },

                    { type: 'separator' },

                    {
                        label: 'Settings...',
                        accelerator: 'Command+,',
                        click: () => PreferencesWindowManager.instance.open(),
                    },
                    { type: 'separator' },

                    { role: 'services' },

                    { type: 'separator' },

                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },

                    { type: 'separator' },

                    {
                        label: 'Log Out',
                        click: () => {
                            void AuthStore.get().logout('manual');
                            this.activate();
                        },
                    },

                    { role: 'quit' },
                ],
            },
            {
                role: 'fileMenu',
                submenu: [
                    {
                        label: 'Open Unblocked',
                        click: () => MainWindowManager.instance.openMainWindow(),
                        accelerator: ShortcutBindingToAccelerator(
                            UserPreferencesStream.instance().current.shortcutBinding
                        ),
                        registerAccelerator: false /* this is globally registered */,
                    },
                    {
                        label: 'Ask a Question',
                        accelerator: 'CommandOrControl+N',
                        click: () => this.askQuestion(),
                    },

                    { type: 'separator' },

                    { role: 'close' },
                ],
            },
            { role: 'editMenu' },
            {
                role: 'viewMenu',
                submenu: [
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { role: 'resetZoom' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' },
                ],
            },
            {
                label: 'Developer',
                visible: isDeveloperBuild(),
                submenu: [{ role: 'reload' }, { role: 'toggleDevTools' }],
            },
            { role: 'windowMenu' },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'Unblocked Help',
                        click: () => shell.openExternal(DashboardUrls.documentation()),
                    },
                    {
                        label: 'Contact Support',
                        click: () => shell.openExternal(DashboardUrls.indexWithIntercom()),
                    },
                ],
            },
        ]);
    }

    askQuestion() {
        HomeStateStream.instance.reset();
        setTimeout(() => FocusActionStream.instance.focus({ $case: 'messageInput' }), 10);
    }

    private async onOpenUrl(urlString: string) {
        let url: URL;
        try {
            url = new URL(urlString);
        } catch (error) {
            log.error('Error occurred handling URL', error);
            return;
        }

        const pathValues = url.pathname.split('/').filter((segment) => segment !== '');
        const action = ArrayUtils.firstOrUndefined(pathValues);

        // This is called when we complete the dashboard parts of onboarding.  At this point we have
        // completed creating the new team, and it is likely in the processing state.  We will explicitly
        // refresh our person/team listings (as these will have changed), and pop the UI to the front to
        // display the next onboarding step.
        if (action === DesktopAppUrls.continueOnboardingPath) {
            await PersonStore.refreshPerson();
            await TeamStore.refreshTeams();
            this.activate();
            return;
        }

        if (action === DesktopAppUrls.openPath) {
            this.activate();
            return;
        }
    }
}
