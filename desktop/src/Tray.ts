import { KeyboardEvent, Menu, NativeImage, nativeImage, nativeTheme, shell, Tray } from 'electron';
import path from 'path';
import dropRepeats from 'xstream/extra/dropRepeats';

import { buildCfg } from '@shared/config';
import { AllMyUnreadThreadsStream } from '@shared/stores/AllMyUnreadThreadsStream';
import { AuthStore } from '@shared/stores/AuthStore';
import { DashboardUrls } from '@shared/webUtils';

import { App } from './App';
import { InstallIDEPluginsWindow } from './install/InstallIDEPluginsWindow';
import { MainWindowManager } from './MainWindow';
import { PreferencesWindowManager } from './prefs/PrefsWindow';
import { UserPreferencesStream } from './prefs/UserPreferencesStream';
import { ShortcutBindingToAccelerator } from './ShortCutTypes';

class SystemTray {
    private tray = new Tray(this.image);
    private hasUnreads = false;

    constructor() {
        nativeTheme.on('updated', () => {
            this.updateImage();
        });

        this.tray.on('right-click', () => {
            this.showMenu();
        });

        this.tray.on('click', (e: KeyboardEvent) => {
            if (e.ctrlKey) {
                this.showMenu();
            } else {
                App.instance().activate();
            }
        });

        AllMyUnreadThreadsStream()
            .map((state) => (state.$case === 'ready' ? state.value.length > 0 : false))
            .compose(dropRepeats())
            .subscribe({
                next: (hasUnreads) => {
                    this.hasUnreads = hasUnreads;
                    this.updateImage();
                },
            });
    }

    private showMenu() {
        this.tray.popUpContextMenu(this.menu);
    }

    private updateImage() {
        this.tray.setImage(this.image);
    }

    private trayImage(baseName: string) {
        return nativeImage.createFromPath(path.join(__dirname, 'tray', baseName));
    }

    private menuImage(baseName: string) {
        return nativeImage.createFromPath(path.join(__dirname, 'tray', 'menu', baseName));
    }

    private get image(): NativeImage {
        let iconFile: string;
        // if (this.hasUnreads) {
        //     iconFile = nativeTheme.shouldUseDarkColors ? 'TrayNotifyLight.png' : 'TrayNotifyDark.png';
        // } else {
        //     iconFile = nativeTheme.shouldUseDarkColors ? 'TrayLight.png' : 'TrayDark.png';
        // }

        if (this.hasUnreads) {
            iconFile = 'TrayNotifyLight.png';
        } else {
            iconFile = 'TrayLight.png';
        }

        const trayImage = this.trayImage(iconFile);
        trayImage.setTemplateImage(true);
        return trayImage;
    }

    private get menu(): Menu {
        return Menu.buildFromTemplate([
            { label: `Unblocked Version ${buildCfg.productNumber}`, enabled: false },

            { type: 'separator' },

            {
                label: 'Open Unblocked',
                icon: this.menuImage('OpenUnblockedTemplate.png'),
                click: () => MainWindowManager.instance.openMainWindow(),
                accelerator: ShortcutBindingToAccelerator(UserPreferencesStream.instance().current.shortcutBinding),
            },

            {
                label: 'Ask a Question...',
                icon: this.menuImage('AskQuestionTemplate.png'),
                accelerator: 'CommandOrControl+N',
                click: () => App.instance().askQuestion(),
            },

            { type: 'separator' },

            // TODO: Invite Team Members...

            {
                label: 'Install IDE Plugins',
                icon: this.menuImage('InstallIDEPluginsTemplate.png'),
                click: () => InstallIDEPluginsWindow.instance().open(),
            },

            {
                label: 'Contact Support...',
                icon: this.menuImage('SupportTemplate.png'),
                click: () => shell.openExternal(DashboardUrls.indexWithIntercom()),
            },

            { type: 'separator' },

            {
                label: 'Settings...',
                icon: this.menuImage('SettingsTemplate.png'),
                accelerator: 'Command+,',
                click: () => PreferencesWindowManager.instance.open(),
            },

            { type: 'separator' },

            {
                label: 'Log Out',
                icon: this.menuImage('SignOutTemplate.png'),
                click: () => {
                    void AuthStore.get().logout('manual');
                    App.instance().activate();
                },
            },
            {
                label: 'Quit',
                icon: this.menuImage('QuitTemplate.png'),
                click: () => App.instance().shutdown(),
            },
        ]);
    }
}

export function InitializeTray() {
    new SystemTray();
}
