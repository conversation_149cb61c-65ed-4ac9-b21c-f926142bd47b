import { useCallback, useState } from 'react';

import { useStore } from '@shared/stores/useStore';
import { BadgeIcon } from '@shared/webComponents/BadgeIcon/BadgeIcon';
import { Button } from '@shared/webComponents/Button/Button';
import { Checkbox } from '@shared/webComponents/Checkbox/Checkbox';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { DashboardUrls } from '@shared/webUtils';

import { faRobot } from '@fortawesome/pro-duotone-svg-icons/faRobot';
import { faArrowUpRightFromSquare } from '@fortawesome/pro-solid-svg-icons/faArrowUpRightFromSquare';

import { McpUpsellTraits } from './McpUpsellTypes';

import './McpUpsell.scss';

interface Props {
    teamId: string;
}

export function McpUpsell({ teamId }: Props) {
    const store = useStore(McpUpsellTraits, {});

    const viewUpsellDlg = useCallback(
        () =>
            ClientWorkspace.instance().handleAction({
                $case: 'openUrl',
                url: DashboardUrls.orgPlans(teamId),
            }),
        [teamId]
    );

    const [dontShow, setDontShow] = useState(false);

    const toggleDontShow = useCallback(() => {
        const newValue = !dontShow;
        setDontShow(newValue);
        void store.setDontShowAgain(newValue);
    }, [dontShow, store]);

    return (
        <div className="mcp_upsell">
            <BadgeIcon icon={faRobot} />
            <h1>Want to use Unblocked MCP?</h1>
            <p>
                Your team is on the Legacy plan, which does not support Unblocked&apos;s MCP tools. Upgrade to a
                Business or Enterprise plan to use Unblocked MCP.
            </p>

            <Button className="upsell_dialog__button" onClick={viewUpsellDlg}>
                View all plans and features
                <Icon className="upsell_dialog__external_icon" icon={faArrowUpRightFromSquare} size="xxSmall" />
            </Button>

            <Button variant="secondary" onClick={() => store.closeWindow()}>
                Cancel
            </Button>

            <Checkbox isChecked={dontShow} onClick={toggleDontShow}>
                Don&apos;t ask again
            </Checkbox>
        </div>
    );
}
