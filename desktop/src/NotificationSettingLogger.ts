import { Stream } from 'xstream';

import { PersonStore } from '@shared/stores/PersonStore';
import { UserSettingsStore } from '@shared/stores/UserSettingsStore';

import { NotificationSetupStream } from './onboarding/NotificationSetupStream';

export function LogNotificationSetting() {
    Stream.combine(
        PersonStore.stream,
        UserSettingsStore.instance().stream,
        NotificationSetupStream.instance().stream
    ).subscribe({
        next: ([personState, userSettingsState, notificationState]) => {
            // Wait until user has completed onboarding (which includes notification setup),
            // and wait until we've loaded the user settings and notification settings
            if (
                personState.$case !== 'loaded' ||
                !personState.person.hasSeenTutorial ||
                userSettingsState.$case === 'loading' ||
                notificationState.$case === 'loading'
            ) {
                return;
            }

            // If the user's service-stored enableSystemNotifications doesn't match our detected
            // local notification setting, update it now
            if (userSettingsState.value.enableSystemNotifications !== notificationState.isEnabled) {
                void UserSettingsStore.instance().update({ enableSystemNotifications: notificationState.isEnabled });
            }
        },
    });
}
