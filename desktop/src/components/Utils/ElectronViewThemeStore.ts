import { nativeTheme } from 'electron';

import { ViewThemeStore } from '@shared/webComponents/View/ViewThemeStore';
import { ViewTheme } from '@shared/webComponents/View/ViewThemeTypes';
import { LazyValue } from '@shared/webUtils';

export class ElectronViewThemeStore extends ViewThemeStore {
    static get = LazyValue(() => new ElectronViewThemeStore());
    private themeChangeListener: () => void;
    constructor() {
        const getCurrentTheme = (): ViewTheme => {
            return nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
        };
        // Initialize with current theme
        super(getCurrentTheme());
        this.themeChangeListener = () => {
            void this.setTheme(getCurrentTheme());
        };
        nativeTheme.on('updated', this.themeChangeListener);
    }
}
