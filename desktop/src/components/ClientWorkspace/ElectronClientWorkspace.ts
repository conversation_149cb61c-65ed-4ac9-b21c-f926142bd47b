import { dialog, Menu, MenuItemConstructorOptions, shell } from 'electron';

import {
    ContextmenuItemType,
    ContextMenuRequest,
    ContextMenuResponse,
    DialogRequest,
    DialogResponse,
    NotificationResponse,
} from '@shared/api/generatedExtraApi';

import { App } from '@desktop/App';
import { ElectronTokenProvider } from '@desktop/auth/ElectronTokenProvider';
import { HomeStateStream } from '@desktop/home/<USER>';
import { MainWindowManager } from '@desktop/MainWindow';
import { UserSettings } from '@desktop/UserSettings';
import { StoreProxyRegistry } from '@shared/proxy/StoreProxy/StoreProxyRegistry';
import { StreamProxyRegistry } from '@shared/proxy/StreamProxy/StreamProxyRegistry';
import {
    StreamProxyCommand,
    StreamProxyData,
    StreamProxyKey,
    StreamProxyTraits,
} from '@shared/proxy/StreamProxy/StreamProxyTypes';
import { InviteeStreams } from '@shared/stores/InviteeStore';
import { updateThreadUnread } from '@shared/stores/UnreadStore';
import {
    ClientWorkspace,
    ClientWorkspaceAction,
    ClientWorkspaceProvider,
} from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { ArrayUtils, DashboardUrls } from '@shared/webUtils';
import { SyncHighlightJsService } from '@shared/webUtils/SyntaxHighlighting/SyncHighlightJsService';

export class ElectronClientWorkspace implements ClientWorkspaceProvider {
    syntaxHighlightService = new SyncHighlightJsService();
    tokenProvider = ElectronTokenProvider;
    localContextProvider = undefined;
    storeProvider = StoreProxyRegistry.instance();

    async handleAction(action: ClientWorkspaceAction) {
        switch (action.$case) {
            case 'openThread':
                HomeStateStream.instance.selectThread(action.threadId);
                break;
            case 'openThreadReference':
                if (action.reference.provider === 'unblocked') {
                    HomeStateStream.instance.selectTeam(action.teamId);
                    HomeStateStream.instance.selectThread(action.reference.id);
                } else {
                    await shell.openExternal(action.reference.externalUrl ?? action.reference.dashboardUrl);
                }
                break;
            case 'updateThreadUnread':
                await updateThreadUnread(action.teamId, action.threadId, action.updateThreadRequest);
                break;
            case 'openPrReference':
                await shell.openExternal(action.reference.externalUrl ?? action.reference.dashboardUrl);
                break;
            case 'openSourceFileReference':
                await shell.openExternal(action.reference.externalUrl);
                break;
            case 'openUrl':
                await shell.openExternal(action.url);
                break;
            case 'emailInvitees':
                await InviteeStreams.invite(action.teamId, action.emailInvites);
                break;
            case 'openAskQuestion':
                App.instance().askQuestion();
                break;
            case 'contactSupport':
                await shell.openExternal(DashboardUrls.indexWithIntercom());
                break;
            case 'launchInstallation':
                console.log('Unsupported action', action.$case);
                break;
        }
    }

    async setLocalStorage(key: string, value: string) {
        UserSettings.instance.setSetting(key, value);
    }

    async getLocalStorage(key: string): Promise<string | undefined> {
        return UserSettings.instance.getSetting(key);
    }

    async removeLocalStorage(key: string): Promise<void> {
        UserSettings.instance.deleteSetting(key);
    }

    async notify(): Promise<NotificationResponse> {
        return Promise.resolve({});
    }

    contextMenuProvider = {
        menu: async (request: ContextMenuRequest): Promise<ContextMenuResponse> => {
            return new Promise((resolve) => {
                const items = request.items.map((item): MenuItemConstructorOptions => {
                    const itemType =
                        item.itemType === ContextmenuItemType.UnknownDefaultOpenApi ? undefined : item.itemType;
                    return {
                        label: item.title,
                        type: itemType,
                        click: () => {
                            resolve({
                                responseId: item.id,
                            });
                        },
                    };
                });

                const contextMenu = Menu.buildFromTemplate(items);

                const positionOptions =
                    !!request.xPosition && !!request.yPosition
                        ? { x: Math.round(request.xPosition), y: Math.round(request.yPosition) }
                        : undefined;
                try {
                    contextMenu.popup({
                        ...positionOptions,

                        // When menu closes, resolve the promise
                        callback: () => resolve({}),
                    });
                } catch (e) {
                    console.error('failed to open menu', e);
                    resolve({});
                }
            });
        },
    };

    async dialog(request: DialogRequest): Promise<DialogResponse> {
        const buttons = ArrayUtils.compact([request.okTitle ?? 'OK', request.cancelTitle]);
        const mainWindow = MainWindowManager.instance.mainWindow;
        const showMessageBox = mainWindow
            ? (options: Electron.MessageBoxOptions) => dialog.showMessageBox(mainWindow, options)
            : (options: Electron.MessageBoxOptions) => dialog.showMessageBox(options);
        const dialogResponse = await showMessageBox({
            message: request.title,
            detail: request.description,
            type: 'info',
            buttons,
        });
        if (dialogResponse.response === 0) {
            return {
                response: 'ok',
            };
        }
        return {
            response: 'cancel',
        };
    }

    getStream<Key extends StreamProxyKey, Data extends StreamProxyData, Command extends StreamProxyCommand>(
        traits: StreamProxyTraits<Key, Data>,
        key: Key
    ) {
        return StreamProxyRegistry.instance.getStream<Key, Data, Command>(key);
    }
}

export function initElectronClientWorkspaceProvider() {
    ClientWorkspace.instance().setProvider(new ElectronClientWorkspace());
}
