import { shell } from 'electron';

import { HomeStateStream } from '@desktop/home/<USER>';
import { InstallIDEPluginsWindow } from '@desktop/install/InstallIDEPluginsWindow';
import { MainWindowManager } from '@desktop/MainWindow';
import { ConnectTeamStream } from '@desktop/onboarding/ConnectTeamStream';
import { ConnectTeamStreamTraits } from '@desktop/onboarding/ConnectTeamTypes';
import { NotificationSetupStream } from '@desktop/onboarding/NotificationSetupStream';
import { PreferencesWindowManager } from '@desktop/prefs/PrefsWindow';
import { UserPreferencesStream } from '@desktop/prefs/UserPreferencesStream';
import { UserPreferencesStreamTraits } from '@desktop/prefs/UserPreferencesStreamTypes';
import { StreamProxyRegistry } from '@shared/proxy/StreamProxy/StreamProxyRegistry';
import { AuthStore } from '@shared/stores/AuthStore';
import { MyDiscussionThreadStore } from '@shared/stores/MyDiscussionThreadStore';
import { RegisterStreamProxies as SharedRegisteredStreamProxies } from '@shared/stores/RegisterStreamProxies';
import { UnreadThreadListFacade } from '@shared/stores/UnreadThreadListFacade';
import { DashboardUrls } from '@shared/webUtils';

import { AuthStreamTraits } from './AuthStreamTraits';
import { HomeStreamTraits } from './HomeStreamTraits';
import { MyQuestionsStreamTraits } from './MyQuestionsStreamTraits';
import { NotificationSetupStreamTraits } from './NotificationSetupStreamTraits';

export function RegisterStreamProxies() {
    SharedRegisteredStreamProxies();
    StreamProxyRegistry.instance.register('homeState', HomeStreamTraits, (key, regFn) => {
        const store = HomeStateStream.instance;
        regFn((command) => {
            switch (command.$case) {
                case 'selectTeam':
                    store.selectTeam(command.teamId);
                    break;
                case 'selectThread':
                    if (!command.threadId) {
                        store.selectThread(undefined);
                        break;
                    }
                    store.selectThread(command.threadId);
                    break;
                case 'launchPreferences':
                    PreferencesWindowManager.instance.open();
                    break;
                case 'installIdePlugins':
                    InstallIDEPluginsWindow.instance().open();
                    break;
                case 'contactSupport':
                    void shell.openExternal(DashboardUrls.indexWithIntercom());
                    break;
                case 'closeWindow':
                    MainWindowManager.instance.closeMainWindow();
                    break;
                case 'finishTutorial':
                    void store.finishTutorial();
                    break;
                case 'logout':
                    void AuthStore.get().logout('manual');
                    break;
            }
        });
        return store.stream;
    });

    StreamProxyRegistry.instance.register('myQuestions', MyQuestionsStreamTraits, (key) => {
        const unfilteredStore = MyDiscussionThreadStore.get(key.teamId);

        if (!key.unreadsOnly) {
            return unfilteredStore.stream;
        }

        const filteredStore = new UnreadThreadListFacade(unfilteredStore);
        return filteredStore.stream;
    });

    StreamProxyRegistry.instance.register('auth', AuthStreamTraits, (key, regFn) => {
        regFn((command) => {
            switch (command.$case) {
                case 'logout':
                    void AuthStore.get().logout('manual');
                    break;
            }
        });
        return AuthStore.get().stream;
    });

    StreamProxyRegistry.instance.register('userPreferences', UserPreferencesStreamTraits, (key, regFn) => {
        regFn((command) => UserPreferencesStream.instance().processCommand(command));
        return UserPreferencesStream.instance().stream;
    });

    StreamProxyRegistry.instance.register('notificationSetup', NotificationSetupStreamTraits, (key, regFn) => {
        regFn((command) => NotificationSetupStream.instance().processCommand(command));
        return NotificationSetupStream.instance().stream;
    });

    StreamProxyRegistry.instance.register('connectTeam', ConnectTeamStreamTraits, () => {
        return ConnectTeamStream.instance().stream;
    });
}
