import { BrowserWindow, Event, globalShortcut, Input, nativeTheme } from 'electron';
import equal from 'fast-deep-equal';
import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { FocusActionStream } from '@shared/stores/FocusActionStream';

import { App } from './App';
import { HomeStateStream } from './home/<USER>';
import { MainWindowManager } from './MainWindow';
import { UserPreferencesStream } from './prefs/UserPreferencesStream';
import {
    ExitShortcut,
    NewChatShortcut,
    Shortcut,
    ShortcutBindingToAccelerator,
    ThemeShortcut,
    ToggleSidebarShortcut,
} from './ShortCutTypes';

// Shortcuts that are triggered when window has focus.
interface LocalShortcutAction {
    shortCut: Shortcut;
    shouldTrigger: (input: Input) => void;
    trigger: (event: Event) => void;
}

type GlobalLaunchShorcutBehaviour = 'toggle' | 'tutorial';

export const initializeGlobalLaunchShortcuts = () => {
    let lastAccelerator: string | undefined;

    const prefStream: Stream<string | undefined> = UserPreferencesStream.instance()
        .stream.map((preferences) => ShortcutBindingToAccelerator(preferences.shortcutBinding))
        .compose(dropRepeats(equal));

    const homeStateStream = HomeStateStream.instance.stream.map((v) => v.$case).compose(dropRepeats());

    Stream.combine(prefStream, homeStateStream).subscribe({
        next: ([accelerator, homeState]) => {
            const behaviour: GlobalLaunchShorcutBehaviour = homeState === 'tutorial' ? 'tutorial' : 'toggle';
            updateGlobalLaunchShortcut(accelerator, lastAccelerator, behaviour);
            lastAccelerator = accelerator;
        },
    });
};

const updateGlobalLaunchShortcut = (
    newAccelerator: string | undefined,
    oldAccelerator: string | undefined,
    behaviour: GlobalLaunchShorcutBehaviour
) => {
    // Temporarily just unregistering all instead of keeping track of existing shortcuts
    if (oldAccelerator) {
        globalShortcut.unregister(oldAccelerator);
    }

    if (newAccelerator) {
        globalShortcut.register(newAccelerator, () => {
            if (behaviour === 'tutorial') {
                void HomeStateStream.instance.finishTutorial();
                return;
            }

            if (MainWindowManager.instance.isFocused) {
                MainWindowManager.instance.exitFullscreenOrClose();
            } else {
                App.instance().activate();
                FocusActionStream.instance.focus({ $case: 'messageInput' });
            }
        });
    }
};

const NewChatShortcutAction: LocalShortcutAction = {
    shortCut: NewChatShortcut,
    shouldTrigger: (input: Input) => input.meta && input.key.toLocaleLowerCase() === 'n',
    trigger: () => App.instance().askQuestion(),
};

const ThemeShortcutAction: LocalShortcutAction = {
    shortCut: ThemeShortcut,
    shouldTrigger: (input: Input) => input.meta && input.shift && input.key.toLocaleLowerCase() === 't',
    trigger: (event) => {
        UserPreferencesStream.instance().processCommand({
            $case: 'setNativeThemeSource',
            themeSource: nativeTheme.shouldUseDarkColors ? 'light' : 'dark',
        });
        event.preventDefault();
    },
};

const ToggleSidebarShortcutAction: LocalShortcutAction = {
    shortCut: ToggleSidebarShortcut,
    shouldTrigger: (input: Input) => input.meta && input.control && input.key.toLocaleLowerCase() === 's',
    trigger: () => {
        MainWindowManager.instance.toggleExpanded();
    },
};

const ExitShortcutAction: LocalShortcutAction = {
    shortCut: ExitShortcut,
    shouldTrigger: (input: Input) => input.key === 'Escape',
    trigger: (event) => {
        event.preventDefault();
        MainWindowManager.instance.exitFullscreenOrClose();
    },
};

const LocalShortCuts = [NewChatShortcutAction, ThemeShortcutAction, ToggleSidebarShortcutAction, ExitShortcutAction];

export const InitializeShortcuts = (browserWindow: BrowserWindow) => {
    browserWindow.webContents.on('before-input-event', (event, input) => {
        const targetAction = LocalShortCuts.find((v) => v.shouldTrigger(input));
        targetAction?.trigger(event);
    });
};
