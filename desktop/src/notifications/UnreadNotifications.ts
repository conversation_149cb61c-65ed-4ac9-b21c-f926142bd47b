import dayjs from 'dayjs';

import { ThreadInfoAggregate } from '@shared/api/generatedExtraApi';

import { HomeStateStream } from '@desktop/home/<USER>';
import { MainWindowManager } from '@desktop/MainWindow';
import { UserSettings } from '@desktop/UserSettings';
import { AllMyUnreadThreadsStream } from '@shared/stores/AllMyUnreadThreadsStream';
import { sortThreadsBase } from '@shared/stores/ThreadListStoreTypes';
import { RenderBlocksToText } from '@shared/webComponents/MessageView/MessageTextRenderer';
import { ArrayUtils, logger, MessageTransformer } from '@shared/webUtils';

import { DesktopNotifications } from './DesktopNotifications';

const LastSentKey = 'LAST_SENT';
const log = logger('DesktopNotifications');
class UnreadNotifications {
    lastSent: Date | undefined;

    constructor() {
        /**
         * If there's not last date, this should be the first time the user is getting unread notifications.
         * Set to current date so we do not send a large backlog of unread notifications
         */
        this.lastSent = UserSettings.instance.getDateSetting(LastSentKey) ?? new Date();

        AllMyUnreadThreadsStream().subscribe({
            next: (state) => {
                if (state.$case === 'loading') {
                    return;
                }

                let notifyUnreads: ThreadInfoAggregate[];

                const sorted = state.value.sort(sortThreadsBase);

                // Data from AllMyUnreadThreadsStream should already be sorted by `UnreadThreadSort` which sorts by lastMessageCreatedAt
                if (!this.lastSent) {
                    notifyUnreads = sorted;
                } else {
                    notifyUnreads = sorted.filter((v) => dayjs(v.thread.lastMessageCreatedAt).isAfter(this.lastSent));
                }

                const latestNotification = ArrayUtils.firstOrUndefined(sorted);

                const sendNotification = async (thread: ThreadInfoAggregate): Promise<void> => {
                    const title = thread.pullRequest?.title ?? thread.thread.title;

                    const messageContent = ArrayUtils.lastOrUndefined(thread.messages)?.messageContent;
                    const body = messageContent
                        ? RenderBlocksToText(
                              MessageTransformer.fromBytesToMessage(messageContent.content).blocks
                          ).trim()
                        : undefined;

                    await DesktopNotifications.instance.sendNotification(title, body, () => {
                        MainWindowManager.instance.openMainWindow();
                        HomeStateStream.instance.selectTeam(thread.thread.teamId);
                        HomeStateStream.instance.selectThread(thread.thread.id);
                    });
                };

                notifyUnreads.forEach((v) => {
                    try {
                        void sendNotification(v);
                    } catch (e) {
                        log.error('Failed to send notification', v.thread.id, e);
                    }
                });
                if (latestNotification) {
                    this.updateLastSent(latestNotification.thread.lastMessageCreatedAt);
                }
            },
        });
    }

    private updateLastSent(date: Date) {
        this.lastSent = date;
        UserSettings.instance.setDateSetting(LastSentKey, date);
    }
}

export function InitializeUnreadNotifications() {
    new UnreadNotifications();
}
