package com.nextchaptersoftware.documents.mongo.scm.bitbucket.models

import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import com.nextchaptersoftware.documents.schema.SchemaDoc
import io.ktor.http.Url
import kotlin.time.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.bson.types.ObjectId

/**
 * Container document for storing Bitbucket PRs in MongoDB,
 * enriched with metadata and comments.
 */
@SchemaDoc(description = "Bitbucket pull request container document stored in 'bitbucket_pull_requests' collection")
@Serializable
data class BitbucketPullRequestContainerDocument(
    @SchemaDoc(description = "MongoDB ObjectId for this document")
    @Contextual
    @SerialName("_id") val mongoId: ObjectId = ObjectId(),

    @SchemaDoc(description = "Document metadata containing tenant, collection, and group identifiers")
    @SerialName("metadata") val metadata: MetadataDocument,

    @SchemaDoc(description = "The pull request data matching Bitbucket API response structure")
    @SerialName("pull_request") val pullRequest: BitbucketPullRequestDocument,

    @SchemaDoc(description = "All pull request comments including general and inline comments")
    @SerialName("comments") val comments: List<BitbucketPullRequestCommentDocument> = emptyList(),
)

/* =========================================================
 * Pull Request
 * ========================================================= */

@Serializable
data class BitbucketPullRequestDocument(
    // Identity
    @SerialName("id") val id: Long,
    @SerialName("title") val title: String,
    @SerialName("description") val description: String? = null,

    // State
    @SerialName("state") val state: BitbucketPrStateDocument,
    @SerialName("draft") val draft: Boolean? = null,

    // Author
    @SerialName("author") val author: BitbucketUserDocument? = null,

    // Timing
    @Contextual @SerialName("created_on") val createdOn: Instant,
    @Contextual @SerialName("updated_on") val updatedOn: Instant,

    // Branch info
    @SerialName("source") val source: BitbucketBranchRefDocument,
    @SerialName("destination") val destination: BitbucketBranchRefDocument,

    // Reviewers & participants (approval state lives on participants)
    @SerialName("reviewers") val reviewers: List<BitbucketUserDocument> = emptyList(),
    @SerialName("participants") val participants: List<BitbucketParticipantDocument> = emptyList(),

    // Merge info
    @SerialName("merge_commit") val mergeCommit: BitbucketMergeCommitDocument? = null,

    // Links
    @SerialName("links") val links: BitbucketLinksDocument,

    // Counts (from API)
    @SerialName("comment_count") val commentCount: Int? = null,
    @SerialName("task_count") val taskCount: Int? = null,

    // Additional fields
    @SerialName("close_source_branch") val closeSourceBranch: Boolean? = null,
    @SerialName("closed_by") val closedBy: BitbucketUserDocument? = null,
    @SerialName("reason") val reason: String? = null,
)

@Serializable
enum class BitbucketPrStateDocument {
    @SerialName("OPEN")
    OPEN,

    @SerialName("MERGED")
    MERGED,

    @SerialName("DECLINED")
    DECLINED,

    @SerialName("SUPERSEDED")
    SUPERSEDED,
}

/* =========================================================
 * Comments (single model; inline is optional)
 * ========================================================= */

@Serializable
data class BitbucketPullRequestCommentDocument(
    @SerialName("id") val id: Long,
    @SerialName("content") val content: BitbucketContentDocument,
    @SerialName("user") val user: BitbucketUserDocument,
    @Contextual @SerialName("created_on") val createdOn: Instant,
    @Contextual @SerialName("updated_on") val updatedOn: Instant,
    @SerialName("links") val links: BitbucketCommentLinksDocument,

    // Inline comment info (for file/line-level comments)
    @SerialName("inline") val inline: BitbucketInlineDocument? = null,

    // Parent comment (for threaded comments)
    @SerialName("parent") val parent: BitbucketCommentParentDocument? = null,

    // Pull request reference (as returned by some endpoints)
    @SerialName("pullrequest") val pullrequest: BitbucketCommentPullRequestDocument? = null,
)

@Serializable
data class BitbucketContentDocument(
    @SerialName("raw") val raw: String,
    @SerialName("markup") val markup: String? = null,
    @SerialName("html") val html: String? = null,
    @SerialName("type") val type: String? = null,
)

@Serializable
data class BitbucketInlineDocument(
    @SerialName("from") val from: Int? = null, // left-side line number
    @SerialName("to") val to: Int? = null, // right-side line number
    @SerialName("path") val path: String,
)

@Serializable
data class BitbucketCommentParentDocument(
    @SerialName("id") val id: Long,
)

@Serializable
data class BitbucketCommentPullRequestDocument(
    @SerialName("source") val source: BitbucketCommentSourceDocument? = null,
) {
    @Serializable
    data class BitbucketCommentSourceDocument(
        @SerialName("commit") val commit: BitbucketCommentCommitDocument,
    ) {
        @Serializable
        data class BitbucketCommentCommitDocument(
            @SerialName("hash") val hash: String,
        )
    }
}

/* =========================================================
 * Participants (approval state lives here)
 * ========================================================= */

@Serializable
data class BitbucketParticipantDocument(
    @SerialName("user") val user: BitbucketUserDocument,
    @SerialName("approved") val approved: Boolean? = null,
    @SerialName("role") val role: String? = null, // e.g., "REVIEWER"
)

/* =========================================================
 * Shared sub-docs
 * ========================================================= */

@Serializable
data class BitbucketBranchRefDocument(
    @SerialName("branch") val branch: BitbucketBranchDocument,
    @SerialName("commit") val commit: BitbucketCommitDocument,
    @SerialName("repository") val repository: BitbucketRepoDocument? = null,
) {
    @Serializable
    data class BitbucketBranchDocument(
        @SerialName("name") val name: String,
        @SerialName("links") val links: BitbucketLinksDocument? = null,
    )

    @Serializable
    data class BitbucketCommitDocument(
        @SerialName("hash") val hash: String,
        @SerialName("links") val links: BitbucketCommitLinksDocument? = null,
        @SerialName("type") val type: String? = null,
    )
}

@Serializable
data class BitbucketMergeCommitDocument(
    @SerialName("hash") val hash: String,
    @SerialName("links") val links: BitbucketCommitLinksDocument,
)

@Serializable
data class BitbucketRepoDocument(
    @SerialName("type") val type: String? = null,
    @SerialName("full_name") val fullName: String,
    @SerialName("name") val name: String,
    @SerialName("uuid") val uuid: String? = null,
    @SerialName("links") val links: BitbucketRepoLinksDocument? = null,
)

@Serializable
data class BitbucketUserDocument(
    @SerialName("uuid") val uuid: String,
    @SerialName("account_id") val accountId: String? = null,
    @SerialName("nickname") val nickname: String? = null,
    @SerialName("display_name") val displayName: String,
    @SerialName("type") val type: String,
    @SerialName("links") val links: BitbucketUserLinksDocument,
)

/* =========================================================
 * Links structures
 * ========================================================= */

@Serializable
data class BitbucketLinksDocument(
    @SerialName("self") val self: BitbucketLinkDocument,
    @SerialName("html") val html: BitbucketLinkDocument,
    @SerialName("diff") val diff: BitbucketLinkDocument? = null,
    @SerialName("diffstat") val diffstat: BitbucketLinkDocument? = null,
    @SerialName("comments") val comments: BitbucketLinkDocument? = null,
    @SerialName("activity") val activity: BitbucketLinkDocument? = null,
    @SerialName("merge") val merge: BitbucketLinkDocument? = null,
    @SerialName("decline") val decline: BitbucketLinkDocument? = null,
    @SerialName("statuses") val statuses: BitbucketLinkDocument? = null,
)

@Serializable
data class BitbucketCommentLinksDocument(
    @SerialName("self") val self: BitbucketLinkDocument,
    @SerialName("html") val html: BitbucketLinkDocument,
    @SerialName("code") val code: BitbucketLinkDocument? = null,
)

@Serializable
data class BitbucketCommitLinksDocument(
    @SerialName("self") val self: BitbucketLinkDocument,
    @SerialName("html") val html: BitbucketLinkDocument,
)

@Serializable
data class BitbucketRepoLinksDocument(
    @SerialName("self") val self: BitbucketLinkDocument,
    @SerialName("html") val html: BitbucketLinkDocument,
    @SerialName("avatar") val avatar: BitbucketLinkDocument? = null,
)

@Serializable
data class BitbucketUserLinksDocument(
    @SerialName("self") val self: BitbucketLinkDocument,
    @SerialName("html") val html: BitbucketLinkDocument,
    @SerialName("avatar") val avatar: BitbucketLinkDocument,
)

@Serializable
data class BitbucketLinkDocument(
    @Contextual @SerialName("href") val href: Url,
)
