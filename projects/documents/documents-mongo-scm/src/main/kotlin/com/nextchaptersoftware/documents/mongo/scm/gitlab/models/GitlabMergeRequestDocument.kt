package com.nextchaptersoftware.documents.mongo.scm.gitlab.models

import com.nextchaptersoftware.documents.mongo.core.models.MetadataDocument
import com.nextchaptersoftware.documents.schema.SchemaDoc
import kotlin.time.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.bson.types.ObjectId

/**
 * Container document for storing GitLab merge requests in MongoDB,
 * enriched with metadata and notes (comments).
 *
 * Uses kotlin.time.Instant with @Contextual — make sure your serialization module
 * registers a contextual serializer for Instant.
 */
@SchemaDoc(description = "GitLab merge request container document stored in 'gitlab_merge_requests' collection")
@Serializable
data class GitlabMergeRequestContainerDocument(
    @SchemaDoc(description = "MongoDB ObjectId for this document")
    @Contextual
    @SerialName("_id") val mongoId: ObjectId = ObjectId(),

    @SchemaDoc(description = "Document metadata containing tenant, collection, and group identifiers")
    @SerialName("metadata") val metadata: MetadataDocument,

    @SchemaDoc(description = "The merge request data matching GitLab API response structure")
    @SerialName("merge_request") val mergeRequest: GitlabMergeRequestDocument,

    @SchemaDoc(description = "All merge request notes and comments including plain notes and discussion notes")
    @SerialName("notes") val notes: List<GitlabMergeRequestNoteDocument> = emptyList(),
)

/**
 * GitLab merge request document (API v4).
 * Fields mirror the REST response; many are optional across versions/editions.
 */
@Serializable
data class GitlabMergeRequestDocument(
    // Identity
    @SerialName("id") val id: Long,
    @SerialName("iid") val iid: Long, // Internal ID (MR number in project)
    @SerialName("project_id") val projectId: Long,
    @SerialName("title") val title: String,
    @SerialName("description") val description: String? = null,

    // State
    @SerialName("state") val state: GitlabMergeRequestStateDocument,
    @SerialName("draft") val draft: Boolean? = null, // preferred over work_in_progress
    @SerialName("work_in_progress") val workInProgress: Boolean? = null, // deprecated alias

    // Author
    @SerialName("author") val author: GitlabUserDocument,

    // Timing
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,
    @Contextual @SerialName("merged_at") val mergedAt: Instant? = null,
    @Contextual @SerialName("closed_at") val closedAt: Instant? = null,
    @Contextual @SerialName("prepared_at") val preparedAt: Instant? = null,
    @Contextual @SerialName("merge_after") val mergeAfter: Instant? = null,

    // Branch info
    @SerialName("source_branch") val sourceBranch: String,
    @SerialName("target_branch") val targetBranch: String,
    @SerialName("source_project_id") val sourceProjectId: Long,
    @SerialName("target_project_id") val targetProjectId: Long,

    // Assignees & reviewers
    @SerialName("assignee") val assignee: GitlabUserDocument? = null, // legacy "first assignee"
    @SerialName("assignees") val assignees: List<GitlabUserDocument> = emptyList(),
    @SerialName("reviewers") val reviewers: List<GitlabUserDocument> = emptyList(),

    // Merge info
    @SerialName("merge_user") val mergeUser: GitlabUserDocument? = null,
    @SerialName("merged_by") val mergedBy: GitlabUserDocument? = null, // deprecated; prefer merge_user
    @SerialName("closed_by") val closedBy: GitlabUserDocument? = null,

    // Status
    @SerialName("merge_status") val mergeStatus: GitlabMergeStatusDocument? = null, // deprecated; prefer detailed_merge_status
    @SerialName("detailed_merge_status") val detailedMergeStatus: GitlabDetailedMergeStatusDocument? = null,
    @SerialName("merge_when_pipeline_succeeds") val mergeWhenPipelineSucceeds: Boolean? = null,

    // Commits
    @SerialName("sha") val sha: String,
    @SerialName("merge_commit_sha") val mergeCommitSha: String? = null,
    @SerialName("squash_commit_sha") val squashCommitSha: String? = null,

    // Voting
    @SerialName("upvotes") val upvotes: Int = 0,
    @SerialName("downvotes") val downvotes: Int = 0,

    // Labels & milestone
    @SerialName("labels") val labels: List<String> = emptyList(),
    @SerialName("milestone") val milestone: GitlabMilestoneDocument? = null,

    // Settings
    @SerialName("should_remove_source_branch") val shouldRemoveSourceBranch: Boolean? = null,
    @SerialName("force_remove_source_branch") val forceRemoveSourceBranch: Boolean? = null,
    @SerialName("allow_collaboration") val allowCollaboration: Boolean? = null,
    @SerialName("allow_maintainer_to_push") val allowMaintainerToPush: Boolean? = null,
    @SerialName("squash") val squash: Boolean? = null,
    @SerialName("squash_on_merge") val squashOnMerge: Boolean? = null,

    // Counts
    @SerialName("user_notes_count") val userNotesCount: Int? = null,
    @SerialName("changes_count") val changesCount: String? = null,

    // Discussion & conflicts
    @SerialName("discussion_locked") val discussionLocked: Boolean? = null,
    @SerialName("has_conflicts") val hasConflicts: Boolean? = null,
    @SerialName("blocking_discussions_resolved") val blockingDiscussionsResolved: Boolean? = null,

    // Links & references
    @SerialName("web_url") val webUrl: String,
    @SerialName("references") val references: GitlabReferencesDocument? = null,

    // Time tracking
    @SerialName("time_stats") val timeStats: GitlabTimeStatsDocument? = null,

    // Task completion
    @SerialName("task_completion_status") val taskCompletionStatus: GitlabTaskCompletionStatusDocument? = null,

    // Import info
    @SerialName("imported") val imported: Boolean? = null,
    @SerialName("imported_from") val importedFrom: String? = null,
)

@Serializable
enum class GitlabMergeRequestStateDocument {
    @SerialName("opened")
    OPENED,

    @SerialName("closed")
    CLOSED,

    @SerialName("merged")
    MERGED,

    @SerialName("locked")
    LOCKED,
}

@Serializable
enum class GitlabMergeStatusDocument {
    @SerialName("unchecked")
    UNCHECKED,

    @SerialName("checking")
    CHECKING,

    @SerialName("can_be_merged")
    CAN_BE_MERGED,

    @SerialName("cannot_be_merged")
    CANNOT_BE_MERGED,

    @SerialName("cannot_be_merged_recheck")
    CANNOT_BE_MERGED_RECHECK,
}

@Serializable
enum class GitlabDetailedMergeStatusDocument {
    @SerialName("mergeable")
    MERGEABLE,

    @SerialName("not_approved")
    NOT_APPROVED,

    @SerialName("not_open")
    NOT_OPEN,

    @SerialName("policies_denied")
    POLICIES_DENIED,

    @SerialName("merge_time")
    MERGE_TIME,

    @SerialName("need_rebase")
    NEED_REBASE,

    @SerialName("cannot_be_merged")
    CANNOT_BE_MERGED,

    @SerialName("checking")
    CHECKING,

    @SerialName("unchecked")
    UNCHECKED,

    @SerialName("blocked_status")
    BLOCKED_STATUS,

    @SerialName("ci_must_pass")
    CI_MUST_PASS,

    @SerialName("ci_still_running")
    CI_STILL_RUNNING,

    @SerialName("discussions_not_resolved")
    DISCUSSIONS_NOT_RESOLVED,

    @SerialName("draft_status")
    DRAFT_STATUS,

    @SerialName("external_status_checks")
    EXTERNAL_STATUS_CHECKS,

    @SerialName("jira_association_missing")
    JIRA_ASSOCIATION_MISSING,

    @SerialName("requested_changes")
    REQUESTED_CHANGES,
}

@Serializable
enum class GitlabNoteableTypeDocument {
    @SerialName("MergeRequest")
    MERGE_REQUEST,

    @SerialName("Issue")
    ISSUE,

    @SerialName("Commit")
    COMMIT,

    @SerialName("Snippet")
    SNIPPET,
}

/* =========================================================
 * Notes/Comments
 * - This shape covers both plain MR notes (Notes API) and discussion notes (Discussions API).
 * - Discussion notes add resolution + position info; fields are nullable for plain notes.
 * ========================================================= */

@Serializable
data class GitlabMergeRequestNoteDocument(
    @SerialName("id") val id: Long,
    @SerialName("body") val body: String,
    @SerialName("author") val author: GitlabUserDocument,
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,

    // Note properties
    @SerialName("system") val system: Boolean = false,
    @SerialName("noteable_id") val noteableId: Long,
    @SerialName("noteable_type") val noteableType: GitlabNoteableTypeDocument,
    @SerialName("noteable_iid") val noteableIid: Long,
    @SerialName("project_id") val projectId: Long,

    // Visibility & resolution flags present on Notes
    @SerialName("resolvable") val resolvable: Boolean = false,
    @SerialName("confidential") val confidential: Boolean = false,
    @SerialName("internal") val internal: Boolean = false,

    // Import info
    @SerialName("imported") val imported: Boolean = false,
    @SerialName("imported_from") val importedFrom: String = "none",

    // Additional fields for threaded discussions (Discussions API)
    @SerialName("discussion_id") val discussionId: String? = null,
    @SerialName("resolved") val resolved: Boolean? = null,
    @SerialName("resolved_by") val resolvedBy: GitlabUserDocument? = null,
    @Contextual @SerialName("resolved_at") val resolvedAt: Instant? = null,
    @SerialName("position") val position: GitlabDiffPositionDocument? = null,

    // Misc
    @SerialName("type") val type: String? = null,
)

/**
 * Position of a discussion note on the diff.
 * When present, identifies the exact location in the code review.
 */
@Serializable
data class GitlabDiffPositionDocument(
    @SerialName("position_type") val positionType: String? = null, // "text" | "image"
    @SerialName("base_sha") val baseSha: String? = null,
    @SerialName("start_sha") val startSha: String? = null,
    @SerialName("head_sha") val headSha: String? = null,
    @SerialName("old_path") val oldPath: String? = null,
    @SerialName("new_path") val newPath: String? = null,
    @SerialName("old_line") val oldLine: Int? = null,
    @SerialName("new_line") val newLine: Int? = null,
)

/* =========================================================
 * Shared sub-documents
 * ========================================================= */

@Serializable
data class GitlabUserDocument(
    @SerialName("id") val id: Long,
    @SerialName("username") val username: String,
    @SerialName("name") val name: String,
    @SerialName("state") val state: String, // "active", "blocked", etc.
    @SerialName("locked") val locked: Boolean? = null,
    @SerialName("avatar_url") val avatarUrl: String? = null,
    @SerialName("web_url") val webUrl: String,
    @SerialName("email") val email: String? = null, // Only in some contexts
)

@Serializable
data class GitlabMilestoneDocument(
    @SerialName("id") val id: Long,
    @SerialName("iid") val iid: Long,
    @SerialName("project_id") val projectId: Long? = null,
    @SerialName("title") val title: String,
    @SerialName("description") val description: String? = null,
    @SerialName("state") val state: String, // "active", "closed"
    @Contextual @SerialName("created_at") val createdAt: Instant,
    @Contextual @SerialName("updated_at") val updatedAt: Instant,
    @SerialName("due_date") val dueDate: String? = null, // yyyy-MM-dd
    @SerialName("start_date") val startDate: String? = null, // yyyy-MM-dd
    @SerialName("web_url") val webUrl: String,
)

@Serializable
data class GitlabReferencesDocument(
    @SerialName("short") val short: String, // "!1"
    @SerialName("relative") val relative: String, // "!1" or "group/project!1"
    @SerialName("full") val full: String, // "group/project!1"
)

@Serializable
data class GitlabTimeStatsDocument(
    @SerialName("time_estimate") val timeEstimate: Int = 0,
    @SerialName("total_time_spent") val totalTimeSpent: Int = 0,
    @SerialName("human_time_estimate") val humanTimeEstimate: String? = null,
    @SerialName("human_total_time_spent") val humanTotalTimeSpent: String? = null,
)

@Serializable
data class GitlabTaskCompletionStatusDocument(
    @SerialName("count") val count: Int = 0,
    @SerialName("completed_count") val completedCount: Int = 0,
)
