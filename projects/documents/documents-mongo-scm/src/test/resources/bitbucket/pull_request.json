{"id": **********, "title": "Optimize database queries for better performance", "description": "This PR improves query performance by adding proper indexes and optimizing joins.\n\nKey changes:\n- Added composite indexes\n- Optimized N+1 queries\n- Added query metrics", "state": "OPEN", "draft": false, "author": {"uuid": "{********-1234-1234-1234-************}", "account_id": "557058:********-1234-1234-1234-************", "nickname": "bitbucket-dev", "display_name": "Bitbucket Developer", "type": "user", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7B********-1234-1234-1234-************%7D"}, "html": {"href": "https://bitbucket.org/%7B********-1234-1234-1234-************%7D/"}, "avatar": {"href": "https://secure.gravatar.com/avatar/12345?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Fdefault-avatar.png"}}}, "created_on": "2024-01-15T10:30:00.000000+00:00", "updated_on": "2024-01-16T14:45:30.123456+00:00", "source": {"branch": {"name": "feature/optimize-queries", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/refs/branches/feature%2Foptimize-queries"}, "html": {"href": "https://bitbucket.org/unblocked/backend/branch/feature/optimize-queries"}}}, "commit": {"hash": "abcdef********90abcdef********90abcdef12", "type": "commit", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/commit/abcdef********90abcdef********90abcdef12"}, "html": {"href": "https://bitbucket.org/unblocked/backend/commits/abcdef********90abcdef********90abcdef12"}}}, "repository": {"type": "repository", "full_name": "unblocked/backend", "name": "backend", "uuid": "{*************-4321-4321-************}", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend"}, "html": {"href": "https://bitbucket.org/unblocked/backend"}}}}, "destination": {"branch": {"name": "main", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/refs/branches/main"}, "html": {"href": "https://bitbucket.org/unblocked/backend/branch/main"}}}, "commit": {"hash": "fedcba09********fedcba09********fedcba09", "type": "commit", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/commit/fedcba09********fedcba09********fedcba09"}, "html": {"href": "https://bitbucket.org/unblocked/backend/commits/fedcba09********fedcba09********fedcba09"}}}, "repository": {"type": "repository", "full_name": "unblocked/backend", "name": "backend", "uuid": "{*************-4321-4321-************}", "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend"}, "html": {"href": "https://bitbucket.org/unblocked/backend"}}}}, "reviewers": [{"uuid": "{********-1111-1111-1111-************}", "account_id": "557058:********-1111-1111-1111-************", "nickname": "reviewer1", "display_name": "Code Reviewer One", "type": "user", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7B********-1111-1111-1111-************%7D"}, "html": {"href": "https://bitbucket.org/%7B********-1111-1111-1111-************%7D/"}, "avatar": {"href": "https://secure.gravatar.com/avatar/11111?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Fdefault-avatar.png"}}}], "participants": [{"user": {"uuid": "{********-1111-1111-1111-************}", "account_id": "557058:********-1111-1111-1111-************", "nickname": "reviewer1", "display_name": "Code Reviewer One", "type": "user", "links": {"self": {"href": "https://api.bitbucket.org/2.0/users/%7B********-1111-1111-1111-************%7D"}, "html": {"href": "https://bitbucket.org/%7B********-1111-1111-1111-************%7D/"}, "avatar": {"href": "https://secure.gravatar.com/avatar/11111?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Fdefault-avatar.png"}}}, "approved": true, "role": "REVIEWER"}], "merge_commit": null, "links": {"self": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123"}, "html": {"href": "https://bitbucket.org/unblocked/backend/pull-requests/123"}, "diff": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/diff"}, "diffstat": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/diffstat"}, "comments": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/comments"}, "activity": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/activity"}, "merge": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/merge"}, "decline": {"href": "https://api.bitbucket.org/2.0/repositories/unblocked/backend/pullrequests/123/decline"}}, "comment_count": 3, "task_count": 1, "close_source_branch": true, "closed_by": null, "reason": null}