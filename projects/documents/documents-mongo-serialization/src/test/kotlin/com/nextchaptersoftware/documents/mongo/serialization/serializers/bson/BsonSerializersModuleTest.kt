package com.nextchaptersoftware.documents.mongo.serialization.serializers.bson

import com.nextchaptersoftware.documents.mongo.serialization.utils.BsonSerializationUtils
import java.util.UUID
import kotlin.time.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.assertj.core.api.Assertions.assertThat
import org.bson.BsonBinarySubType
import org.bson.BsonDateTime
import org.bson.BsonDocument
import org.bson.codecs.kotlinx.BsonConfiguration
import org.bson.types.ObjectId
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class BsonSerializersModuleTest {
    @Serializable
    data class MiniDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @SerialName("count") val count: Int,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
        @Contextual @SerialName("closed_at") val closedAt: Instant? = null,
    )

    @Serializable
    data class MiniUser(
        @SerialName("id") val id: Long,
        @SerialName("login") val login: String,
    )

    @Serializable
    data class MiniCommit(
        @SerialName("ref") val ref: String,
        @SerialName("sha") val sha: String,
        @SerialName("author") val author: MiniUser? = null,
        @Contextual @SerialName("authored_at") val authoredAt: Instant? = null,
    )

    @Serializable
    data class ParentDoc(
        @Contextual @SerialName("_id") val mongoId: ObjectId = ObjectId(),
        @SerialName("title") val title: String,
        @SerialName("owner") val owner: MiniUser,
        @SerialName("commits") val commits: List<MiniCommit>,
        @SerialName("latest") val latest: MiniCommit?,
        @Contextual @SerialName("created_at") val createdAt: Instant,
        @Contextual @SerialName("updated_at") val updatedAt: Instant,
    )

    // --- Minimal doc for UUID tests
    @Serializable
    private data class UuidDoc(
        @SerialName("title") val title: String,
        @Contextual @SerialName("correlation_id") val correlationId: UUID?,
    )

    @Test
    fun `encodes native types with default module`() {
        val oid = ObjectId()
        val created = Instant.Companion.fromEpochMilliseconds(1_800_000_000_000)
        val updated = Instant.Companion.fromEpochMilliseconds(1_800_000_111_111)
        val value = MiniDoc(
            mongoId = oid,
            title = "check serialization",
            count = 7,
            createdAt = created,
            updatedAt = updated,
        )

        val bson: BsonDocument = BsonSerializationUtils.serialize(value)

        assertThat(bson["_id"]?.isObjectId).isTrue()
        assertThat(bson["_id"]?.asObjectId()?.value).isEqualTo(oid)

        assertThat(bson["title"]?.asString()?.value).isEqualTo("check serialization")
        assertThat(bson["count"]?.asInt32()?.value).isEqualTo(7)

        assertThat(bson["created_at"]?.isDateTime).isTrue()
        assertThat(bson["created_at"]?.asDateTime()).isInstanceOf(BsonDateTime::class.java)
        assertThat(bson["created_at"]?.asDateTime()?.value).isEqualTo(created.toEpochMilliseconds())

        assertThat(bson["updated_at"]?.asDateTime()?.value).isEqualTo(updated.toEpochMilliseconds())

        // Null optional omitted
        assertThat(bson.containsKey("closed_at")).isFalse()
    }

    @Test
    fun `round-trips through BSON`() {
        val created = Instant.Companion.fromEpochMilliseconds(1_900_000_000_000)
        val updated = Instant.Companion.fromEpochMilliseconds(1_900_000_222_222)

        val original = MiniDoc(
            title = "round trip",
            count = 1,
            createdAt = created,
            updatedAt = updated,
            closedAt = null,
        )

        val encoded = BsonSerializationUtils.serialize(original)
        val decoded = BsonSerializationUtils.deserialize<MiniDoc>(encoded)

        assertThat(decoded).isEqualTo(original)
    }

    @Test
    fun `writes nulls when explicitNulls true`() {
        val value = MiniDoc(
            title = "explicit nulls",
            count = 0,
            createdAt = Instant.Companion.fromEpochMilliseconds(1_700_000_000_000),
            updatedAt = Instant.Companion.fromEpochMilliseconds(1_700_000_100_000),
            closedAt = null,
        )

        val bson = BsonSerializationUtils.serialize(value, configuration = BsonConfiguration(explicitNulls = true))

        assertThat(bson.containsKey("closed_at")).isTrue()
        assertThat(bson["closed_at"]?.isNull).isTrue()
    }

    @Nested
    inner class NestedDocumentsTest {
        @Test
        fun `embeds single nested document`() {
            val created = Instant.Companion.fromEpochMilliseconds(1_840_000_000_000)
            val updated = Instant.Companion.fromEpochMilliseconds(1_840_000_123_456)

            val parent = ParentDoc(
                title = "nested-single",
                owner = MiniUser(id = 1001, login = "alice"),
                commits = emptyList(),
                latest = MiniCommit(ref = "main", sha = "abc123", author = MiniUser(2, "bob")),
                createdAt = created,
                updatedAt = updated,
            )

            val bson = BsonSerializationUtils.serialize(parent)

            val ownerDoc = bson["owner"]?.asDocument()
            assertThat(ownerDoc?.get("id")?.asInt64()?.value).isEqualTo(1001L)

            val latestDoc = bson["latest"]?.asDocument()
            assertThat(latestDoc?.get("ref")?.asString()?.value).isEqualTo("main")

            val authorDoc = latestDoc?.get("author")?.asDocument()
            assertThat(authorDoc?.get("login")?.asString()?.value).isEqualTo("bob")

            val decoded = BsonSerializationUtils.deserialize<ParentDoc>(bson)
            assertThat(decoded).isEqualTo(parent)
        }

        @Test
        fun `embeds list of nested documents`() {
            val created = Instant.Companion.fromEpochMilliseconds(1_850_000_000_000)
            val updated = Instant.Companion.fromEpochMilliseconds(1_850_000_111_111)
            val authoredAt = Instant.Companion.fromEpochMilliseconds(1_850_000_222_222)

            val parent = ParentDoc(
                title = "nested-list",
                owner = MiniUser(id = 2002, login = "charlie"),
                commits = listOf(
                    MiniCommit(ref = "main", sha = "aaa111", author = MiniUser(3, "dana"), authoredAt = authoredAt),
                    MiniCommit(ref = "feature/x", sha = "bbb222", author = MiniUser(4, "edgar")),
                ),
                latest = null,
                createdAt = created,
                updatedAt = updated,
            )

            val bson = BsonSerializationUtils.serialize(parent)

            val commitsArray = bson["commits"]?.asArray()
            assertThat(commitsArray?.size).isEqualTo(2)

            val first = commitsArray?.get(0)?.asDocument()
            assertThat(first?.get("sha")?.asString()?.value).isEqualTo("aaa111")

            val second = commitsArray?.get(1)?.asDocument()
            assertThat(second?.get("ref")?.asString()?.value).isEqualTo("feature/x")

            val decoded = BsonSerializationUtils.deserialize<ParentDoc>(bson)
            assertThat(decoded).isEqualTo(parent)
        }

        @Test
        fun `nullable nested document is written when explicitNulls true`() {
            val created = Instant.Companion.fromEpochMilliseconds(1_860_000_000_000)
            val updated = Instant.Companion.fromEpochMilliseconds(1_860_000_333_333)

            val parent = ParentDoc(
                title = "nullable-embed",
                owner = MiniUser(id = 3003, login = "frank"),
                commits = emptyList(),
                latest = null,
                createdAt = created,
                updatedAt = updated,
            )

            val bson = BsonSerializationUtils.serialize(parent, configuration = BsonConfiguration(explicitNulls = true))

            assertThat(bson.containsKey("latest")).isTrue()
            assertThat(bson["latest"]?.isNull).isTrue()

            val decoded = BsonSerializationUtils.deserialize<ParentDoc>(bson, configuration = BsonConfiguration(explicitNulls = true))
            assertThat(decoded).isEqualTo(parent)
        }
    }

    @Nested
    inner class UuidTests {

        @Test
        fun `encodes UUID to BSON binary subtype 4`() {
            val uuid = UUID.fromString("12345678-90ab-cdef-1234-567890abcdef")
            val doc = UuidDoc(title = "uuid-binary", correlationId = uuid)

            val bson: BsonDocument = BsonSerializationUtils.serialize(doc)

            // raw check: stored as binary (UUID_STANDARD -> subtype 0x04)
            val bin = bson["correlation_id"]?.asBinary()
            assertThat(bin).isNotNull
            assertThat(bin?.type).isEqualTo(BsonBinarySubType.UUID_STANDARD.value)

            // sanity: decode back to UUID via deserialize(UuidDoc)
            val roundTripped = BsonSerializationUtils.deserialize<UuidDoc>(bson)
            assertThat(roundTripped.correlationId).isEqualTo(uuid)
        }

        @Test
        fun `decodes UUID from canonical string fallback`() {
            // Simulate stored string (e.g., legacy doc or manual insert)
            val uuidStr = "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"
            val bson = BsonDocument.parse(
                """{
              "title": "uuid-string",
              "correlation_id": "$uuidStr"
            }""",
            )

            val decoded = BsonSerializationUtils.deserialize<UuidDoc>(bson)
            assertThat(decoded.correlationId).isEqualTo(UUID.fromString(uuidStr))

            // Re-encode should normalize to Binary subtype 4
            val reEncoded = BsonSerializationUtils.serialize(decoded)
            val asBin = reEncoded["correlation_id"]?.asBinary()
            assertThat(asBin).isNotNull
            assertThat(asBin?.type).isEqualTo(BsonBinarySubType.UUID_STANDARD.value)
        }

        @Test
        fun `handles nullable UUID`() {
            val doc = UuidDoc(title = "uuid-null", correlationId = null)

            val bson = BsonSerializationUtils.serialize(doc)
            // By default (explicitNulls = false), null field should be omitted
            assertThat(bson.containsKey("correlation_id")).isFalse

            // If you want to assert explicit null behavior, round-trip with explicitNulls=true:
            val bsonWithNull = BsonSerializationUtils.serialize(doc, configuration = BsonConfiguration(explicitNulls = true))
            assertThat(bsonWithNull.containsKey("correlation_id")).isTrue
            assertThat(bsonWithNull["correlation_id"]?.isNull).isTrue
        }

        @Test
        fun `round-trips UUID via serialize - deserialize`() {
            val uuid = UUID.randomUUID()
            val original = UuidDoc(title = "uuid-rt", correlationId = uuid)

            val encoded = BsonSerializationUtils.serialize(original)
            val decoded = BsonSerializationUtils.deserialize<UuidDoc>(encoded)

            assertThat(decoded).isEqualTo(original)
        }
    }
}
