package com.nextchaptersoftware.scmservice.jobs

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.pr.ingestion.PullRequestBulkIngestionService
import com.nextchaptersoftware.service.BackgroundJob
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class PullRequestBulkIngestionJob(
    private val pullRequestBulkIngestionService: PullRequestBulkIngestionService,
) : BackgroundJob {
    override val name: String
        get() = "Pull Request Bulk Ingestion Job"

    override suspend fun run() {
        runSuspendCatching {
            pullRequestBulkIngestionService.run()
        }.getOrElse {
            LOGGER.errorAsync(it) { "Failed to bulk ingest pull requests" }
        }
    }
}
