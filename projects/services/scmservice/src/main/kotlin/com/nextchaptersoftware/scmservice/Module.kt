package com.nextchaptersoftware.scmservice

import com.nextchaptersoftware.access.RestrictedAccessServiceFactory
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.api.threads.services.ThreadUnreadServiceImpl
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.aws.s3.StandardS3ProviderFactory
import com.nextchaptersoftware.aws.sfn.StandardSfnProviderFactory
import com.nextchaptersoftware.aws.sfn.statemachine.StandardStateMachineProvider
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.ghdiscussions.services.GitHubDiscussionService
import com.nextchaptersoftware.ghdiscussions.services.GitHubDiscussionsEmbeddingService
import com.nextchaptersoftware.ghissues.services.GitHubIssuesIngestionService
import com.nextchaptersoftware.ingestion.catalogue.config.IngestionCatalogueConfig
import com.nextchaptersoftware.ingestion.catalogue.data.store.S3IngestionCatalogueDataStore
import com.nextchaptersoftware.ingestion.catalogue.services.commit.IngestionCatalogueCommitService
import com.nextchaptersoftware.ingestion.catalogue.services.embedding.IngestionCatalogueEmbeddingService
import com.nextchaptersoftware.ingestion.catalogue.services.extraction.IngestionCatalogueExtractionService
import com.nextchaptersoftware.ingestion.pipeline.config.IngestionPipelineConfig
import com.nextchaptersoftware.ingestion.redis.IngestionDisablementService
import com.nextchaptersoftware.ingestion.services.EmojiMarkdownPreProcessorService
import com.nextchaptersoftware.ingestion.services.MarkdownToMessageBodyService
import com.nextchaptersoftware.ingestion.services.StandardMarkdownMentionResolutionService
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.integration.data.config.IntegrationDataConfig
import com.nextchaptersoftware.integration.data.events.queue.enqueue.DataEventEnqueueService
import com.nextchaptersoftware.maintenance.IdentityMaintenance
import com.nextchaptersoftware.maintenance.OrgMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmInstallationMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamMaintenance
import com.nextchaptersoftware.metrics.MetricsService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.pr.config.PullRequestConfig
import com.nextchaptersoftware.pr.ingestion.PullRequestArchiverService
import com.nextchaptersoftware.pr.ingestion.PullRequestBulkIngestionService
import com.nextchaptersoftware.pr.ingestion.PullRequestCommentService
import com.nextchaptersoftware.pr.ingestion.PullRequestIngestionHandler
import com.nextchaptersoftware.pr.ingestion.PullRequestReviewCommentService
import com.nextchaptersoftware.pr.ingestion.PullRequestReviewService
import com.nextchaptersoftware.pr.ingestion.PullRequestService
import com.nextchaptersoftware.pr.ingestion.PullRequestTopLevelCommentService
import com.nextchaptersoftware.pr.ingestion.catalogue.providers.PullRequestRawContextProvider
import com.nextchaptersoftware.pr.ingestion.catalogue.queue.enqueue.PullRequestIngestionCatalogueEventEnqueueService
import com.nextchaptersoftware.pr.ingestion.catalogue.queue.enqueue.PullRequestIngestionCatalogueEventEnqueueServiceProvider
import com.nextchaptersoftware.pr.ingestion.catalogue.queue.handlers.PullRequestIngestionCatalogueCommitEventHandler
import com.nextchaptersoftware.pr.ingestion.catalogue.queue.handlers.PullRequestIngestionCatalogueEventHandler
import com.nextchaptersoftware.pr.ingestion.catalogue.services.PullRequestBulkIngestionCatalogueService
import com.nextchaptersoftware.pr.ingestion.catalogue.services.commit.PullRequestIngestionCatalogueCommitService
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.azuredevops.AzureDevOpsPullRequestCommentIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.azuredevops.AzureDevOpsPullRequestIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.bitbucket.BitbucketPullRequestCommentIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.bitbucket.BitbucketPullRequestIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.bitbucketdatacenter.BitbucketDataCenterPullRequestCommentIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.bitbucketdatacenter.BitbucketDataCenterPullRequestIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.github.GitHubPullRequestCommentIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.github.GitHubPullRequestIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.github.GitHubPullRequestReviewIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.gitlab.GitLabPullRequestCommentIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.embedding.gitlab.GitLabPullRequestIngestionCatalogueEmbedder
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.azuredevops.AzureDevOpsPullRequestCommentIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.azuredevops.AzureDevOpsPullRequestIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.bitbucket.BitbucketPullRequestCommentIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.bitbucket.BitbucketPullRequestIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.bitbucketdatacenter.BitbucketDataCenterPullRequestCommentIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.bitbucketdatacenter.BitbucketDataCenterPullRequestIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.github.GitHubPullRequestCommentIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.github.GitHubPullRequestIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.github.GitHubPullRequestReviewIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.gitlab.GitLabPullRequestCommentIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.catalogue.services.extraction.gitlab.GitLabPullRequestIngestionCatalogueExtractor
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestCommentThreadProvider
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestIngestionServiceProvider
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestEventEnqueueService
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestEventEnqueueServiceProvider
import com.nextchaptersoftware.pr.ingestion.queue.handlers.PullRequestArchiveEventHandler
import com.nextchaptersoftware.pr.ingestion.queue.handlers.PullRequestEventHandler
import com.nextchaptersoftware.pr.ingestion.queue.handlers.PullRequestIngestionEventHandler
import com.nextchaptersoftware.pr.ingestion.redis.lock.PullRequestIngestionLockProvider
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.PullRequestIngestionDecisionPipeline
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.PullRequestIngestionPipeline
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.RepoPullRequestSummaryService
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.filter.RepoPullRequestIngestionFilterEventHandler
import com.nextchaptersoftware.pr.summary.ingestion.queue.enqueue.PullRequestSummaryIngestionEventEnqueueService
import com.nextchaptersoftware.pr.summary.ingestion.queue.handlers.PullRequestSummariesCompletionEventHandler
import com.nextchaptersoftware.pr.summary.ingestion.queue.handlers.PullRequestSummaryIngestionEventHandler
import com.nextchaptersoftware.pr.summary.ingestion.services.PullRequestSummariesCompletionService
import com.nextchaptersoftware.pr.summary.ingestion.services.PullRequestSummaryIngestionService
import com.nextchaptersoftware.pr.summary.ingestion.services.internal.PullRequestIngestionInputPayloadService
import com.nextchaptersoftware.pr.summary.ingestion.services.internal.PullRequestIngestionStateMachineProvider
import com.nextchaptersoftware.pr.summary.ingestion.services.payload.PullRequestSummariesIngestionPayloadService
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.review.CodeReviewEventProducer
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmCloneUrlProvider
import com.nextchaptersoftware.scm.ScmRawRepoApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.ScmWebhookService
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.scm.delegates.ScmAppDelegateImpl
import com.nextchaptersoftware.scm.ingestion.pipeline.filter.StandardRepoIngestionFilterService
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.scm.raw.DefaultScmRawPullRequestConverter
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.scm.services.RepoSelectionService
import com.nextchaptersoftware.scm.utils.DefaultEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.scm.validators.GitHubEventValidator
import com.nextchaptersoftware.scmservice.handlers.ScmEventWebhookHandler
import com.nextchaptersoftware.scmservice.handlers.bitbucket.BitbucketPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.bitbucketdatacenter.BitbucketDataCenterPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubDiscussionCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubDiscussionHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubIssueCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubIssueHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestReviewCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestReviewHandler
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubDiscussionCommentEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubDiscussionEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubIssueCommentEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubIssueEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubPullRequestEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubPullRequestReviewCommentEventService
import com.nextchaptersoftware.scmservice.handlers.github.services.GitHubPullRequestReviewEventService
import com.nextchaptersoftware.scmservice.handlers.gitlab.GitLabPullRequestHandler
import com.nextchaptersoftware.scmservice.jobs.GitHubDiscussionsIngestionJob
import com.nextchaptersoftware.scmservice.jobs.GitHubInstallationMaintenanceJob
import com.nextchaptersoftware.scmservice.jobs.GitHubIssuesBulkIngestionJob
import com.nextchaptersoftware.scmservice.jobs.GitHubIssuesIngestionSyncJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestBulkIngestionCatalogueJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestBulkIngestionJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestEventJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionBackfillJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionCatalogueEmbeddingJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionCatalogueExtractionJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionCatalogueSyncJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionOnboardingJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionPipelineJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestIngestionSyncJob
import com.nextchaptersoftware.scmservice.jobs.PullRequestSummariesIngestionEventJob
import com.nextchaptersoftware.scmservice.jobs.ScmEventProcessingJob
import com.nextchaptersoftware.scmservice.jobs.ScmEventProcessor
import com.nextchaptersoftware.scmservice.jobs.ScmWebhookProcessingJob
import com.nextchaptersoftware.scmservice.plugins.configureRouting
import com.nextchaptersoftware.search.indexing.events.queue.enqueue.SearchIndexingEventEnqueueService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.semantic.bot.services.MessageMentionService
import com.nextchaptersoftware.semantic.bot.services.MessageService
import com.nextchaptersoftware.service.ExclusiveBackgroundJob
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import io.ktor.server.application.Application
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

/**
 * The number of concurrent consumers for the scm event queue.
 */
private const val CONSUMER_COUNT = 6

/**
 * The number of concurrent polling jobs for the scm event queue.
 */
private const val POLLING_COUNT = 2

@Suppress("LongMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = emptyList()),
    config: GlobalConfig = GlobalConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    scmSecretConfig: ScmSecretConfig = ScmSecretConfig.INSTANCE,
    ingestionCatalogueConfig: IngestionCatalogueConfig = IngestionCatalogueConfig.INSTANCE,
    ingestionPipelineConfig: IngestionPipelineConfig = IngestionPipelineConfig.INSTANCE,
    integrationDataConfig: IntegrationDataConfig = IntegrationDataConfig.INSTANCE,
    pullRequestConfig: PullRequestConfig = PullRequestConfig.INSTANCE,
    userSecretConfig: UserSecretConfig = UserSecretConfig.INSTANCE,
) {
    val s3ProviderFactory by lazy {
        StandardS3ProviderFactory()
    }

    val ingestionCatalogueAwsClientProvider by lazy {
        AWSClientProvider.from(
            region = ingestionCatalogueConfig.ingestionCatalogue.s3.region.toRegion(),
        )
    }

    val pullRequestDataPipelineAwsClientProvider by lazy {
        AWSClientProvider.from(
            region = ingestionPipelineConfig.ingestionPipeline.s3.region.toRegion(),
        )
    }

    val botAccountService by lazy {
        InstallationBotAccountService()
    }

    val userSecretServiceRSA by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = RSACryptoSystem.RSADecryption(
                privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                modulusBitLength = 4096,
            ),
        )
    }

    val userSecretService by lazy {
        // TODO: remove once all have been migrated to `userSecretServiceRSA`
        userSecretServiceRSA
    }

    val userSecretServiceAES by lazy {
        val key = AESCryptoSystem.importKey(
            userSecretConfig.encryption.userSecretsAesKey.value,
        )
        UserSecretService(
            encryption = AESCryptoSystem.AESEncryption(key),
            decryption = AESCryptoSystem.AESDecryption(key),
        )
    }

    val userSecretServiceResolver by lazy {
        UserSecretServiceResolver(
            userSecretServiceRSA = userSecretServiceRSA,
            userSecretServiceAES = userSecretServiceAES,
        )
    }

    val messageMentionService by lazy {
        MessageMentionService()
    }

    val dataEventEnqueueService by lazy {
        DataEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.dataEventsQueueName,
                ),
            ),
        )
    }

    val markdownToMessageBodyService by lazy {
        MarkdownToMessageBodyService(
            markdownPreProcessorService = EmojiMarkdownPreProcessorService(),
            markdownMentionResolutionService = StandardMarkdownMentionResolutionService(),
        )
    }

    val pullRequestService by lazy {
        PullRequestService(
            dataEventEnqueueService = dataEventEnqueueService,
            markdownToMessageBodyService = markdownToMessageBodyService,
        )
    }

    val pullRequestEventEnqueueService by lazy {
        PullRequestEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.pullRequestEventsQueueName,
                ),
            ),
        )
    }

    val pullRequestIngestionCatalogueEventEnqueueService by lazy {
        PullRequestIngestionCatalogueEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.pullRequestIngestionCatalogueEventsQueueName,
                ),
            ),
        )
    }

    val prIngestionLockProvider by lazy {
        PullRequestIngestionLockProvider()
    }

    val prIngestionCatalogueEmbeddingLockProvider by lazy {
        LockProvider(type = LockType.PullRequestIngestionCatalogueEmbedding)
    }

    val prIngestionCatalogueExtractionLockProvider by lazy {
        LockProvider(type = LockType.PullRequestIngestionCatalogueExtraction)
    }

    val prBulkIngestionLockProvider by lazy {
        LockProvider(type = LockType.PullRequestIngestionBulk)
    }

    val prBulkIngestionCatalogueLockProvider by lazy {
        LockProvider(type = LockType.PullRequestIngestionCatalogueBulk)
    }

    val enterpriseAppConfigOrgIdsResolver by lazy {
        DefaultEnterpriseAppConfigOrgIdsResolver()
    }

    val codeReviewEventProducer by lazy {
        CodeReviewEventProducer(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.codeReviewQueueName,
                ),
            ),
        )
    }

    val scmAppApiFactory by lazy {
        ScmAppApiFactory(
            scmConfig = scmConfig,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = ScmWebFactory(scmConfig),
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmRepoApiFactory by lazy {
        ScmRepoApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmRawRepoApiFactory by lazy {
        ScmRawRepoApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmRawPullRequestConverter by lazy {
        DefaultScmRawPullRequestConverter()
    }

    val pullRequestCommentThreadProvider by lazy {
        PullRequestCommentThreadProvider(scmRepoApiFactory = scmRepoApiFactory, pullRequestService = pullRequestService)
    }

    val searchIndexingEventEnqueueService by lazy {
        SearchIndexingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchIndexingEventsQueueName,
                ),
            ),
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val indexingAndEmbeddingService by lazy {
        IndexingAndEmbeddingService(
            searchIndexingEventEnqueueService = searchIndexingEventEnqueueService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val messageService by lazy {
        MessageService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = botAccountService,
        )
    }

    val insiderService by lazy {
        InsiderService()
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
        )
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val peerInviteSuggestionService by lazy {
        PeerInviteSuggestionService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val inactiveFollowupService by lazy {
        InactiveFollowupService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val metricsService by lazy {
        MetricsService(
            peerInviteSuggestionService = peerInviteSuggestionService,
            inactiveFollowupService = inactiveFollowupService,
        )
    }

    val threadService by lazy {
        ThreadService(
            messageService = messageService,
            metricsService = metricsService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = null,
        )
    }

    val pullRequestIngestionServiceProvider by lazy {
        PullRequestIngestionServiceProvider(
            messageMentionService = messageMentionService,
            threadService = threadService,
            dataEventEnqueueService = dataEventEnqueueService,
            markdownToMessageBodyService = markdownToMessageBodyService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val identityMaintenance by lazy {
        IdentityMaintenance(
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretService = userSecretService,
        )
    }

    val scmMembershipMaintenance by lazy {
        ScmMembershipMaintenance(
            slackNotifier = slackNotifier,
        )
    }

    val pullRequestTopLevelCommentService by lazy {
        PullRequestTopLevelCommentService()
    }

    val prReviewService by lazy {
        PullRequestReviewService(
            dataEventEnqueueService = dataEventEnqueueService,
            markdownToMessageBodyService = markdownToMessageBodyService,
            pullRequestTopLevelCommentService = pullRequestTopLevelCommentService,
            pullRequestIngestionServiceProvider = pullRequestIngestionServiceProvider,
        )
    }

    val pullRequestIngestionHandler by lazy {
        PullRequestIngestionHandler(
            prCommentThreadProvider = pullRequestCommentThreadProvider,
            prIngestionServiceProvider = pullRequestIngestionServiceProvider,
            prReviewService = prReviewService,
            pullRequestService = pullRequestService,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
            scmMembershipMaintenance = scmMembershipMaintenance,
        )
    }

    val pullRequestIngestionEventHandler by lazy {
        PullRequestIngestionEventHandler(
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
            pullRequestIngestionHandler = pullRequestIngestionHandler,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val pullRequestArchiveEventHandler by lazy {
        PullRequestArchiveEventHandler(
            pullRequestArchiverService = PullRequestArchiverService(),
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val pullRequestEventHandler by lazy {
        PullRequestEventHandler(
            pullRequestIngestionEventHandler = pullRequestIngestionEventHandler,
            pullRequestArchiveEventHandler = pullRequestArchiveEventHandler,
        )
    }

    val pullRequestRawContextProvider by lazy {
        PullRequestRawContextProvider(
            scmRepoApiFactory = scmRepoApiFactory,
            scmRawRepoApiFactory = scmRawRepoApiFactory,
            pullRequestService = pullRequestService,
        )
    }

    val ingestionCatalogueDataStore by lazy {
        S3IngestionCatalogueDataStore(
            s3ProviderFactory = s3ProviderFactory,
            awsClientProvider = ingestionCatalogueAwsClientProvider,
            bucketName = ingestionCatalogueConfig.ingestionCatalogue.s3.bucket,
        )
    }

    val ingestionCatalogueCommitService by lazy {
        IngestionCatalogueCommitService(
            ingestionCatalogueDataStore = ingestionCatalogueDataStore,
        )
    }

    val pullRequestIngestionCatalogueCommitService by lazy {
        PullRequestIngestionCatalogueCommitService(
            ingestionCatalogueCommitService = ingestionCatalogueCommitService,
        )
    }

    val pullRequestIngestionCatalogueCommitEventHandler by lazy {
        PullRequestIngestionCatalogueCommitEventHandler(
            pullRequestIngestionCatalogueEventEnqueueService = pullRequestIngestionCatalogueEventEnqueueService,
            pullRequestRawContextProvider = pullRequestRawContextProvider,
            pullRequestIngestionCatalogueCommitService = pullRequestIngestionCatalogueCommitService,
        )
    }

    val pullRequestIngestionCatalogueEventHandler by lazy {
        PullRequestIngestionCatalogueEventHandler(
            pullRequestIngestionCatalogueCommitEventHandler = pullRequestIngestionCatalogueCommitEventHandler,
        )
    }

    val pullRequestCommentService by lazy {
        PullRequestCommentService(
            markdownToMessageBodyService = markdownToMessageBodyService,
            pullRequestTopLevelCommentService = pullRequestTopLevelCommentService,
            pullRequestIngestionServiceProvider = pullRequestIngestionServiceProvider,
            dataEventEnqueueService = dataEventEnqueueService,
        )
    }

    val pullRequestReviewCommentService by lazy {
        PullRequestReviewCommentService(
            pullRequestService = pullRequestService,
            pullRequestIngestionServiceProvider = pullRequestIngestionServiceProvider,
            dataEventEnqueueService = dataEventEnqueueService,
        )
    }

    val threadUnreadService by lazy {
        ThreadUnreadServiceImpl()
    }

    val repoPullRequestSummaryService by lazy {
        RepoPullRequestSummaryService(
            ingestionInterval = pullRequestConfig.pullRequestDataPipeline.ingestionInterval,
            lastActiveAtThreshold = pullRequestConfig.pullRequestDataPipeline.lastActiveAtThreshold,
            maxReposPerTeam = pullRequestConfig.pullRequestDataPipeline.maxReposPerTeam,
        )
    }

    val repoPullRequestIngestionFilterEventHandler by lazy {
        RepoPullRequestIngestionFilterEventHandler(
            repoPullRequestSummaryService = repoPullRequestSummaryService,
        )
    }

    val repoPullRequestIngestionFilterService by lazy {
        StandardRepoIngestionFilterService(
            scmRepoApiFactory = scmRepoApiFactory,
            repoIngestionFilterEventHandler = repoPullRequestIngestionFilterEventHandler,
        )
    }

    val teamAndRepoProvider by lazy {
        TeamAndRepoProvider()
    }

    val gitHubPullRequestReviewCommentEventService by lazy {
        GitHubPullRequestReviewCommentEventService(
            teamAndRepoProvider = teamAndRepoProvider,
            scmMembershipMaintenance = scmMembershipMaintenance,
            pullRequestService = pullRequestService,
            prCommentThreadProvider = pullRequestCommentThreadProvider,
            prIngestionServiceProvider = pullRequestIngestionServiceProvider,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
        )
    }

    val gitHubIssuesIngestionService by lazy {
        GitHubIssuesIngestionService(
            threadService = threadService,
            threadUnreadService = threadUnreadService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val gitHubIssueCommentEventService by lazy {
        GitHubIssueCommentEventService(
            teamAndRepoProvider = teamAndRepoProvider,
            scmMembershipMaintenance = scmMembershipMaintenance,
            pullRequestCommentService = pullRequestCommentService,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            gitHubIssuesIngestionService = gitHubIssuesIngestionService,
        )
    }

    val gitHubPullRequestEventService by lazy {
        GitHubPullRequestEventService(
            codeReviewEventProducer = codeReviewEventProducer,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
            pullRequestService = pullRequestService,
            teamAndRepoProvider = teamAndRepoProvider,
        )
    }

    val gitHubPullRequestReviewEventService by lazy {
        GitHubPullRequestReviewEventService(
            scmMembershipMaintenance = scmMembershipMaintenance,
            teamAndRepoProvider = teamAndRepoProvider,
            pullRequestService = pullRequestService,
            pullRequestReviewService = prReviewService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
        )
    }

    val gitHubCloudHMACAuthenticator by lazy {
        scmSecretConfig.githubCloud?.let {
            HMACAuthenticator(
                authenticationSecret = it.webhookHMACSecret,
                signaturePrefix = "sha256=",
            )
        }
    }

    val gitHubIssuesIngestionLockProvider by lazy {
        LockProvider(type = LockType.GitHubIssues)
    }

    val gitHubIssueEventService by lazy {
        GitHubIssueEventService(
            teamAndRepoProvider = teamAndRepoProvider,
            scmMembershipMaintenance = scmMembershipMaintenance,
            gitHubIssuesIngestionService = gitHubIssuesIngestionService,
        )
    }

    val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val pullRequestSummariesIngestionPayloadService by lazy {
        PullRequestSummariesIngestionPayloadService()
    }

    val pullRequestSummaryIngestionEventEnqueueService by lazy {
        PullRequestSummaryIngestionEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.pullRequestSummaryIngestionQueueName,
                ),
            ),
        )
    }

    val pullRequestSummariesIngestionService by lazy {
        PullRequestSummariesCompletionService(
            s3ProviderFactory = s3ProviderFactory,
            awsClientProvider = pullRequestDataPipelineAwsClientProvider,
            ingestionPipelineConfig = ingestionPipelineConfig,
            pullRequestSummariesIngestionPayloadService = pullRequestSummariesIngestionPayloadService,
            pullRequestSummaryIngestionEventEnqueueService = pullRequestSummaryIngestionEventEnqueueService,
            repoPullRequestSummaryService = repoPullRequestSummaryService,
        )
    }

    val pullRequestSummaryIngestionService by lazy {
        PullRequestSummaryIngestionService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val pullRequestIngestionBackfillJob by lazy {
        PullRequestIngestionBackfillJob()
    }

    val repoMaintenance by lazy {
        RepoMaintenance()
    }

    val repoSelectionService by lazy {
        RepoSelectionService(repoMaintenance = repoMaintenance)
    }

    val scmTeamApiFactory by lazy {
        ScmTeamApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmWebhookService by lazy {
        ScmWebhookService(scmConfig = scmConfig)
    }

    val scmTeamMaintenance by lazy {
        ScmTeamMaintenance(
            scmMembershipMaintenance = scmMembershipMaintenance,
            lockProvider = LockProvider(
                type = LockType.ScmMaintenanceScmTeam,
            ),
            repoMaintenance = repoMaintenance,
            scmTeamApiFactory = scmTeamApiFactory,
            scmUserApiFactory = scmUserApiFactory,
            scmWebhookService = scmWebhookService,
        )
    }

    val orgMaintenance by lazy {
        OrgMaintenance()
    }

    val scmInstallationMaintenance by lazy {
        ScmInstallationMaintenance()
    }

    val scmTeamLifecycleMaintenance by lazy {
        ScmTeamLifecycleMaintenance(
            insiderService = insiderService,
            restrictedAccessService = RestrictedAccessServiceFactory.fromConfig(),
            scmAppDelegate = ScmAppDelegateImpl(
                scmAppApiFactory = scmAppApiFactory,
            ),
            orgMaintenance = orgMaintenance,
            scmInstallationMaintenance = scmInstallationMaintenance,
        )
    }

    val gitHubDiscussionsEmbeddingService by lazy {
        GitHubDiscussionsEmbeddingService(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val gitHubDiscussionService by lazy {
        GitHubDiscussionService(
            scmAppApiFactory = scmAppApiFactory,
            embeddingService = gitHubDiscussionsEmbeddingService,
        )
    }

    val gitHubDiscussionEventService by lazy {
        GitHubDiscussionEventService(
            teamAndRepoProvider = teamAndRepoProvider,
            gitHubDiscussionService = gitHubDiscussionService,
        )
    }

    val gitHubDiscussionCommentEventService by lazy {
        GitHubDiscussionCommentEventService(
            teamAndRepoProvider = teamAndRepoProvider,
            gitHubDiscussionService = gitHubDiscussionService,
        )
    }

    val scmEventWebhookHandler by lazy {
        ScmEventWebhookHandler(
            gitHubPullRequestReviewCommentHandler = GitHubPullRequestReviewCommentHandler(
                service = gitHubPullRequestReviewCommentEventService,
            ),
            gitHubIssueCommentHandler = GitHubIssueCommentHandler(
                service = gitHubIssueCommentEventService,
            ),
            gitHubIssueHandler = GitHubIssueHandler(
                service = gitHubIssueEventService,
            ),
            gitHubPullRequestHandler = GitHubPullRequestHandler(
                service = gitHubPullRequestEventService,
            ),
            gitHubPullRequestReviewHandler = GitHubPullRequestReviewHandler(
                service = gitHubPullRequestReviewEventService,
            ),
            gitHubDiscussionCommentHandler = GitHubDiscussionCommentHandler(
                service = gitHubDiscussionCommentEventService,
            ),
            gitHubDiscussionHandler = GitHubDiscussionHandler(
                service = gitHubDiscussionEventService,
            ),
            gitHubEventValidator = GitHubEventValidator(
                gitHubCloudHMACAuthenticator = gitHubCloudHMACAuthenticator,
                gitHubCloudConfig = scmConfig.githubCloud,
            ),
            scmAppApiFactory = scmAppApiFactory,
            scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
            repoMaintenance = repoMaintenance,
            identityMaintenance = identityMaintenance,
            scmTeamMaintenance = scmTeamMaintenance,
            bitbucketPullRequestHandler = BitbucketPullRequestHandler(
                teamAndRepoProvider = teamAndRepoProvider,
                pullRequestService = pullRequestService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
            ),
            bitbucketDataCenterPullRequestHandler = BitbucketDataCenterPullRequestHandler(
                teamAndRepoProvider = teamAndRepoProvider,
                pullRequestService = pullRequestService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
            ),
            gitLabPullRequestHandler = GitLabPullRequestHandler(
                teamAndRepoProvider = teamAndRepoProvider,
                pullRequestService = pullRequestService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                scmRepoApiFactory = scmRepoApiFactory,
            ),
            slackNotifier = slackNotifier,
        )
    }

    val sfnStateMachineProvider by lazy {
        StandardSfnProviderFactory()
    }

    val pullRequestIngestionStateMachineProvider by lazy {
        PullRequestIngestionStateMachineProvider(
            config = config,
            stateMachineProvider = StandardStateMachineProvider(
                sfnProvider = sfnStateMachineProvider.generate(
                    awsClientProvider = AWSClientProvider.from(
                        region = ingestionPipelineConfig.ingestionPipeline.s3.region.toRegion(),
                    ),
                ),
            ),
            ingestionPipelineConfig = ingestionPipelineConfig,
            pullRequestConfig = pullRequestConfig,
            scmS3Bucket = integrationDataConfig.integrationData.s3.bucket,
            pullRequestIngestionInputPayloadService = PullRequestIngestionInputPayloadService(),
            repoPullRequestSummaryService = repoPullRequestSummaryService,
        )
    }

    val scmCloneUrlProvider by lazy {
        ScmCloneUrlProvider(
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val pullRequestIngestionDecisionPipeline by lazy {
        PullRequestIngestionDecisionPipeline(
            pullRequestIngestionPipeline = PullRequestIngestionPipeline(
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                redis = Redis.API,
                concurrencyLimit = pullRequestConfig.pullRequestDataPipeline.concurrencyLimit,
                repoIngestionFilterService = repoPullRequestIngestionFilterService,
            ),
        )
    }

    val pullRequestIngestionPipelineJob by lazy {
        ExclusiveBackgroundJob(
            job = PullRequestIngestionPipelineJob(
                pullRequestIngestionDecisionPipeline = pullRequestIngestionDecisionPipeline,
            ),
        )
    }

    val ingestionDisablementService by lazy {
        IngestionDisablementService()
    }

    val prIngestionCatalogueExtractionService by lazy {
        IngestionCatalogueExtractionService(
            ingestionCatalogueDataStore = ingestionCatalogueDataStore,
            ingestionCatalogueExtractors = listOf(
                // GitHub
                GitHubPullRequestIngestionCatalogueExtractor(),
                GitHubPullRequestReviewIngestionCatalogueExtractor(),
                GitHubPullRequestCommentIngestionCatalogueExtractor(),
                // GitLab
                GitLabPullRequestIngestionCatalogueExtractor(),
                GitLabPullRequestCommentIngestionCatalogueExtractor(),
                // Azure DevOps
                AzureDevOpsPullRequestIngestionCatalogueExtractor(),
                AzureDevOpsPullRequestCommentIngestionCatalogueExtractor(),
                // Bitbucket
                BitbucketPullRequestIngestionCatalogueExtractor(),
                BitbucketPullRequestCommentIngestionCatalogueExtractor(),
                // Bitbucket Data Center
                BitbucketDataCenterPullRequestIngestionCatalogueExtractor(),
                BitbucketDataCenterPullRequestCommentIngestionCatalogueExtractor(),
            ),
            lockProvider = prIngestionCatalogueExtractionLockProvider,
        )
    }

    val prIngestionCatalogueEmbeddingService by lazy {
        IngestionCatalogueEmbeddingService(
            ingestionCatalogueEmbedders = listOf(
                // GitHub
                GitHubPullRequestIngestionCatalogueEmbedder(),
                GitHubPullRequestReviewIngestionCatalogueEmbedder(),
                GitHubPullRequestCommentIngestionCatalogueEmbedder(),
                // GitLab
                GitLabPullRequestIngestionCatalogueEmbedder(),
                GitLabPullRequestCommentIngestionCatalogueEmbedder(),
                // Azure DevOps
                AzureDevOpsPullRequestIngestionCatalogueEmbedder(),
                AzureDevOpsPullRequestCommentIngestionCatalogueEmbedder(),
                // Bitbucket
                BitbucketPullRequestIngestionCatalogueEmbedder(),
                BitbucketPullRequestCommentIngestionCatalogueEmbedder(),
                // Bitbucket Data Center
                BitbucketDataCenterPullRequestIngestionCatalogueEmbedder(),
                BitbucketDataCenterPullRequestCommentIngestionCatalogueEmbedder(),
            ),
            lockProvider = prIngestionCatalogueEmbeddingLockProvider,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    configureRouting(
        serviceLifecycle = serviceLifecycle,
    )
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureBackgroundJobs(
        jobs = buildList {
            add(
                PollingBackgroundJob(
                    interval = 24.hours,
                    job = ExclusiveBackgroundJob(
                        job = pullRequestIngestionBackfillJob,
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 12.hours,
                    job = ExclusiveBackgroundJob(
                        GitHubInstallationMaintenanceJob(
                            scmAppApiFactory = scmAppApiFactory,
                            slackNotifier = slackNotifier,
                        ),
                    ),
                ),
            )

            add(
                // Bulk Ingests PR threads for newly onboarded repos
                PollingBackgroundJob(
                    interval = 1.minutes,
                    disabled = ingestionDisablementService::isDisabled,
                    job = ExclusiveBackgroundJob(
                        PullRequestBulkIngestionJob(
                            pullRequestBulkIngestionService = PullRequestBulkIngestionService(
                                scmRepoApiFactory = scmRepoApiFactory,
                                lockProvider = prBulkIngestionLockProvider,
                                pullRequestService = pullRequestService,
                                pullRequestCommentService = pullRequestCommentService,
                                pullRequestEventEnqueueServiceProvider = PullRequestEventEnqueueServiceProvider(
                                    pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                                ),
                                pullRequestReviewCommentService = pullRequestReviewCommentService,
                                scmMembershipMaintenance = scmMembershipMaintenance,
                                indexingAndEmbeddingService = indexingAndEmbeddingService,
                            ),
                        ),
                    ),
                ),
            )

            add(
                // Bulk Ingests PR threads for newly onboarded repos
                PollingBackgroundJob(
                    interval = 5.seconds,
                    job =
                        ExclusiveBackgroundJob(
                            PullRequestBulkIngestionCatalogueJob(
                                pullRequestBulkIngestionCatalogueService = PullRequestBulkIngestionCatalogueService(
                                    scmRawRepoApiFactory = scmRawRepoApiFactory,
                                    lockProvider = prBulkIngestionCatalogueLockProvider,
                                    pullRequestService = pullRequestService,
                                    scmRawPullRequestConverter = scmRawPullRequestConverter,
                                    pullRequestIngestionCatalogueEventEnqueueServiceProvider =
                                        PullRequestIngestionCatalogueEventEnqueueServiceProvider(
                                            pullRequestIngestionCatalogueEventEnqueueService = pullRequestIngestionCatalogueEventEnqueueService,
                                        ),
                                ),
                            ),
                        ),
                ),
            )

            add(
                // Bulk Ingests PR threads for newly onboarded repos
                PollingBackgroundJob(
                    interval = 5.seconds,
                    job = ExclusiveBackgroundJob(
                        PullRequestIngestionCatalogueEmbeddingJob(
                            ingestionCatalogueEmbeddingService = prIngestionCatalogueEmbeddingService,
                        ),
                    ),
                ),
            )

            add(
                // Bulk Ingests PR threads for newly onboarded repos
                PollingBackgroundJob(
                    interval = 5.seconds,
                    job = ExclusiveBackgroundJob(
                        PullRequestIngestionCatalogueExtractionJob(
                            ingestionCatalogueExtractionService = prIngestionCatalogueExtractionService,
                        ),
                    ),
                ),
            )

            add(
                // Ingests PR threads for newly onboarded repos
                PollingBackgroundJob(
                    interval = 1.minutes,
                    disabled = ingestionDisablementService::isDisabled,
                    job = ExclusiveBackgroundJob(
                        PullRequestIngestionOnboardingJob(
                            scmAppApiFactory = scmAppApiFactory,
                            lockProvider = prIngestionLockProvider,
                            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                        ),
                    ),
                ),
            )

            add(
                // Syncs/ingests PR threads for existing repos
                PollingBackgroundJob(
                    interval = 12.hours,
                    disabled = ingestionDisablementService::isDisabled,
                    job = ExclusiveBackgroundJob(
                        PullRequestIngestionSyncJob(
                            scmRepoApiFactory = scmRepoApiFactory,
                            lockProvider = prIngestionLockProvider,
                            pullRequestService = pullRequestService,
                            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                        ),
                    ),
                ),
            )

            add(
                // Syncs/ingests PR threads for existing repos using catalogue ingestion
                PollingBackgroundJob(
                    interval = 12.hours,
                    disabled = ingestionDisablementService::isDisabled,
                    job = ExclusiveBackgroundJob(
                        PullRequestIngestionCatalogueSyncJob(
                            scmRawRepoApiFactory = scmRawRepoApiFactory,
                            lockProvider = prIngestionLockProvider,
                            pullRequestService = pullRequestService,
                            scmRawPullRequestConverter = scmRawPullRequestConverter,
                            pullRequestIngestionCatalogueEventEnqueueService = pullRequestIngestionCatalogueEventEnqueueService,
                        ),
                    ),
                ),
            )

            repeat(POLLING_COUNT) {
                add(
                    // Bulk Ingests PR threads for newly onboarded repos
                    PollingBackgroundJob(
                        interval = 1.minutes,
                        disabled = ingestionDisablementService::isDisabled,
                        job = PullRequestBulkIngestionJob(
                            pullRequestBulkIngestionService = PullRequestBulkIngestionService(
                                scmRepoApiFactory = scmRepoApiFactory,
                                lockProvider = prBulkIngestionLockProvider,
                                pullRequestService = pullRequestService,
                                pullRequestCommentService = pullRequestCommentService,
                                pullRequestEventEnqueueServiceProvider = PullRequestEventEnqueueServiceProvider(
                                    pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                                ),
                                pullRequestReviewCommentService = pullRequestReviewCommentService,
                                scmMembershipMaintenance = scmMembershipMaintenance,
                                indexingAndEmbeddingService = indexingAndEmbeddingService,
                            ),
                        ),
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = ScmWebhookProcessingJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.hooksScmQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(scmEventWebhookHandler),
                            ),
                        ),
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = ScmEventProcessingJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.scmEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = ScmEventProcessor(
                                    scmTeamMaintenance = scmTeamMaintenance,
                                    repoSelectionService = repoSelectionService,
                                ),
                            ),
                        ),
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = PullRequestEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.pullRequestEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = pullRequestEventHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = PullRequestEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.pullRequestIngestionCatalogueEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = pullRequestIngestionCatalogueEventHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }

            add(
                // Bulk Ingests GitHub issues
                PollingBackgroundJob(
                    interval = 1.minutes,
                    job = ExclusiveBackgroundJob(
                        GitHubIssuesBulkIngestionJob(
                            scmRepoApiFactory = scmRepoApiFactory,
                            lockProvider = gitHubIssuesIngestionLockProvider,
                            gitHubIssuesIngestionService = gitHubIssuesIngestionService,
                            indexingAndEmbeddingService = indexingAndEmbeddingService,
                            scmMembershipMaintenance = scmMembershipMaintenance,
                        ),
                    ),
                ),
            )

            add(
                // Syncs GitHub Issues
                PollingBackgroundJob(
                    interval = 12.hours,
                    job = ExclusiveBackgroundJob(
                        GitHubIssuesIngestionSyncJob(
                            scmRepoApiFactory = scmRepoApiFactory,
                            lockProvider = gitHubIssuesIngestionLockProvider,
                            gitHubIssuesIngestionService = gitHubIssuesIngestionService,
                            scmMembershipMaintenance = scmMembershipMaintenance,
                        ),
                    ),
                ),
            )

            add(
                PollingBackgroundJob(
                    interval = 12.hours,
                    job = ExclusiveBackgroundJob(
                        GitHubDiscussionsIngestionJob(
                            lockProvider = gitHubIssuesIngestionLockProvider,
                            scmAppApiFactory = scmAppApiFactory,
                            embeddingEventEnqueueService = embeddingEventEnqueueService,
                        ),
                    ),
                ),
            )

            if (config.featureFlags.enablePullRequestSummaries) {
                add(
                    PollingBackgroundJob(
                        interval = 7.seconds,
                        job = pullRequestIngestionPipelineJob,
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    // Ingestion pull request summaries
                    PollingBackgroundJob(
                        interval = 5.seconds,
                        job = PullRequestSummariesIngestionEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.pullRequestSummariesIngestionQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    PullRequestSummariesCompletionEventHandler(
                                        pullRequestSummariesCompletionService = pullRequestSummariesIngestionService,
                                    ),
                                ),
                            ),
                        ),
                    ),
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    // Ingestion pull request summary
                    PollingBackgroundJob(
                        interval = 5.seconds,
                        job = PullRequestSummariesIngestionEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.pullRequestSummaryIngestionQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    PullRequestSummaryIngestionEventHandler(
                                        pullRequestSummaryIngestionService = pullRequestSummaryIngestionService,
                                    ),
                                ),
                            ),
                        ),
                    ),
                )
            }
        },
    )
    configureJvmMetrics()
}
