package com.nextchaptersoftware.searchservice

import com.aallam.openai.client.OpenAIHost
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApiImpl
import com.nextchaptersoftware.atlassian.api.NoopAtlassianAuthApi
import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.oauth.NoopOAuthTokenRefresher
import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UnencryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.aws.bedrock.anthropic.api.BedrockAnthropicCompletionsApi
import com.nextchaptersoftware.aws.bedrock.cohere.api.BedrockCohereRerankApi
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.aws.lambda.LambdaProviderFactory
import com.nextchaptersoftware.aws.lambda.StandardLambdaProviderFactory
import com.nextchaptersoftware.aws.rpc.StsProviderViaRpc
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.utils.BillingPlanService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.bot.toolbox.IsInvalidHumanQueryLengthFilter
import com.nextchaptersoftware.bot.toolbox.IsTrialExpiredFilter
import com.nextchaptersoftware.bot.toolbox.responses.PresetBotResponseService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationServiceImpl
import com.nextchaptersoftware.datasources.ConfluenceAccessService
import com.nextchaptersoftware.datasources.JiraAccessService
import com.nextchaptersoftware.datasources.ThreadAccessService
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.diff.utils.DiffChunker
import com.nextchaptersoftware.documents.embedding.services.PartitionedDocumentEmbeddingService
import com.nextchaptersoftware.documents.embedding.services.ThreadEmbeddingService
import com.nextchaptersoftware.documents.embedding.utils.InstallationIdProvider
import com.nextchaptersoftware.documents.mongo.scm.github.stores.GitHubPullRequestDocumentStore
import com.nextchaptersoftware.dsac.filter.DataSourceAccessControlFilterFactory
import com.nextchaptersoftware.dsac.provider.CodaDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.GoogleDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraAccessComputeService
import com.nextchaptersoftware.dsac.provider.JiraDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.LinearAccessComputeService
import com.nextchaptersoftware.dsac.provider.NotionDocumentAccessProvider
import com.nextchaptersoftware.embedding.config.EmbeddingSecretConfig
import com.nextchaptersoftware.embedding.encoding.EmbeddingEncoding
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.event.queue.dequeue.RealtimeEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.gemini.api.GeminiApiProvider
import com.nextchaptersoftware.gemini.config.GeminiApiConfig
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.google.services.GoogleWorkspaceServiceAccountKeyProvider
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.insight.refresh.InsightRefreshService
import com.nextchaptersoftware.integration.queue.redis.cache.NoopIngestionProgressServiceProvider
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.services.LinearTokenProvider
import com.nextchaptersoftware.links.LinkInstallationResolverFactory
import com.nextchaptersoftware.links.LinkProcessorFactory
import com.nextchaptersoftware.maintenance.events.queue.enqueue.MaintenanceEventEnqueueService
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.completion.AnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockAnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockConverseCompletionService
import com.nextchaptersoftware.ml.completion.CohereCompletionService
import com.nextchaptersoftware.ml.completion.DecisionCompletionService
import com.nextchaptersoftware.ml.completion.GeminiCompletionService
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.ml.completion.OpenAICompletionService
import com.nextchaptersoftware.ml.completion.RoundRobinCompletionService
import com.nextchaptersoftware.ml.doc.converter.AsanaDocConverter
import com.nextchaptersoftware.ml.doc.converter.CodaDocConverter
import com.nextchaptersoftware.ml.doc.converter.ConfluenceDocConverter
import com.nextchaptersoftware.ml.doc.converter.JiraDocConverter
import com.nextchaptersoftware.ml.doc.converter.PullRequestDocConverter
import com.nextchaptersoftware.ml.doc.converter.ThreadDocConverter
import com.nextchaptersoftware.ml.doc.rerank.services.BedrockCohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.CohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.DecisionDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.RoundRobinDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.VoyageApiConfiguration
import com.nextchaptersoftware.ml.doc.rerank.services.VoyageApiProvider
import com.nextchaptersoftware.ml.doc.rerank.services.VoyageDocumentRerankService
import com.nextchaptersoftware.ml.embedding.opensearch.store.OpenSearchEmbeddingStore
import com.nextchaptersoftware.ml.embedding.pinecone.store.PineconeEmbeddingStore
import com.nextchaptersoftware.ml.embedding.query.services.EmbeddingQueryService
import com.nextchaptersoftware.ml.embedding.query.services.filter.EmbeddingQueryFilterBuilder
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionDecorator
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionService
import com.nextchaptersoftware.ml.embedding.services.EmbeddingService
import com.nextchaptersoftware.ml.embedding.services.RoundRobinEmbeddingService
import com.nextchaptersoftware.ml.embedding.services.similarity.CosineSimilarityCalculator
import com.nextchaptersoftware.ml.functions.AWSLiveQueryMLFunction
import com.nextchaptersoftware.ml.functions.AsanaMLFunction
import com.nextchaptersoftware.ml.functions.CIMLFunctions
import com.nextchaptersoftware.ml.functions.CommitsMLFunctions
import com.nextchaptersoftware.ml.functions.DataSourceAccessMLFunctions
import com.nextchaptersoftware.ml.functions.EngagementMetricFunctions
import com.nextchaptersoftware.ml.functions.ExpertsMLFunctions
import com.nextchaptersoftware.ml.functions.FileMLFunctions
import com.nextchaptersoftware.ml.functions.GitHubForkedRepoMLFunctions
import com.nextchaptersoftware.ml.functions.GitHubIssuesMLFunctions
import com.nextchaptersoftware.ml.functions.GraphRagMLFunctions
import com.nextchaptersoftware.ml.functions.JiraMLFunctions
import com.nextchaptersoftware.ml.functions.LinearMLFunctions
import com.nextchaptersoftware.ml.functions.MemberSummaryMLFunctions
import com.nextchaptersoftware.ml.functions.PRCodeReviewMLFunctions
import com.nextchaptersoftware.ml.functions.PRDetailsMLFunctions
import com.nextchaptersoftware.ml.functions.PRSummaryMLFunctions
import com.nextchaptersoftware.ml.functions.RepoSummaryMLFunction
import com.nextchaptersoftware.ml.functions.SlackMLFunctions
import com.nextchaptersoftware.ml.functions.SuggestedQuestionsMLFunctions
import com.nextchaptersoftware.ml.functions.mongo.core.DefaultMongoUserAliasesResolver
import com.nextchaptersoftware.ml.functions.mongo.scm.DefaultMongoPRProviderTargetFactory
import com.nextchaptersoftware.ml.functions.mongo.scm.DefaultMongoRepoScopeResolver
import com.nextchaptersoftware.ml.functions.mongo.scm.PRSummaryMongoMLFunctions
import com.nextchaptersoftware.ml.functions.mongo.scm.PRTargets
import com.nextchaptersoftware.ml.functions.services.ScmCommitService
import com.nextchaptersoftware.ml.functions.services.SlackContentFilterService
import com.nextchaptersoftware.ml.inference.extractors.PrIdExtractor
import com.nextchaptersoftware.ml.inference.services.filter.DocumentTypeContentFilter
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.mongo.MongoNLQueryService
import com.nextchaptersoftware.ml.mongo.MongoQueryPlanner
import com.nextchaptersoftware.ml.mongo.MongoTargetCollection
import com.nextchaptersoftware.ml.mongo.OpenAiMongoCompletionService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.ml.services.MLFunctionMemberService
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAIApiProvider
import com.nextchaptersoftware.openai.api.delegates.AzureOpenAIApiProviderDelegate
import com.nextchaptersoftware.opensearch.api.OpenSearchApiConfiguration
import com.nextchaptersoftware.opensearch.api.OpenSearchApiProvider
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.opensearch.plugins.configureOpenSearch
import com.nextchaptersoftware.partitioning.ContentPartitioningProvider
import com.nextchaptersoftware.partitioning.ContentStorageStrategy
import com.nextchaptersoftware.partitioning.incremental.DecisionIncrementalPartitionTracking
import com.nextchaptersoftware.partitioning.incremental.StandardIncrementalPartitionTracking
import com.nextchaptersoftware.pinecone.api.PineconeApiConfiguration
import com.nextchaptersoftware.pinecone.api.PineconeApiProvider
import com.nextchaptersoftware.pinecone.api.PineconeControlPlaneApiProvider
import com.nextchaptersoftware.pinecone.config.PineconeConfig
import com.nextchaptersoftware.pinecone.index.PineconeIndexLoader
import com.nextchaptersoftware.pinecone.plugins.configurePinecone
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.rapid.services.RapidServiceProvider
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.utils.DefaultEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.search.events.queue.enqueue.SearchEventEnqueueService
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.indexing.events.queue.enqueue.SearchIndexingEventEnqueueService
import com.nextchaptersoftware.search.semantic.handlers.ApiQuestionEventHandler
import com.nextchaptersoftware.search.semantic.handlers.BotQuestionEventHandler
import com.nextchaptersoftware.search.semantic.handlers.EmbedThreadEventHandler
import com.nextchaptersoftware.search.semantic.handlers.GenerateFollowupSuggestionsEventHandler
import com.nextchaptersoftware.search.semantic.handlers.GenerateSampleQuestionsEventHandler
import com.nextchaptersoftware.search.semantic.handlers.MaybeBotQuestionEventHandler
import com.nextchaptersoftware.search.semantic.handlers.McpQuestionEventHandler
import com.nextchaptersoftware.search.semantic.handlers.PrefetchBotQuestionEventHandler
import com.nextchaptersoftware.search.semantic.handlers.RegressionEvalHandler
import com.nextchaptersoftware.search.semantic.handlers.RegressionEvalRunEvalHandler
import com.nextchaptersoftware.search.semantic.handlers.RegressionEvalSearchHandler
import com.nextchaptersoftware.search.semantic.handlers.ResolvePrefetchBotQuestionEventHandler
import com.nextchaptersoftware.search.semantic.services.SemanticSearchDocumentService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchQueryService
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.agents.DocumentRelevanceEvaluatorAgent
import com.nextchaptersoftware.search.semantic.services.api.ApiQuestionService
import com.nextchaptersoftware.search.semantic.services.asset.ScoredAssetContextCollector
import com.nextchaptersoftware.search.semantic.services.bot.HumanBotQuestionService
import com.nextchaptersoftware.search.semantic.services.bot.PrefetchBotQuestionService
import com.nextchaptersoftware.search.semantic.services.bot.ResolvePrefetchBotQuestionService
import com.nextchaptersoftware.search.semantic.services.bot.notifier.SlackQAValidationNotifier
import com.nextchaptersoftware.search.semantic.services.defence.MaliciousQueryDetectionService
import com.nextchaptersoftware.search.semantic.services.detection.RAGWorthinessDetectionService
import com.nextchaptersoftware.search.semantic.services.documentation.SlackDocumentRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.SlackThreadRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.StandardDocumentFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.StandardDocumentationValidationService
import com.nextchaptersoftware.search.semantic.services.eval.BotNotificationDeciderService
import com.nextchaptersoftware.search.semantic.services.eval.EvalService
import com.nextchaptersoftware.search.semantic.services.eval.RAGRegressionInstanceService
import com.nextchaptersoftware.search.semantic.services.eval.RegressionTestService
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionExecutor
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionService
import com.nextchaptersoftware.search.semantic.services.functions.ScmExpertService
import com.nextchaptersoftware.search.semantic.services.functions.TimeFilteredDocumentsMLFunctions
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolAskUnblockedWhy
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolConfluencePages
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolDispatcher
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolFailureDebugging
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolHistoricalContext
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolJiraIssues
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolPRDetails
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolService
import com.nextchaptersoftware.search.semantic.services.mcp.McpToolSlackConversationDetails
import com.nextchaptersoftware.search.semantic.services.presets.MessageDataSourcePresetService
import com.nextchaptersoftware.search.semantic.services.references.DemoReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InPromptReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesCleanupService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesResolverService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesToMarkdownLinksService
import com.nextchaptersoftware.search.semantic.services.references.MessageReferencesService
import com.nextchaptersoftware.search.semantic.services.references.ReferencesExpansionService
import com.nextchaptersoftware.search.semantic.services.references.ResponseReferenceResolver
import com.nextchaptersoftware.search.semantic.services.retrieval.ChatCompressionService
import com.nextchaptersoftware.search.semantic.services.retrieval.RepoExtractorService
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.GleanFilter
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorImpl
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorService
import com.nextchaptersoftware.search.semantic.services.sanitizer.CompositeResponseSanitizer
import com.nextchaptersoftware.search.semantic.services.sanitizer.DevelopedByOpenAIResponseSanitizer
import com.nextchaptersoftware.search.semantic.services.suggestions.MessageSuggestionsService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.search.services.query.factory.DocumentInsightContentService
import com.nextchaptersoftware.search.services.query.factory.SearchDecorator
import com.nextchaptersoftware.search.services.query.filter.DocumentSearchResultDecisionServiceProvider
import com.nextchaptersoftware.search.services.query.filter.SlackDecisionService
import com.nextchaptersoftware.search.services.query.filter.StandardSearchResultsFilter
import com.nextchaptersoftware.search.services.query.filter.ThreadSearchResultDecisionServiceProvider
import com.nextchaptersoftware.searchservice.handlers.SearchEventHandler
import com.nextchaptersoftware.searchservice.handlers.SearchPriorityEventHandler
import com.nextchaptersoftware.searchservice.jobs.GlobalRegressionTestJob
import com.nextchaptersoftware.searchservice.jobs.RAGRegressionJob
import com.nextchaptersoftware.searchservice.jobs.SearchEventJob
import com.nextchaptersoftware.searchservice.plugins.configureRouting
import com.nextchaptersoftware.semantic.bot.services.MessageService
import com.nextchaptersoftware.semantic.bot.services.NotifyBotService
import com.nextchaptersoftware.semantic.bot.services.ThreadParticipantService
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.lifecycle.ShutdownHookManager
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureCoroutineSchedulerMetrics
import com.nextchaptersoftware.service.plugins.configureCors
import com.nextchaptersoftware.service.plugins.configureDefaultHeaders
import com.nextchaptersoftware.service.plugins.configureForwardProxySupport
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.extractor.utils.SlackConversationSummaryPromptExtractor
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.AuthorizedSlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.topic.config.TopicConfig
import com.nextchaptersoftware.topic.insight.services.ClusterInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.DecisionInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.TopicExpertMappingService
import com.nextchaptersoftware.topic.insight.services.content.InsightContentModelService
import com.nextchaptersoftware.topic.insight.services.map.DecisionTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.QuestionAnswerThreadService
import com.nextchaptersoftware.topic.insight.services.map.StandardTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.TopicMappingRuleService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import com.nextchaptersoftware.web.events.queue.enqueue.WebEventEnqueueService
import io.ktor.server.application.Application
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

/**
 * The number of concurrent consumers for the search event queue.
 */
private const val CONSUMER_COUNT = 4
private const val PRIORITY_CONSUMER_COUNT = 2

@Suppress("LongMethod")
fun Application.module(
    config: GlobalConfig = GlobalConfig.INSTANCE,
    openSearchConfig: OpenSearchConfig = OpenSearchConfig.INSTANCE,
    pineconeConfig: PineconeConfig = PineconeConfig.INSTANCE,
    topicConfig: TopicConfig = TopicConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    geminiConfig: GeminiApiConfig = GeminiApiConfig.INSTANCE,
    lambdaProviderFactory: LambdaProviderFactory = StandardLambdaProviderFactory(),
    bedrockRuntimeProviderFactory: BedrockRuntimeProviderFactory = StandardBedrockRuntimeProviderFactory(),
    bedrockRuntimeAsyncProviderFactory: BedrockRuntimeAsyncProviderFactory = StandardBedrockRuntimeAsyncProviderFactory(),
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = emptyList()),
) {
    val openSearchApiProvider by lazy {
        OpenSearchApiProvider(
            config = OpenSearchApiConfiguration(
                baseApiUri = config.openSearch.baseApiUri.asUrl,
                timeout = config.openSearch.defaultTimeout,
                userName = config.openSearch.userName,
                password = config.openSearch.password,
            ),
        )
    }

    val openSearchEmbeddingStore by lazy {
        OpenSearchEmbeddingStore(
            indexName = openSearchConfig.openSearchIndex.indexName,
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchIndexLoader by lazy {
        OpenSearchIndexLoader(
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchLockProvider by lazy {
        LockProvider(type = LockType.OpenSearchLoader)
    }

    val pineconeApiProvider by lazy {
        PineconeApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeControlPlaneApiProvider by lazy {
        PineconeControlPlaneApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeEmbeddingStore by lazy {
        PineconeEmbeddingStore(pineconeApiProvider = pineconeApiProvider)
    }

    val pineconeIndexLoader by lazy {
        PineconeIndexLoader(
            pineconeControlPlaneApiProvider = pineconeControlPlaneApiProvider,
        )
    }

    val pineconeLockProvider by lazy {
        LockProvider(type = LockType.PineconeLoader)
    }

    val awsClientProvider by lazy {
        AWSClientProvider.from(
            region = ServiceInitializer.REGION.toRegion(),
        )
    }

    val slackApiProvider by lazy {
        SlackApiProvider()
    }

    val insiderService by lazy {
        InsiderService()
    }

    val bedrockRuntimeProvider by lazy {
        bedrockRuntimeProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockRuntimeAsyncProvider by lazy {
        bedrockRuntimeAsyncProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockAnthropicCompletionsApi by lazy {
        BedrockAnthropicCompletionsApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val bedrockCohereRerankApi by lazy {
        BedrockCohereRerankApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
        )
    }

    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val embeddingService by lazy {
        RoundRobinEmbeddingService(
            embeddingServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                EmbeddingService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val machineLearningCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                MachineLearningCompletionService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val rapidServiceProvider by lazy {
        RapidServiceProvider(
            config = config.rapid,
        )
    }

    val threadInsightIndexContentService by lazy {
        ThreadInsightIndexContentService()
    }

    val pullRequestInsightIndexContentService by lazy {
        PullRequestInsightIndexContentService()
    }

    val documentInsightContentService by lazy {
        DocumentInsightContentService()
    }

    val clientConfigService by lazy { ClientConfigService() }

    val embeddingSecretConfig by lazy {
        EmbeddingSecretConfig.INSTANCE
    }

    val embeddingEncoding by lazy {
        EmbeddingEncoding(
            embeddingContentConfig = embeddingSecretConfig.embedding,
        )
    }

    val botAccountService by lazy {
        InstallationBotAccountService()
    }

    val threadEmbeddingContentFilter by lazy {
        DocumentTypeContentFilter(
            botAccountService = botAccountService,
        )
    }

    val installationIdProvider by lazy {
        InstallationIdProvider()
    }

    val embeddingStoreFacade by lazy {
        EmbeddingStoreFacade(
            openSearchEmbeddingStore = openSearchEmbeddingStore,
            pineconeEmbeddingStore = pineconeEmbeddingStore,
        )
    }

    val partitionTrackingService by lazy {
        DecisionIncrementalPartitionTracking(
            standardIncrementalPartitionTracking = StandardIncrementalPartitionTracking(embeddingStoreFacade = embeddingStoreFacade),
        )
    }

    val contentPartitioningProvider by lazy {
        ContentPartitioningProvider(
            embeddingConfig = config.embedding,
        )
    }

    val contentStorageStrategy by lazy {
        ContentStorageStrategy()
    }

    val partitionedDocumentEmbeddingService by lazy {
        PartitionedDocumentEmbeddingService(
            embeddingEncoding = embeddingEncoding,
            contentFilter = threadEmbeddingContentFilter,
            embedder = embeddingService,
            contentPartitioningProvider = contentPartitioningProvider,
            contentStorageStrategy = contentStorageStrategy,
            embeddingStoreFacade = embeddingStoreFacade,
            partitionTrackingService = partitionTrackingService,
        )
    }

    val threadEmbeddingService by lazy {
        ThreadEmbeddingService(
            contentFilter = threadEmbeddingContentFilter,
            threadInsightIndexContentService = threadInsightIndexContentService,
            installationIdProvider = installationIdProvider,
            documentEmbeddingService = partitionedDocumentEmbeddingService,
        )
    }

    val inferenceTemplateService by lazy {
        MLInferenceTemplateService()
    }

    val searchPriorityEventEnqueueService by lazy {
        SearchPriorityEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchPriorityEventsQueueName,
                ),
            ),
        )
    }

    val searchEventEnqueueService by lazy {
        SearchEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchEventsQueueName,
                ),
            ),
        )
    }

    val inferenceService by lazy {
        MLInferenceService()
    }

    val planCapabilitiesService by lazy {
        PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
        )
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackDecisionService by lazy {
        SlackDecisionService(slackChannelAccessService = slackChannelAccessService)
    }

    val threadSearchResultDecisionProvider by lazy {
        ThreadSearchResultDecisionServiceProvider(slackDecisionService = slackDecisionService)
    }

    val documentDecisionServiceProvider by lazy {
        DocumentSearchResultDecisionServiceProvider()
    }

    val searchResultsFilter by lazy {
        StandardSearchResultsFilter(
            threadSearchResultDecisionProvider = threadSearchResultDecisionProvider,
            documentDecisionServiceProvider = documentDecisionServiceProvider,
        )
    }

    val searchDecorator by lazy {
        SearchDecorator(
            botAccountService = botAccountService,
            threadInsightIndexContentService = threadInsightIndexContentService,
            prInsightIndexContentService = pullRequestInsightIndexContentService,
            documentInsightContentService = documentInsightContentService,
            searchResultsFilter = searchResultsFilter,
        )
    }

    val reciprocalRankFusionService by lazy {
        ReciprocalRankFusionService()
    }

    val reciprocalRankFusionDecorator by lazy {
        ReciprocalRankFusionDecorator(
            reciprocalRankFusionService = reciprocalRankFusionService,
        )
    }

    val embeddingQueryFilterBuilder by lazy {
        EmbeddingQueryFilterBuilder()
    }

    val standardEmbeddingQueryService by lazy {
        EmbeddingQueryService(
            embeddingStoreFacade = embeddingStoreFacade,
            embedder = embeddingService,
            embeddingEncoding = embeddingEncoding,
            embeddingQueryFilterBuilder = embeddingQueryFilterBuilder,
            reciprocalRankFusionDecorator = reciprocalRankFusionDecorator,
            searchDecorator = searchDecorator,
        )
    }

    val webEventEnqueueService by lazy {
        WebEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.webEventsQueueName,
                ),
            ),
            progressServiceProvider = NoopIngestionProgressServiceProvider(),
        )
    }

    val documentationValidationService by lazy {
        StandardDocumentationValidationService(
            webEventEnqueueService = webEventEnqueueService,
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val searchIndexingEventEnqueueService by lazy {
        SearchIndexingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchIndexingEventsQueueName,
                ),
            ),
        )
    }

    val slackDocumentRetentionFilterService by lazy {
        SlackDocumentRetentionFilterService(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val indexingAndEmbeddingService by lazy {
        IndexingAndEmbeddingService(
            searchIndexingEventEnqueueService = searchIndexingEventEnqueueService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackThreadRetentionFilterService by lazy {
        SlackThreadRetentionFilterService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val documentFilterService by lazy {
        StandardDocumentFilterService(
            documentFilters = listOf(
                slackDocumentRetentionFilterService,
                slackThreadRetentionFilterService,
            ),
        )
    }

    val semanticSearchDocumentService by lazy {
        SemanticSearchDocumentService(
            embeddingQueryService = standardEmbeddingQueryService,
            documentationValidationService = documentationValidationService,
            documentFilterService = documentFilterService,
        )
    }

    val openAIApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openAI.defaultTimeout,
                token = config.openAI.apiKey,
            ),
        )
    }

    val openRouterApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openRouter.defaultTimeout,
                token = config.openRouter.apiKey,
                host = OpenAIHost(baseUrl = config.openRouter.baseApiUri),
            ),
        )
    }

    val azureGPT41OpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41DeploymentId,
    )

    val azureGPT41NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41NanoDeploymentId,
    )

    val azureGPT41MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41MiniDeploymentId,
    )

    val azureGPT5MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5MiniDeploymentId,
    )

    val azureGPT5NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5NanoDeploymentId,
    )

    val anthropicApiConfiguration by lazy {
        AnthropicApiConfiguration(
            baseApiUri = config.anthropic.baseApiUri.asUrl,
            timeout = config.anthropic.defaultTimeout,
            token = config.anthropic.apiKey,
        )
    }

    val anthropicApiProvider by lazy {
        AnthropicApiProvider(
            config = anthropicApiConfiguration,
        )
    }

    val anthropicCompletionService by lazy {
        AnthropicCompletionService(
            anthropicApiProvider = anthropicApiProvider,
        )
    }

    val bedrockAnthropicCompletionService by lazy {
        BedrockAnthropicCompletionService(
            bedrockAnthropicCompletionsApi = bedrockAnthropicCompletionsApi,
        )
    }

    val bedrockConverseCompletionService by lazy {
        BedrockConverseCompletionService(
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val roundRobinAnthropicCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = listOf(
                anthropicCompletionService,
                bedrockAnthropicCompletionService,
            ),
        )
    }

    val cohereApiProvider by lazy {
        CohereApiProvider(
            config = CohereApiConfiguration(config),
        )
    }

    val cohereCompletionService by lazy {
        CohereCompletionService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val geminiApiProvider by lazy {
        GeminiApiProvider(
            config = geminiConfig,
        )
    }

    val geminiCompletionService by lazy {
        GeminiCompletionService(
            geminiApiProvider = geminiApiProvider,
        )
    }

    val roundRobinGPT41MiniCompletionService = RoundRobinCompletionService(
        completionServices = azureGPT41MiniApiProviders.plus(openAIApiProvider).map {
            OpenAICompletionService(
                openAIApiProvider = it,
            )
        },
    )

    val completionService by lazy {
        DecisionCompletionService(
            machineLearningCompletionService = machineLearningCompletionService,
            openAICompletionService = OpenAICompletionService(
                openAIApiProvider = openAIApiProvider,
                useResponsesApi = config.openAI.useResponsesApi,
            ),
            openRouterCompletionService = OpenAICompletionService(
                openAIApiProvider = openRouterApiProvider,
                useResponsesApi = config.openAI.useResponsesApi,
            ),
            bedrockAnthropicCompletionService = bedrockAnthropicCompletionService,
            anthropicCompletionService = anthropicCompletionService,
            roundRobinGPT41CompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41OpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT41NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT5MiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5MiniApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT5NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
            cohereCompletionService = cohereCompletionService,
            geminiCompletionService = geminiCompletionService,
            roundRobinGPT41MiniCompletionService = roundRobinGPT41MiniCompletionService,
            bedrockConverseCompletionService = bedrockConverseCompletionService,
        )
    }

    val similarityService by lazy {
        CosineSimilarityCalculator(
            embedder = embeddingService,
        )
    }

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val promptCompilerService by lazy {
        PromptCompilerService(
            scmWebFactory = scmWebFactory,
        )
    }

    val prIdExtractor by lazy {
        PrIdExtractor()
    }

    val responseReferenceResolver by lazy {
        ResponseReferenceResolver(prIdExtractor)
    }

    val inPromptReferenceResolver by lazy {
        InPromptReferenceResolver()
    }

    val messageReferencesService by lazy {
        MessageReferencesService()
    }

    val messageDataSourcePresetService by lazy {
        MessageDataSourcePresetService()
    }

    val compositeResponseSanitizer by lazy {
        CompositeResponseSanitizer(DevelopedByOpenAIResponseSanitizer())
    }

    val cohereDocumentRerankService by lazy {
        CohereDocumentRerankService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val bedrockCohereDocumentRerankService by lazy {
        BedrockCohereDocumentRerankService(
            bedrockCohereRerankApi = bedrockCohereRerankApi,
        )
    }

    val voyageApiProvider by lazy {
        VoyageApiProvider(
            config = VoyageApiConfiguration(config),
        )
    }

    val voyageDocumentRerankService by lazy {
        VoyageDocumentRerankService(
            voyageApiProvider = voyageApiProvider,
        )
    }

    val documentRerankService by lazy {
        DecisionDocumentRerankService(
            cohereDocumentRerankService = cohereDocumentRerankService,
            bedrockCohereDocumentRerankService = bedrockCohereDocumentRerankService,
            roundRobinCohereDocumentRerankService = RoundRobinDocumentRerankService(
                documentRerankServices = listOf(
                    cohereDocumentRerankService,
                    bedrockCohereDocumentRerankService,
                ),
            ),
            voyageDocumentRerankService = voyageDocumentRerankService,
        )
    }

    val chatCompressionService by lazy {
        ChatCompressionService(
            reducerCompletionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            inferenceService = inferenceService,
        )
    }

    val inlineReferencesResolverService by lazy {
        InlineReferencesResolverService(
            inlineReferencesResolutionTimeout = config.search.inlineReferencesResolutionTimeout,
            inlineReferencesResolutionCompletionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
        )
    }

    val inlineReferencesToMarkdownLinksService by lazy {
        InlineReferencesToMarkdownLinksService()
    }

    val inlineReferencesCleanupService by lazy {
        InlineReferencesCleanupService()
    }

    val referencesExpansionService by lazy {
        ReferencesExpansionService()
    }

    val enterpriseAppConfigOrgIdsResolver by lazy {
        DefaultEnterpriseAppConfigOrgIdsResolver()
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = scmWebFactory,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val userSecretConfig by lazy {
        UserSecretConfig.INSTANCE
    }

    val userSecretServiceRSA by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = RSACryptoSystem.RSADecryption(
                privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                modulusBitLength = 4096,
            ),
        )
    }

    val userSecretService by lazy {
        // TODO: remove once all have been migrated to `userSecretServiceRSA`
        userSecretServiceRSA
    }

    val userSecretServiceAES by lazy {
        val key = AESCryptoSystem.importKey(
            userSecretConfig.encryption.userSecretsAesKey.value,
        )
        UserSecretService(
            encryption = AESCryptoSystem.AESEncryption(key),
            decryption = AESCryptoSystem.AESDecryption(key),
        )
    }

    val userSecretServiceResolver by lazy {
        UserSecretServiceResolver(
            userSecretServiceRSA = userSecretServiceRSA,
            userSecretServiceAES = userSecretServiceAES,
        )
    }

    val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmAppApiFactory by lazy {
        ScmAppApiFactory(
            scmConfig = scmConfig,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmTeamApiFactory by lazy {
        ScmTeamApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val repoComputeService by lazy {
        LocalRepoComputeService(
            scmTeamApiFactory = scmTeamApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    val repoExtractorService by lazy {
        RepoExtractorService(
            repoAccessService = repoAccessService,
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
        )
    }

    val memberService by lazy {
        MemberService()
    }

    val threadParticipantService by lazy {
        ThreadParticipantService()
    }

    val scmRepoApiFactory by lazy {
        ScmRepoApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmAppApiFactory = scmAppApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val repoFocusService by lazy {
        RepoFocusService()
    }

    val unencryptedTokenPersistence by lazy {
        UnencryptedTokenPersistence(identityStore = Stores.identityStore)
    }

    val confluenceAuthApi by lazy {
        config.providers.confluence?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val asanaDocConverter by lazy {
        AsanaDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val confluenceAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = confluenceAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val confluenceCloudApiProvider by lazy {
        ConfluenceCloudApiProvider()
    }

    val jiraAuthApi by lazy {
        config.providers.jira?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val jiraAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = jiraAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val jiraApiProvider by lazy {
        JiraApiProvider()
    }

    val googleOAuthRefreshService by lazy {
        UserSecretOAuthRefreshService(
            tokenRefresher = NoopOAuthTokenRefresher(), // Not used for Google OAuth
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretService),
        )
    }

    val googleWorkspaceServiceAccountKeyProvider by lazy {
        GoogleWorkspaceServiceAccountKeyProvider(
            userSecretService = userSecretServiceAES,
        )
    }

    val googleCredentialProvider by lazy {
        config.providers.googleDrive?.let {
            GoogleCredentialProvider(
                config = it,
                oAuthRefreshService = googleOAuthRefreshService,
                googleWorkspaceServiceAccountKeyProvider = googleWorkspaceServiceAccountKeyProvider,
            )
        }
    }

    val googleApiProvider by lazy {
        GoogleApiProvider()
    }

    val linearTokenProvider by lazy {
        LinearTokenProvider(
            userSecretService = userSecretService,
        )
    }

    val linearAccessComputeService by lazy {
        config.providers.linear?.let {
            LinearAccessComputeService(
                apiProvider = LinearApiProvider(
                    config = it,
                ),
                linearTokenProvider = linearTokenProvider,
            )
        }
    }

    val googleDocumentAccessProvider by lazy {
        googleCredentialProvider?.let {
            GoogleDocumentAccessProvider(
                googleCredentialProvider = it,
                googleApiProvider = googleApiProvider,
            )
        }
    }

    val dataSourceAccessControlFilterFactory by lazy {
        DataSourceAccessControlFilterFactory(
            codaDocumentAccessProvider = CodaDocumentAccessProvider(),
            confluenceDataCenterDocumentAccessProvider = ConfluenceDataCenterDocumentAccessProvider(),
            confluenceDocumentAccessProvider = ConfluenceDocumentAccessProvider(
                atlassianTokenProvider = confluenceAtlassianTokenProvider,
                confluenceCloudApiProvider = confluenceCloudApiProvider,
            ),
            jiraDocumentAccessProvider = JiraDocumentAccessProvider(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraProjectComputeService = JiraAccessComputeService(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraDataCenterDocumentAccessProvider = JiraDataCenterDocumentAccessProvider(),
            googleDocumentAccessProvider = googleDocumentAccessProvider,
            linearAccessComputeService = linearAccessComputeService,
            notionDocumentAccessProvider = NotionDocumentAccessProvider(),
        )
    }

    val commitService by lazy {
        ScmCommitService(
            scmRepoApiFactory = scmRepoApiFactory,
            repoFocusService = repoFocusService,
            completionService = completionService,
            repoAccessService = repoAccessService,
        )
    }

    val repoSummaryMLFunction by lazy {
        RepoSummaryMLFunction(
            repoFocusService = repoFocusService,
            repoAccessService = repoAccessService,
            commitService = commitService,
        )
    }

    val topicExpertMappingService by lazy {
        TopicExpertMappingService(
            topicConfig = topicConfig,
            machineLearningModelsApi = machineLearningApiProviders.first().machineLearningModelsApi,
        )
    }

    val semanticDocumentRetriever by lazy {
        SemanticDocumentRetriever(
            embedder = embeddingService,
            semanticSearchDocumentService = semanticSearchDocumentService,
            documentRerankService = documentRerankService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
            repoFocusService = repoFocusService,
            repoAccessService = repoAccessService,
            planCapabilitiesService = planCapabilitiesService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val expertService by lazy {
        ScmExpertService(
            semanticDocumentRetriever = semanticDocumentRetriever,
            templateService = inferenceTemplateService,
            scmCommitService = commitService,
            expertFunctionTimeout = config.search.functions.expertsFunctionTimeout,
            useExpertsUsingTopics = config.search.useExpertsUsingTopics,
            topicExpertMappingService = topicExpertMappingService,
        )
    }

    val threadAccessService by lazy {
        ThreadAccessService(
            repoAccessService = repoAccessService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
        )
    }

    val jiraAccessService by lazy {
        JiraAccessService(
            atlassianTokenProvider = jiraAtlassianTokenProvider,
            jiraApiProvider = jiraApiProvider,
        )
    }

    val mlFunctionMemberService by lazy {
        MLFunctionMemberService(
            completionService = completionService,
            memberService = memberService,
        )
    }

    val memberSummaryMLFunctions by lazy {
        MemberSummaryMLFunctions(
            memberService = memberService,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val awsLiveQueryMLFunction by lazy {
        if (config.featureFlags.enableAWSLiveQuery) {
            AWSLiveQueryMLFunction(
                lambdaProvider = lambdaProviderFactory.generate(
                    awsClientProvider = AWSClientProvider.from(
                        region = ServiceInitializer.REGION.toRegion(),
                    ),
                ),
                stsProvider = StsProviderViaRpc {
                    RpcFacade.withProxyProvider().forSearchService()
                },
                completionService = completionService,
                awsQueryFunctionTimeout = config.search.functions.awsQueryFunctionTimeout,
            )
        } else {
            null
        }
    }

    val graphRagMLFunctions by lazy {
        if (config.featureFlags.enableGraphRag) {
            GraphRagMLFunctions(
                machinelearningApiProvider = machineLearningApiProviders.first(),
                graphRagFunctionTimeout = config.search.functions.graphRagFunctionTimeout,
            )
        } else {
            null
        }
    }
    val jiraDocConverter by lazy {
        JiraDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val dataAccessMLFunctions by lazy {
        DataSourceAccessMLFunctions()
    }

    val asanaMLFunctions by lazy {
        AsanaMLFunction(
            dsacFilterFactory = dataSourceAccessControlFilterFactory,
            asanaDocConverter = asanaDocConverter,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val jiraMLFunctions by lazy {
        JiraMLFunctions(
            dsacFilterFactory = dataSourceAccessControlFilterFactory,
            jiraAccessService = jiraAccessService,
            jiraDocConverter = jiraDocConverter,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val linearMLFunctions by lazy {
        LinearMLFunctions(
            mlFunctionMemberService = mlFunctionMemberService,
            threadAccessService = threadAccessService,
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val githubIssuesMLFunctions by lazy {
        GitHubIssuesMLFunctions(
            threadInsightIndexContentService = threadInsightIndexContentService,
            threadAccessService = threadAccessService,
        )
    }

    val gitHubForkedRepoMLFunctions by lazy {
        GitHubForkedRepoMLFunctions(
            scmRepoApiFactory = scmRepoApiFactory,
            scmAppApiFactory = scmAppApiFactory,
        )
    }

    val fileMLFunctions by lazy {
        FileMLFunctions(
            commitService = commitService,
        )
    }

    val prSummaryMLFunctions by lazy {
        PRSummaryMLFunctions(
            commitService = commitService,
            enablePRMongoSummary = config.search.enablePRMongoSummary,
        )
    }

    val prDetailsMLFunctions by lazy {
        PRDetailsMLFunctions(
            commitService = commitService,
        )
    }

    val diffChunker by lazy {
        DiffChunker()
    }

    val prCodeReviewMLFunctions by lazy {
        PRCodeReviewMLFunctions(
            completionService = completionService,
            scmRepoApiFactory = scmRepoApiFactory,
            repoAccessService = repoAccessService,
            repoFocusService = repoFocusService,
            diffChunker = diffChunker,
        )
    }

    val ciMLFunctions by lazy {
        CIMLFunctions(
            commitService = commitService,
            ciFunctionTimeout = config.search.functions.ciFunctionTimeout,
        )
    }

    val commitsMLFunctions by lazy {
        CommitsMLFunctions(
            commitService = commitService,
        )
    }

    val expertsMLFunctions by lazy {
        ExpertsMLFunctions(
            expertService = expertService,
        )
    }

    val engagementMetricFunctions by lazy {
        EngagementMetricFunctions()
    }

    val timeFilteredDocumentsMLFunctions by lazy {
        TimeFilteredDocumentsMLFunctions(
            embeddingQueryService = standardEmbeddingQueryService,
        )
    }

    val slackTokenService by lazy {
        SlackTokenService(
            userSecretService = userSecretService,
        )
    }

    val slackChannelResolver by lazy {
        SlackChannelResolver()
    }

    val authorizedSlackChannelResolver by lazy {
        AuthorizedSlackChannelResolver(
            slackChannelAccessService = slackChannelAccessService,
            slackChannelResolver = slackChannelResolver,
        )
    }

    val slackConversationSummaryPromptExtractor by lazy {
        SlackConversationSummaryPromptExtractor()
    }

    val slackContentFilterService by lazy {
        SlackContentFilterService(
            completionService = completionService,
        )
    }

    val slackMLFunctions by lazy {
        SlackMLFunctions(
            slackTokenService = slackTokenService,
            authorizedSlackChannelResolver = authorizedSlackChannelResolver,
            slackSummaryFunctionTimeout = config.search.functions.slackSummaryFunctionTimeout,
            slackConversationSummaryPromptExtractor = slackConversationSummaryPromptExtractor,
            slackContentFilterService = slackContentFilterService,
        )
    }

    val confluenceAccessService by lazy {
        ConfluenceAccessService(
            atlassianTokenProvider = confluenceAtlassianTokenProvider,
            confluenceCloudApiProvider = confluenceCloudApiProvider,
        )
    }

    val dataSourcePresetConfigurationService by lazy {
        DataSourcePresetConfigurationServiceImpl(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val sampleQuestionGenerator by lazy {
        SampleQuestionGeneratorImpl(
            completionService = completionService,
            repoAccessService = repoAccessService,
            repoFocusService = repoFocusService,
            semanticDocumentRetriever = semanticDocumentRetriever,
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
            promptCompilerService = promptCompilerService,
            scmCommitService = commitService,
            documentTimeout = config.search.documentRetrievalTimeout,
        )
    }

    val suggestedQuestionsMLFunctions by lazy {
        SuggestedQuestionsMLFunctions(
            templateService = inferenceTemplateService,
            sampleQuestionGenerator = sampleQuestionGenerator,
        )
    }

    val mongoCompletionService by lazy {
        OpenAiMongoCompletionService(api = openAIApiProvider.openAICompletionsApi)
    }

    val mongoQueryPlanner by lazy {
        MongoQueryPlanner(completion = mongoCompletionService)
    }

    val mongoNLQueryService by lazy {
        MongoNLQueryService(planner = mongoQueryPlanner)
    }

    val mongoRepoScopeResolver by lazy {
        DefaultMongoRepoScopeResolver(scmCommitService = commitService)
    }

    val mongoUserAliasesResolver by lazy {
        DefaultMongoUserAliasesResolver()
    }

    val mongoPRProviderTargetFactory by lazy {
        DefaultMongoPRProviderTargetFactory(
            targets = PRTargets(
                github = MongoTargetCollection.FromStore(GitHubPullRequestDocumentStore()),
            ),
        )
    }

    val prSummaryMongoMLFunctions by lazy {
        if (config.search.enablePRMongoSummary) {
            PRSummaryMongoMLFunctions(
                mongoRepoScopeResolver = mongoRepoScopeResolver,
                mongoUserAliasesResolver = mongoUserAliasesResolver,
                targetFactory = mongoPRProviderTargetFactory,
                mongo = mongoNLQueryService,
                prSummaryMLFunctions = prSummaryMLFunctions,
                timeout = config.search.functions.prMongoSummaryTimeout,
            )
        } else {
            null
        }
    }

    val mlFunctionService by lazy {
        MLFunctionService(
            functions = listOfNotNull(
                awsLiveQueryMLFunction,
                asanaMLFunctions,
                ciMLFunctions,
                commitsMLFunctions,
                engagementMetricFunctions,
                expertsMLFunctions,
                fileMLFunctions,
                githubIssuesMLFunctions,
                graphRagMLFunctions,
                jiraMLFunctions,
                linearMLFunctions,
                memberSummaryMLFunctions,
                prDetailsMLFunctions,
                prSummaryMLFunctions,
                prSummaryMongoMLFunctions,
                prCodeReviewMLFunctions,
                repoSummaryMLFunction,
                slackMLFunctions,
                timeFilteredDocumentsMLFunctions,
                suggestedQuestionsMLFunctions,
                dataAccessMLFunctions,
                gitHubForkedRepoMLFunctions,
            ),
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            globalFunctionTimeout = config.search.functions.globalFunctionTimeout,
        )
    }

    val demoReferenceResolver by lazy {
        DemoReferenceResolver()
    }

    val maliciousQueryDetectionService by lazy {
        MaliciousQueryDetectionService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val ragWorthinessDetectionService by lazy {
        RAGWorthinessDetectionService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
            semanticSearchDocumentService = semanticSearchDocumentService,
        )
    }

    val insightContentModelService by lazy {
        InsightContentModelService(
            threadInsightIndexContentService = threadInsightIndexContentService,
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val questionAnswerThreadService by lazy {
        QuestionAnswerThreadService(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val topicMappingRuleService by lazy {
        TopicMappingRuleService(
            questionAnswerThreadService = questionAnswerThreadService,
        )
    }

    val documentRelevanceEvaluatorAgent by lazy {
        DocumentRelevanceEvaluatorAgent(
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
        )
    }

    val mlFunctionExecutor by lazy {
        MLFunctionExecutor(
            mlFunctionService = mlFunctionService,
        )
    }

    val pullRequestDocConverter by lazy {
        PullRequestDocConverter(
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val threadDocConverter by lazy {
        ThreadDocConverter(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val confluenceDocConverter by lazy {
        ConfluenceDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val codaDocConverter by lazy {
        CodaDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val linkProcessorFactory by lazy {
        LinkProcessorFactory(
            installationResolverFactory = LinkInstallationResolverFactory(
                asanaDocConverter = asanaDocConverter,
                confluenceAccessService = confluenceAccessService,
                jiraAccessService = jiraAccessService,
                jiraDocConverter = jiraDocConverter,
                pullRequestDocConverter = pullRequestDocConverter,
                repoAccessService = repoAccessService,
                scmRepoApiFactory = scmRepoApiFactory,
                threadAccessService = threadAccessService,
                threadDocConverter = threadDocConverter,
                confluenceDocConverter = confluenceDocConverter,
                codaDocConverter = codaDocConverter,
            ),
        )
    }

    val documentEvaluationRetriever by lazy {
        DocumentEvaluationRetriever(
            linkProcessorFactory = linkProcessorFactory,
            semanticDocumentRetriever = semanticDocumentRetriever,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            documentRelevanceEvaluatorAgent = documentRelevanceEvaluatorAgent,
            mlFunctionExecutor = mlFunctionExecutor,
            embedder = embeddingService,
        )
    }

    val postRetrievalFilters by lazy {
        listOf(
            GleanFilter(),
        )
    }

    val maintenanceEventEnqueueService by lazy {
        MaintenanceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.maintenanceEventsQueueName,
                ),
            ),
        )
    }

    val scoreAssetContextCollector by lazy {
        ScoredAssetContextCollector(
            machineLearningApiProvider = machineLearningApiProviders.first(),
        )
    }

    val semanticSearchQueryService by lazy {
        SemanticSearchQueryService(
            semanticDocumentRetriever = semanticDocumentRetriever,
            documentEvaluationRetriever = documentEvaluationRetriever,
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            inPromptReferenceResolver = inPromptReferenceResolver,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            responseReferenceResolver = responseReferenceResolver,
            demoReferenceResolver = demoReferenceResolver,
            responseSanitizer = compositeResponseSanitizer,
            documentRerankService = documentRerankService,
            chatCompressionService = chatCompressionService,
            inlineReferencesResolverService = inlineReferencesResolverService,
            inlineReferencesToMarkdownLinksService = inlineReferencesToMarkdownLinksService,
            inlineReferencesCleanupService = inlineReferencesCleanupService,
            referencesExpansionService = referencesExpansionService,
            priorityEventEnqueueService = searchPriorityEventEnqueueService,
            repoExtractorService = repoExtractorService,
            maliciousQueryDetectionService = maliciousQueryDetectionService,
            maliciousQueryDetectionTimeout = config.search.maliciousQueryDetectionTimeout,
            ragWorthinessDetectionService = ragWorthinessDetectionService,
            ragWorthinessDetectionTimeout = config.search.ragWorthinessDetectionTimeout,
            embedder = embeddingService,
            documentRetrievalTimeout = config.search.documentRetrievalTimeout,
            repoExtractionTimeout = config.search.repoExtractionTimeout,
            queryCompressionTimeout = config.search.queryCompressionTimeout,
            postRetrievalFilters = postRetrievalFilters,
            maintenanceEventEnqueueService = maintenanceEventEnqueueService,
            ragSkipDetectionEnabled = config.featureFlags.enableRagSkipDetection,
            scoredAssetContextCollector = scoreAssetContextCollector,
        )
    }

    val clusterInsightTopicsService by lazy {
        ClusterInsightTopicsService(
            insightContentModelService = insightContentModelService,
            machineLearningApiProvider = machineLearningApiProviders.first(),
            topicConfig = topicConfig,
        )
    }

    val insightTopicsService by lazy {
        DecisionInsightTopicsService(
            clusterInsightTopicsService = clusterInsightTopicsService,
        )
    }

    val insightRefreshService by lazy {
        InsightRefreshService()
    }

    val topicMappingService by lazy {
        DecisionTopicMappingService(
            delegate = StandardTopicMappingService(
                topicMappingRuleService = topicMappingRuleService,
                insightTopicsService = insightTopicsService,
                insightRefreshService = insightRefreshService,
            ),
        )
    }

    val slackChannelFilterService by lazy {
        SlackChannelFilterService(
            clientConfigService = clientConfigService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val slackQAValidationNotifier by lazy {
        SlackQAValidationNotifier(
            slackApiProvider = slackApiProvider,
            slackConfig = config.providers.slack,
            slackChannelFilterService = slackChannelFilterService,
        )
    }

    val billingPlanService by lazy {
        BillingPlanService(
            orgBillingStore = Stores.orgBillingStore,
        )
    }

    val isTrialExpiredFilter by lazy {
        IsTrialExpiredFilter(
            billingPlanService = billingPlanService,
        )
    }

    val isInvalidHumanQueryLengthFilter by lazy {
        IsInvalidHumanQueryLengthFilter()
    }

    val presetBotResponseService by lazy {
        PresetBotResponseService(
            isTrialExpiredFilter = isTrialExpiredFilter,
            isInvalidHumanQueryLengthFilter = isInvalidHumanQueryLengthFilter,
        )
    }

    val humanBotQuestionService by lazy {
        HumanBotQuestionService(
            inferenceService = inferenceService,
            semanticSearchQueryService = semanticSearchQueryService,
            slackNotifier = slackNotifier,
            templateService = inferenceTemplateService,
            topicMappingService = topicMappingService,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            slackQAValidationNotifier = slackQAValidationNotifier,
            unblockedQAHistoricalMessageLimit = config.search.unblockedQAHistoricalMessageLimit,
            historicalMessageSizeLimit = config.search.historicalMessageSizeLimit,
            maxConversationHistoryToPromptRatio = config.search.maxConversationHistoryToPromptRatio,
            useCheatCodes = config.search.useCheatCodes,
            presetBotResponseService = presetBotResponseService,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        )
    }

    val prefetchBotQuestionService by lazy {
        PrefetchBotQuestionService(
            inferenceService = inferenceService,
            semanticSearchQueryService = semanticSearchQueryService,
            slackNotifier = slackNotifier,
            templateService = inferenceTemplateService,
            topicMappingService = topicMappingService,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            slackQAValidationNotifier = slackQAValidationNotifier,
        )
    }

    val resolvePrefetchBotQuestionService by lazy {
        ResolvePrefetchBotQuestionService(
            inferenceService = inferenceService,
            semanticSearchQueryService = semanticSearchQueryService,
            slackNotifier = slackNotifier,
            templateService = inferenceTemplateService,
            topicMappingService = topicMappingService,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            slackQAValidationNotifier = slackQAValidationNotifier,
        )
    }

    val messageSuggestionsService by lazy {
        MessageSuggestionsService(
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
        )
    }

    val sampleQuestionGeneratorService by lazy {
        SampleQuestionGeneratorService(
            sampleQuestionGenerator = sampleQuestionGenerator,
            templateService = inferenceTemplateService,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        )
    }

    val messageService by lazy {
        MessageService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = botAccountService,
        )
    }

    val billingEventEnqueueService by lazy {
        BillingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.billingEventsQueueName,
                ),
            ),
        )
    }

    val notifyBotService by lazy {
        NotifyBotService(
            messageService = messageService,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
            participantService = threadParticipantService,
            billingEventEnqueueService = billingEventEnqueueService,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        )
    }

    val botNotificationDeciderService by lazy {
        BotNotificationDeciderService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val mcpToolAskUnblockedWhy by lazy {
        McpToolAskUnblockedWhy(
            templateService = inferenceTemplateService,
            semanticSearchQueryService = semanticSearchQueryService,
            documentEvaluationRetriever = documentEvaluationRetriever,
            promptCompilerService = promptCompilerService,
        )
    }

    val mcpToolFailureDebugging by lazy {
        McpToolFailureDebugging(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
        )
    }

    val mcpToolHistoricalContext by lazy {
        McpToolHistoricalContext(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
        )
    }

    val mcpToolPRDetails by lazy {
        McpToolPRDetails(
            commitService = commitService,
            linkProcessorFactory = linkProcessorFactory,
            repoAccessService = repoAccessService,
        )
    }

    val mcpToolJiraIssues by lazy {
        McpToolJiraIssues(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
        )
    }

    val mcpToolSlackConversationDetails by lazy {
        McpToolSlackConversationDetails(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
        )
    }

    val mcpToolConfluencePages by lazy {
        McpToolConfluencePages(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
        )
    }

    val mcpToolDispatcher by lazy {
        McpToolDispatcher(
            mcpToolAskUnblockedWhy = mcpToolAskUnblockedWhy,
            mcpToolFailureDebugging = mcpToolFailureDebugging,
            mcpToolHistoricalContext = mcpToolHistoricalContext,
            mcpToolPRDetails = mcpToolPRDetails,
            mcpToolJiraIssues = mcpToolJiraIssues,
            mcpToolSlackConversationDetails = mcpToolSlackConversationDetails,
            mcpToolConfluencePages = mcpToolConfluencePages,
        )
    }

    val apiQuestionService by lazy {
        ApiQuestionService(
            templateService = inferenceTemplateService,
            semanticSearchQueryService = semanticSearchQueryService,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
            slackNotifier = slackNotifier,
        )
    }

    val mcpToolService by lazy {
        McpToolService(
            mcpToolDispatcher = mcpToolDispatcher,
            slackNotifier = slackNotifier,
        )
    }

    val searchPriorityEventHandler by lazy {
        SearchPriorityEventHandler(
            apiQuestionEventHandler = ApiQuestionEventHandler(
                apiQuestionService = apiQuestionService,
            ),
            botQuestionEventHandler = BotQuestionEventHandler(
                humanBotQuestionService = humanBotQuestionService,
                searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
                templateService = inferenceTemplateService,
            ),
            maybeBotQuestionEventHandler = MaybeBotQuestionEventHandler(
                notifyBotService = notifyBotService,
                botNotificationDeciderService = botNotificationDeciderService,
            ),
            prefetchBotQuestionEventHandler = PrefetchBotQuestionEventHandler(
                prefetchBotQuestionService = prefetchBotQuestionService,
            ),
            resolvePrefetchBotQuestionEventHandler = ResolvePrefetchBotQuestionEventHandler(
                resolvePrefetchBotQuestionService = resolvePrefetchBotQuestionService,
            ),
            generateFollowupSuggestionsEventHandler = GenerateFollowupSuggestionsEventHandler(
                messageSuggestionsService = messageSuggestionsService,
            ),
            generateSampleQuestionsEventHandler = GenerateSampleQuestionsEventHandler(
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
            ),
            mcpQuestionEventHandler = McpQuestionEventHandler(
                mcpToolService = mcpToolService,
                mcpInferenceStore = Stores.mcpInferenceStore,
                slackNotifier = slackNotifier,
            ),
        )
    }

    val evalService by lazy {
        EvalService(
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            machineLearningApiProvider = machineLearningApiProviders.first(),
        )
    }

    val ragRegressionInstanceService by lazy {
        RAGRegressionInstanceService(
            lockProvider = LockProvider(type = LockType.RAGRegressionTest),
            lockDuration = config.search.ragRegressionTestingInterval,
        )
    }

    val regressionTestService by lazy {
        RegressionTestService(
            evalService = evalService,
            enqueueService = searchEventEnqueueService,
            semanticSearchQueryService = semanticSearchQueryService,
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            similarityCalculator = similarityService,
            slackNotifier = slackNotifier,
            ragRegressionInstanceService = ragRegressionInstanceService,
        )
    }

    val regressionEvalHandler by lazy {
        RegressionEvalHandler(
            regressionTestService = regressionTestService,
        )
    }

    val regressionEvalSearchHandler by lazy {
        RegressionEvalSearchHandler(
            regressionTestService = regressionTestService,
        )
    }

    val regressionEvalRunEvalHandler by lazy {
        RegressionEvalRunEvalHandler(
            regressionTestService = regressionTestService,
        )
    }

    val searchEventHandler by lazy {
        SearchEventHandler(
            embedThreadEventHandler = EmbedThreadEventHandler(threadEmbeddingService),
            regressionEvalHandler = regressionEvalHandler,
            regressionEvalSearchHandler = regressionEvalSearchHandler,
            regressionEvalRunEvalHandler = regressionEvalRunEvalHandler,
        )
    }

    configureOpenSearch(
        lockProvider = openSearchLockProvider,
        config = config,
        openSearchIndexLoader = openSearchIndexLoader,
        openSearchConfig = openSearchConfig,
    )
    configurePinecone(
        lockProvider = pineconeLockProvider,
        pineconeConfig = pineconeConfig,
        pineconeIndexLoader = pineconeIndexLoader,
    )
    configureRouting(serviceLifecycle = serviceLifecycle)
    configureDefaultHeaders()
    configureForwardProxySupport()
    configureCors(config.cors)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureBackgroundJobs(
        jobs = buildList {
            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 500.milliseconds,
                        job = SearchEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.searchPriorityEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(searchPriorityEventHandler),
                            ),
                        ),
                    ).also {
                        ShutdownHookManager.registerShutdownHook(it)
                    },
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 500.milliseconds,
                        job = SearchEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.searchEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(searchEventHandler),
                            ),
                        ),
                    ),
                )
            }

            // Realtime consumer for high-priority events
            repeat(PRIORITY_CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 0.milliseconds,
                        job = SearchEventJob(
                            eventDequeueService = RealtimeEventDequeue(
                                waitTimeout = 5.seconds,
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.searchEventsQueueName,
                                    transacted = true,
                                    messageSelector = "JMSPriority = 9",
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(searchEventHandler),
                            ),
                        ),
                    ),
                )
            }

            add(
                PollingBackgroundJob(
                    interval = 10.minutes,
                    job = GlobalRegressionTestJob(
                        enabled = config.search.regressionTestingEnabled,
                        interval = config.search.regressionTestingInterval,
                        regressionTestService = regressionTestService,
                    ),
                ),
            )

            add(
                PollingBackgroundJob(
                    interval = 10.minutes,
                    job = RAGRegressionJob(
                        enabled = config.search.ragRegressionTestingEnabled,
                        regressionTestService = regressionTestService,
                    ),
                ),
            )
        },
    )
    configureJvmMetrics()
    configureCoroutineSchedulerMetrics()
}
