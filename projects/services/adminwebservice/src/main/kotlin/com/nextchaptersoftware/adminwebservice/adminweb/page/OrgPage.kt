package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminActivity.renderActivity
import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminInvitees.getInviteeAndIdentities
import com.nextchaptersoftware.adminwebservice.adminweb.AdminInvitees.renderInvitees
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalBoolean
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalInteger
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.orgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.DropDownOption
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItemInput
import com.nextchaptersoftware.adminwebservice.adminweb.component.ToggleItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.jumboAvatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.component.renderToggleList
import com.nextchaptersoftware.adminwebservice.adminweb.component.submittingDropDownList
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsCiPage.ciAnalyticsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagesPage.triagesMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.getOrgCollections
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.getOrgIngestions
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.getOrgInstallations
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.getUnblockedInstallations
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.renderInstallations
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.renderUnblockedInstallations
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamsPage.renderScmTeams
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminRoutingContextExtensions.getAdminIdentity
import com.nextchaptersoftware.analytics.AnalyticsAnswers
import com.nextchaptersoftware.analytics.AnalyticsMembers
import com.nextchaptersoftware.auditlog.AuditService
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.services.BillingService
import com.nextchaptersoftware.billing.utils.OrgBillingUsageService
import com.nextchaptersoftware.clientconfig.ClientConfigBundle
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AnswerMetricModel
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgBilling
import com.nextchaptersoftware.db.models.OrgBillingSeatApprovalMode
import com.nextchaptersoftware.db.models.OrgBillingSeatState
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgOnboardingState
import com.nextchaptersoftware.db.models.OrgProxy
import com.nextchaptersoftware.db.models.OrgSettings
import com.nextchaptersoftware.db.models.Plan
import com.nextchaptersoftware.db.models.PlanId
import com.nextchaptersoftware.db.models.PlanModel
import com.nextchaptersoftware.db.models.PlanTier
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.ReleaseChannel
import com.nextchaptersoftware.db.models.SamlIdpMetadataModel
import com.nextchaptersoftware.db.models.SlackSettings
import com.nextchaptersoftware.db.models.UnblockedRole
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.AnswerMetricsStore
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MLSettingsStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgBillingEmailStore
import com.nextchaptersoftware.db.stores.OrgBillingSeatStore
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.OrgProxyStore
import com.nextchaptersoftware.db.stores.OrgSettingsStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.PlanAndPrices
import com.nextchaptersoftware.db.stores.PlanStore
import com.nextchaptersoftware.db.stores.QuestionsFeedback
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.db.stores.Stores.slackSettingsStore
import com.nextchaptersoftware.db.stores.Stores.teamInviteeStore
import com.nextchaptersoftware.db.stores.UserEngagementStore
import com.nextchaptersoftware.embedding.service.stats.EmbeddingStatsFacade
import com.nextchaptersoftware.environment.dsl.adminUrls
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.maintenance.org.OrgDeletionService
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.orchestration.enablement.OnboardingEnablementService
import com.nextchaptersoftware.recommendation.SocialCommentNetwork
import com.nextchaptersoftware.userengagement.SlackQuestionAnalyticsService
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.ReportingUtils
import com.nextchaptersoftware.utils.StringDateTimeExtensions.toStartOfDayInstant
import com.nextchaptersoftware.utils.epoch
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.ContentDisposition
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.header
import io.ktor.server.response.respondFile
import io.ktor.server.response.respondRedirect
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import java.time.format.DateTimeFormatter
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Instant
import kotlin.time.toJavaInstant
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toJavaZoneId
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.InputType
import kotlinx.html.TBODY
import kotlinx.html.TextAreaWrap
import kotlinx.html.a
import kotlinx.html.button
import kotlinx.html.details
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.h4
import kotlinx.html.id
import kotlinx.html.img
import kotlinx.html.input
import kotlinx.html.label
import kotlinx.html.numberInput
import kotlinx.html.p
import kotlinx.html.span
import kotlinx.html.stream.appendHTML
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.textArea
import kotlinx.html.tr
import mu.KotlinLogging
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.countDistinct
import org.jetbrains.exposed.sql.intLiteral

private val LOGGER = KotlinLogging.logger {}

@Suppress("LargeClass", "LongMethod")
object OrgPage {

    suspend fun RoutingContext.renderOrgPage(
        page: AdminPage,
        clientConfigService: ClientConfigService = ClientConfigService(),
        identityStore: IdentityStore = Stores.identityStore,
        scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
        orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
        orgBillingSeatStore: OrgBillingSeatStore = Stores.orgBillingSeatStore,
        orgProxyStore: OrgProxyStore = Stores.orgProxyStore,
        repoStore: RepoStore = Stores.repoStore,
        enableTestBillingActions: Boolean,
    ) {
        val adminIdentity = call.getAdminIdentity()
        val path = call.request.path()
        val org = call.parameters.org()
        val createdBy = org.createdBy?.let { identityStore.findById(identityId = it) }
        val orgBilling = Stores.orgBillingStore.findByOrg(orgId = org.id) ?: error("Org billing not found")
        val seatsAllocated = orgBillingSeatStore.countSeatsFilled(orgBillingId = orgBilling.id)
        val planAndPrices = Stores.planStore.findWithPrices(planId = orgBilling.planId)
        val availablePlans = Stores.planStore.findNonSelfServePaidPlans()
        val updatedBy = orgBilling.updatingOrgMemberId?.let {
            orgMemberStore.findOrgMembersAndBestIdentity(orgId = org.id, orgMemberIds = listOf(it)).firstOrNull()?.identity
        }
        val dsacMode = orgSettingsStore.getDataSourceAccessControlMode(org.id)

        val installations = getOrgInstallations(org = org)
        val unblockedInstallations = getUnblockedInstallations(org = org)
        val ingestions = getOrgIngestions(installations = installations)
        val collections = getOrgCollections(installations = installations)
        val clientConfig = clientConfigService.getOrgConfig(orgId = org.id)

        val actions = getOrgActions(org = org, path = path)
        val billingActions = getOrgBillingActions(
            enableTestBillingActions = enableTestBillingActions,
            path = path,
            orgBilling = orgBilling,
            planAndPrices = planAndPrices,
        )
        val scmTeams = scmTeamStore.findByOrgId(orgId = org.id, includeDeleted = true)
        val scmTeamRepoIngestionStats = repoStore.getIngestionStatsForScmTeams(scmTeamIds = scmTeams.map { it.id })
        val orgSettings = orgSettingsStore.getOrDefault(orgId = org.id)
        val slackSettings = slackSettingsStore.getOrDefault(orgId = org.id)
        val orgProxy = orgProxyStore.getByOrgId(orgId = org.id)
        val invitees = teamInviteeStore.getTeamInvitees(orgId = org.id).let {
            getInviteeAndIdentities(invitees = it)
        }
        val scmTeamInstallations = installationStore.findByScmTeamIds(scmTeamIds = scmTeams.map { it.id })
        val scmTeamDsacModes = scmTeams.associate { scmTeam ->
            scmTeam.id to Pair(dsacMode, scmTeamInstallations.first { it.id == scmTeam.installationId })
        }

        val related = listOfNotNull(
            MenuItem(href = "$path/analytics", label = "Analytics", description = "Analytics for this org."),
            MenuItem(href = "$path/answers", label = "Answers", description = "All answers created for this org."),
            MenuItem(href = "$path/machineLearning", label = "Machine Learning", description = "Machine Learning for this org."),
            MenuItem(href = "$path/orgMembers", label = "Members", description = "Members of this org."),
            MenuItem(href = "$path/slackConnectConversion", label = "Slack Connect Conversion", description = "Slack Connect Conversion Metrics"),
            MenuItem(href = "$path/socialNetwork", label = "Social Network", description = "Social network connections in the org."),
            MenuItem(href = "$path/saml", label = "SSO", description = "Single Sign On."),
            MenuItem(href = "$path/productFeedback", label = "Product Feedback", description = "Product Feedback."),
            orgSettings.enableCodeReview.takeIf { it == true }?.let {
                MenuItem(href = "$path/reviews", label = "Reviews", description = "Code reviews for this org.")
            },
            ciAnalyticsMenuItem(path = path, description = "CI Analytics for this org."),
            triagesMenuItem(path = path, description = "CI Triages in this org."),
        )

        call.respondHtmlTemplate(ContentTemplate(page, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            relatedMenu { renderRelatedMenu(related) }
            actionMenu { renderActionMenu(items = actions + billingActions, sort = false) }
            content {
                div(classes = "row") {
                    div(classes = "col-12") {
                        renderOrgHeading(org = org, path = path, createdBy = createdBy)
                    }
                }

                div(classes = "mt-3 row") {
                    div(classes = "col-12") {
                        h3(classes = "mt-5") { +"Plan" }
                        renderPlan(path, orgBilling, planAndPrices, availablePlans, seatsAllocated, updatedBy)

                        h3(classes = "mt-5") { +"SCM Installations" }
                        renderScmTeams(
                            scmTeams = scmTeams,
                            codeStats = scmTeamRepoIngestionStats.codeStats,
                            prStats = scmTeamRepoIngestionStats.prStats,
                            dsacModes = scmTeamDsacModes,
                        )

                        h3(classes = "mt-5") { +"Installations" }
                        renderInstallations(
                            toPath = { "$path/installations" },
                            dsacModes = mapOf(org.id to dsacMode),
                            installations = installations,
                            ingestions = ingestions,
                            customSources = collections,
                        )

                        h3(classes = "mt-5") { +"Bot Installations" }
                        renderUnblockedInstallations(
                            path = "$path/installations",
                            installations = unblockedInstallations,
                        )

                        h3(classes = "mt-5") { +"Stats" }
                        div {
                            button(classes = "btn btn-outline-primary btn-sm mb-3", type = ButtonType.button) {
                                attributes["hx-get"] = "$path/getOrgStats"
                                attributes["hx-target"] = "closest div"
                                attributes["hx-swap"] = "outerHTML"
                                +"Show Stats"
                            }
                        }

                        h3(classes = "mt-5") { +"Features" }
                        details {
                            renderClientConfig(path = path, clientConfig = clientConfig)
                        }

                        h3(classes = "mt-5") { +"Org Settings" }
                        details {
                            renderOrgSettings(path = path, orgSettings = orgSettings)
                        }

                        h3(classes = "mt-5") { +"Org Onboarding" }
                        details {
                            renderOrgOnboardingStates(path = path, org = org)
                        }

                        h3(classes = "mt-5") { +"Slack Settings" }
                        details {
                            renderSlackSettings(
                                slackSettings = slackSettings,
                                path = path,
                            )
                        }

                        // ─── New Org Proxy Pane ────────────────────────
                        h3(classes = "mt-5") { +"Org Proxy" }
                        details {
                            if (orgProxy?.isEnabled == true) {
                                attributes["open"] = ""
                            }
                            renderOrgProxy(path = path, orgProxy = orgProxy)
                        }

                        if (invitees.isNotEmpty()) {
                            h3(classes = "mt-5") { +"Invites" }
                            renderInvitees(invitees)
                        }

                        h3(classes = "mt-5") { +"Activity" }
                        div {
                            attributes["hx-get"] = "$path/getOrgActivity"
                            attributes["hx-swap"] = "outerHTML"
                            attributes["hx-target"] = "this"
                            attributes["hx-trigger"] = "intersect"
                            img(alt = "Loading ...", src = "$WEB_ROOT/images/bars.svg")
                        }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderOrgHeading(org: Org, path: String, createdBy: Identity?) {
        table(classes = "table align-middle") {
            tbody(classes = "table-dark") {
                tr {
                    td {
                        style = "width: 300px; vertical-align: top;"
                        jumboAvatar(org)
                    }
                    td {
                        h1 { +org.displayName }
                        renderOrgProperties(org, path, createdBy)
                    }
                }
            }
        }
    }

    private fun FlowContent.renderOrgProperties(org: Org, path: String, createdBy: Identity?) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Org ID", org.id.value)
                property("Created", org.createdAt)
                createdBy?.also {
                    property("Created By") { profile(createdBy) }
                }
                property("Vectors") {
                    div {
                        attributes["hx-get"] = "$path/getVectorCounts"
                        attributes["hx-trigger"] = "intersect"
                        attributes["hx-swap"] = "outerHTML"
                        img(alt = "Loading ...", src = "$WEB_ROOT/images/bars.svg")
                    }
                }
                property("Release Channel") {
                    submittingDropDownList(
                        href = "$path/updateSubscribedReleaseChannel",
                        name = "subscribedReleaseChannel",
                        options = ReleaseChannel.entries.filterNot { it == ReleaseChannel.NotReleased }.map { DropDownOption(it.name) },
                        selectedValue = DropDownOption(org.subscribedReleaseChannel.name),
                    )
                }
            }
        }
    }

    private fun FlowContent.renderPlan(
        path: String,
        orgBilling: OrgBilling,
        planAndPrices: PlanAndPrices,
        availablePlans: List<Plan>,
        allocatedSeats: Int?,
        updatedBy: Identity?,
    ) {
        val isEnterprise = planAndPrices.plan.tier == PlanTier.Enterprise
        val seatApprovalMode = orgBilling.seatApprovalMode

        insert(PropertyListTemplate()) {
            propertyList {
                property("Plan") {
                    when (planAndPrices.isSelfServePaidPlan) { // Restrict editing of plan for self-serve paid tiers
                        true -> {
                            +planAndPrices.plan.name
                        }

                        else -> submittingDropDownList(
                            href = "$path/updatePlan",
                            name = "planId",
                            options = availablePlans.map { DropDownOption(value = it.id.toString(), description = it.name) },
                            selectedValue = DropDownOption(value = planAndPrices.plan.id.toString(), description = planAndPrices.plan.name),
                        )
                    }
                }
                orgBilling.rate?.name?.let { property("Rate", it) }
                orgBilling.seats?.let { property("Seats (current billing cycle)", it) }
                orgBilling.seatsNextBillingCycle?.let { property("Seats (next billing cycle)", it) }
                allocatedSeats?.let { property("Seats (allocated)", it) }
                property("Seat Approval Mode") { +seatApprovalMode.name }
                property("Seat Auto-Approval Cap") { +(orgBilling.maxSeats?.toString() ?: "-") }
                orgBilling.currentBillingCycleStart?.let { property("Current Billing Cycle Start", it) }
                orgBilling.currentBillingCycleEnd?.let { property("Current Billing Cycle End", it) }
                orgBilling.contactEmail?.let { property("Billing Email", it) }
                updatedBy?.let { property("Last Updated By") { profile(it) } }
                property("Stripe Customer ID") {
                    a(
                        href = "https://dashboard.stripe.com/customers/${orgBilling.stripeCustomerId}",
                        target = "_blank",
                    ) {
                        +"Stripe Customer Page"
                    }
                }
                property("Trial End (PST)", orgBilling.trialEnd)
                orgBilling.trialExtendedOn?.let { property("Trial Extended On (PST)", it) }
                if (isEnterprise) {
                    property("Quarterly Start Date (PST)", orgBilling.quarterlyStartDate)
                }
                renderActiveUserCountForm(path = path)
            }
        }
    }

    private fun TBODY.renderActiveUserCountForm(path: String) {
        property(
            label = "Active User Count",
            description = "Count of users asking a question. Dates are aligned to start of day in PDT timezone.",
        ) {
            form {
                id = "date-range-form"
                attributes["hx-get"] = "$path/getActiveUsersInRange"
                attributes["hx-target"] = "#user-count-result"
                attributes["hx-trigger"] = "change from:input"
                attributes["hx-swap"] = "transition:true"
                attributes["hx-indicator"] = "#loading-indicator"

                div(classes = "row") {
                    div(classes = "col-4 d-flex flex-column") {
                        label {
                            style = "font-variant: small-caps;"
                            htmlFor = "since"
                            +"Since"
                        }
                        input {
                            type = InputType.date
                            id = "since"
                            name = "since"
                            required = true
                        }
                    }

                    div(classes = "col-4 d-flex flex-column") {
                        label {
                            style = "font-variant: small-caps;"
                            htmlFor = "until"
                            +"Until"
                        }
                        input {
                            type = InputType.date
                            id = "until"
                            name = "until"
                            required = true
                        }
                    }

                    div(classes = "col-4 d-flex flex-column") {
                        label {
                            style = "font-variant: small-caps;"
                            +"User Count "
                            span(classes = "htmx-indicator") {
                                id = "loading-indicator"
                                +"(loading)"
                            }
                        }
                        div {
                            id = "user-count-result"
                            p(classes = "text-muted") { +"""Select date range.""" }
                        }
                    }
                }
            }
        }
    }

    private suspend fun getOrgActions(
        org: Org,
        path: String,
    ) = buildList {
        if (org.isDeleted) {
            add(
                MenuItem(
                    href = "$path/undeleteOrg",
                    label = "Undelete Org",
                    style = BootstrapStyle.Danger,
                    description = """
                    Recover a soft-deleted org.
                    """.trimIndent(),
                ),
            )
            add(
                MenuItem(
                    href = "$path/deleteOrgPermanently",
                    label = "Permanently Delete Org",
                    style = BootstrapStyle.Danger,
                    description = """
                    Permanently delete this org.
                    """.trimIndent(),
                ),
            )
        } else {
            getOrgImpersonationId(org.id)?.also { impersonatingIdentityId ->
                add(
                    MenuItem(
                        href = adminUrls.impersonate.start(impersonatingIdentityId).asString,
                        label = "Impersonate",
                        style = BootstrapStyle.Success,
                        description = "Impersonate someone in the org. Chosen at random, preferring admins.",
                    ),
                )
            }
            if (org.isPending) {
                add(
                    MenuItem(
                        href = "$path/enableOrg",
                        label = "Enable Org",
                        style = BootstrapStyle.Danger,
                        description = """
                        Allow members of this org to use Unblocked.
                        This will send a welcome email to all members of this org with an Unblocked account.
                        """.trimIndent(),
                    ),
                )
            }
            if (org.isProcessingComplete && GlobalConfig.INSTANCE.featureFlags.enableTeamDisablement) {
                add(
                    MenuItem(
                        href = "$path/disableOrg",
                        label = "Disable Org",
                        style = BootstrapStyle.Danger,
                        description = """
                        Disable this org. This will prevent members of this org from using Unblocked.
                        """.trimIndent(),
                    ),
                )
            }
            add(
                MenuItem(
                    href = "$path/deleteOrg",
                    label = "Delete Org",
                    style = BootstrapStyle.Danger,
                    description = """
                    Delete this org.
                    All integrations will be uninstalled.
                    All ML embeddings / vectors will be deleted.
                    Only the shell of the Org will remain.
                    """.trimIndent(),
                ),
            )
        }
        add(
            MenuItem(
                href = "$path/regenerateSocialNetwork",
                label = "Build Social Comment Network",
                description = """
                Generates a Social Comment Network from threads.
                """.trimIndent(),
            ),
        )
    }

    private suspend fun getOrgImpersonationId(orgId: OrgId): IdentityId? {
        val installationId = suspendedTransaction {
            SamlIdpMetadataModel
                .select(SamlIdpMetadataModel.installation)
                .whereAll(
                    SamlIdpMetadataModel.org eq orgId,
                    SamlIdpMetadataModel.isEnforced eq true,
                )
                .firstOrNull()
                ?.let { it[SamlIdpMetadataModel.installation].value }
        }

        return suspendedTransaction {
            MemberModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                ) {
                    AllOp(
                        InstallationStore.INSTALLATION_EXISTS,
                        installationId?.let { InstallationModel.id eq it },
                    )
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgMemberModel,
                    otherColumn = OrgMemberModel.id,
                    onColumn = MemberModel.orgMember,
                ) {
                    MemberStore.IS_PRIMARY_CURRENT_MEMBER
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                ) {
                    IdentityModel.person.isNotNull()
                }
                .select(IdentityModel.id)
                .where(OrgMemberModel.org eq orgId)
                .orderBy(
                    Coalesce(OrgMemberModel.unblockedRole, intLiteral(UnblockedRole.Member.dbOrdinal)) to SortOrder.DESC,
                    Coalesce(MemberModel.providerRole, intLiteral(ProviderRole.None.dbOrdinal)) to SortOrder.DESC,
                )
                .limit(1)
                .firstOrNull()
                ?.let { it[IdentityModel.id].value }
        }
    }

    private fun getOrgBillingActions(
        enableTestBillingActions: Boolean,
        path: String,
        orgBilling: OrgBilling,
        planAndPrices: PlanAndPrices?,
    ) = buildList {
        val isOnTrialPlan = planAndPrices?.plan?.isTrialPlan == true
        val isOnBusinessPlan = planAndPrices?.plan?.isBusiness == true
        val isOnLegacyPlan = planAndPrices?.plan?.isLegacy == true
        val isOnEnterprisePlan = planAndPrices?.plan?.isEnterprise == true

        fun dateInputDefaultValue(instant: Instant): String {
            return instant
                .toJavaInstant()
                .atZone(ReportingUtils.REPORTING_TIMEZONE.toJavaZoneId())
                .format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        }

        val now = Instant.nowWithMicrosecondPrecision()

        add(
            MenuItem(
                href = "$path/generateUserActivityReport",
                label = "Generate User Activity Report (PST)",
                description = "Download user activity (questions asked, CI triages) as a CSV.",
                inputs = listOf(
                    MenuItemInput.TextInput(
                        label = "Since (PST)",
                        name = "since",
                        inputType = InputType.dateTimeLocal,
                        defaultValue = dateInputDefaultValue(now.minus(90.days)),
                    ),
                    MenuItemInput.TextInput(
                        label = "Until (PST)",
                        name = "until",
                        inputType = InputType.dateTimeLocal,
                        defaultValue = dateInputDefaultValue(now),
                    ),
                ),
            ),
        )

        if (isOnEnterprisePlan) {
            add(
                MenuItem(
                    href = "$path/setQuarterlyStartDate",
                    label = "Set Quarterly Start Date (PST)",
                    description = """
                    Set a quarterly start date for this org. Default is trial end date. Billing quarters are calculated from this date.
                    """.trimIndent(),
                    inputs = listOf(
                        MenuItemInput.TextInput(
                            label = "Quarterly Start Date (PST)",
                            name = "quarterlyStartDate",
                            inputType = InputType.dateTimeLocal,
                            defaultValue = dateInputDefaultValue(orgBilling.quarterlyStartDate),
                        ),
                    ),
                ),
            )
        }

        if (isOnTrialPlan) {
            add(
                MenuItem(
                    href = "$path/updateTrialEndDate",
                    label = "Update Trial End Date (PST)",
                    description = """
                    Set a new trial end date for this org. Doing this will disable future trial emails from being sent.
                    """.trimIndent(),
                    inputs = listOf(
                        MenuItemInput.TextInput(
                            label = "Trial End (PST)",
                            name = "trialEndDate",
                            inputType = InputType.dateTimeLocal,
                            defaultValue = dateInputDefaultValue(orgBilling.trialEnd),
                        ),
                    ),
                ),
            )
        }

        if (isOnLegacyPlan) {
            add(
                MenuItem(
                    href = "$path/limitLegacyTeam",
                    label = "Limit Legacy Team",
                    description = """
                    Set a seat limit for this org on the Legacy plan. A value of 0 means unlimited seats.
                    """.trimIndent(),
                    inputs = listOf(
                        MenuItemInput.TextInput(
                            label = "Seat Cap",
                            name = "seatCap",
                            inputType = InputType.number,
                            defaultValue = orgBilling.maxSeats?.toString() ?: "0",
                        ),
                    ),
                ),
            )
        }

        if (orgBilling.trialExtendedOn != null) {
            add(
                MenuItem(
                    href = "$path/clearLastExtendedOn",
                    label = "Clear Trial Extended On",
                    description = "Sets OrgBillingModel.trialExtendedOn to null.",
                    style = BootstrapStyle.Danger,
                ),
            )
        }

        if (enableTestBillingActions) {
            add(
                MenuItem(
                    href = "$path/resetBilling",
                    label = "Reset Billing",
                    description = """
                    Resets the billing state and plan for this org. This will put the team on a trial plan and clear all billing data.
                    """.trimIndent(),
                    style = BootstrapStyle.Danger,
                ),
            )
        }

        if (isOnBusinessPlan) {
            add(
                MenuItem(
                    href = "$path/scheduleSeatUpgrade",
                    label = "Schedule Seat Upgrade",
                    description = "Schedule a seat upgrade for the next billing cycle if allocated seats > current seats",
                    style = BootstrapStyle.Info,
                ),
            )

            add(
                MenuItem(
                    href = "$path/scheduleSeatDowngrade",
                    label = "Schedule Seat Downgrade",
                    description = "Schedule a seat downgrade for the next billing cycle if allocated seats < current seats",
                    style = BootstrapStyle.Info,
                ),
            )

            add(
                MenuItem(
                    href = "$path/scheduleSubscriptionCancellation",
                    label = "Cancel Subscription",
                    description = "Schedule a cancellation for the next billing cycle",
                    style = BootstrapStyle.Danger,
                ),
            )
        }

        add(
            MenuItem(
                href = "$path/downgradeCapabilities",
                label = "Remove Integrations Not Allowed For Plan",
                description = """
                Removes integrations that are not allowed for the plan. This action is irreversible.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
        )

        add(
            MenuItem(
                href = "$path/sendBusinessPlanConfirmationEmail",
                label = "Send Business Plan Confirmation Email",
                description = "Confirmation email on successful business plan purchase",
                style = BootstrapStyle.Warning,
            ),
        )

        add(
            MenuItem(
                href = "$path/sendAssignLicensesEmail",
                label = "Send Assign Licenses Email",
                description = "Email sent to admins to remind them to assign licenses",
                style = BootstrapStyle.Warning,
            ),
        )
    }

    private fun FlowContent.renderOrgStats(
        membersCount: Int,
        questionsFeedback: QuestionsFeedback,
        slackUsersWithoutAccountsWhoHaveAskedQuestions: Int,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Users With Unblocked Accounts", membersCount)
                property("Slack Users Without Unblocked Accounts & Have Asked Questions", slackUsersWithoutAccountsWhoHaveAskedQuestions)
                property("Questions w/ positive feedback", questionsFeedback.positive)
                property("Questions w/ neutral feedback", questionsFeedback.neutral)
                property("Questions w/ negative feedback", questionsFeedback.negative)
                property("Questions w/ feedback", questionsFeedback.total - questionsFeedback.none)
                property("Questions w/o feedback", questionsFeedback.none)
                property("Questions, public", questionsFeedback.total)
                property("Questions, total", questionsFeedback.total)
            }
        }
    }

    private fun FlowContent.renderOrgSettings(
        path: String,
        orgSettings: OrgSettings,
    ) {
        renderToggleList(
            href = "$path/updateOrgSettings",
            tristate = false,
            items = listOf(
                ToggleItem(
                    label = "enableCodeReview",
                    name = "enableCodeReview",
                    checked = orgSettings.enableCodeReview,
                    description = "Enable code review",
                ),
                ToggleItem(
                    label = "codeReviewIncremental",
                    name = "codeReviewIncremental",
                    checked = orgSettings.codeReviewIncremental,
                    description = "Enable incremental code reviews",
                ),
                ToggleItem(
                    label = "enableEmailAlignment",
                    name = "enableEmailAlignment",
                    checked = orgSettings.enableEmailAlignment,
                    description = "Enable org member alignment through identity emails through verified providers",
                ),
                ToggleItem(
                    label = "enableTeamStatsUserFeedback",
                    name = "enableTeamStatsUserFeedback",
                    checked = orgSettings.enableTeamStatsUserFeedback,
                    description = "Always show user feedback in the Team management page in the dashboard.",
                ),
                ToggleItem(
                    label = "enablePresetBotResponse",
                    name = "enablePresetBotResponse",
                    checked = orgSettings.enablePresetBotResponse,
                    description = "Send preset bot responses to clients.",
                ),
                ToggleItem(
                    label = "enableProductFeedback",
                    name = "enableProductFeedback",
                    checked = orgSettings.enableProductFeedback,
                    description = "Enables collection of product feedback.",
                ),
                ToggleItem(
                    label = "disableCiTriageAutoSeatAssignment",
                    name = "disableCiTriageAutoSeatAssignment",
                    checked = orgSettings.disableCiTriageAutoSeatAssignment,
                    description = "Disable auto seat assignment for CI triage",
                ),
            ),
        )

        insert(PropertyListTemplate()) {
            propertyList {
                property(
                    label = "Product Feedback Question Threshold",
                    description = "The number of questions before a user is prompted for feedback",
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateOrgSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] = "return confirm('Are you sure you want to update the feedback question threshold?');"
                        numberInput(classes = "d-flex form-control", name = "productFeedbackQuestionThreshold") {
                            value = (
                                    orgSettings.productFeedbackQuestionThreshold
                                        ?: OrgSettingsStore.PRODUCT_FEEDBACK_QUESTION_COUNT_THRESHOLD
                                    ).toString()
                        }
                        button(classes = "btn btn-outline-info btn-sm mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
                property(
                    label = "Product Feedback Date Threshold",
                    description = "The number of days after the member's first question before a user is prompted for feedback",
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateOrgSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] = "return confirm('Are you sure you want to update the feedback date threshold?');"
                        numberInput(classes = "d-flex form-control", name = "productFeedbackDateThreshold") {
                            value = (
                                    orgSettings.productFeedbackDateThreshold
                                        ?: OrgSettingsStore.PRODUCT_FEEDBACK_DATE_THRESHOLD
                                    ).toString()
                        }
                        button(classes = "btn btn-outline-info btn-sm mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
                property(
                    label = "Web Ingestion Site Limit Override",
                    description = "Override the max number of sites allowed for web ingestion.",
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateOrgSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] = "return confirm('Are you sure you want to update the web ingestion limit?');"
                        numberInput(classes = "d-flex form-control", name = "webIngestionSiteLimitOverride") {
                            value = orgSettings.webIngestionSiteLimitOverride?.toString() ?: ""
                        }
                        button(classes = "btn btn-outline-info btn-sm mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
                property(
                    label = "Daily Quota Limit Override",
                    description = "Override the daily quota limit for Ask API calls.",
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateOrgSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] = "return confirm('Are you sure you want to update the daily quota limit?');"
                        numberInput(classes = "d-flex form-control", name = "dailyQuotaLimitOverride") {
                            value = orgSettings.dailyQuotaLimitOverride?.toString() ?: ""
                        }
                        button(classes = "btn btn-outline-info btn-sm mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderOrgOnboardingStates(
        path: String,
        org: Org,
    ) {
        renderToggleList(
            href = "$path/updateOrgOnboardingStates",
            tristate = false,
            sort = false,
            items = OrgOnboardingState.entries.map {
                ToggleItem(
                    label = it.name,
                    name = it.name,
                    checked = org.hasOnboardingStateSet(state = it),
                )
            },
        )
    }

    @Suppress("CyclomaticComplexMethod")
    suspend fun RoutingContext.updateOrgSettings() {
        call.parameters.orgId().also { orgId ->
            val params = call.receiveParameters()

            params.optionalInteger("webIngestionSiteLimitOverride")?.let { webIngestionSiteLimitOverride ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    webIngestionSiteLimitOverride = webIngestionSiteLimitOverride,
                )
            }

            params.optionalInteger("dailyQuotaLimitOverride")?.let { dailyQuotaLimitOverride ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    dailyQuotaLimitOverride = dailyQuotaLimitOverride,
                )
            }

            params.optionalBoolean("enableEmailAlignment")?.let { enableEmailAlignment ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    enableEmailAlignment = enableEmailAlignment,
                )
            }

            params.optionalBoolean("enableCodeReview")?.let { enableCodeReview ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    enableCodeReview = enableCodeReview,
                )
            }

            params.optionalBoolean("codeReviewIncremental")?.let { codeReviewIncremental ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    codeReviewIncremental = codeReviewIncremental,
                )
            }

            params.optionalBoolean("enableTeamStatsUserFeedback")?.let { enableTeamStatsUserFeedback ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    enableTeamStatsUserFeedback = enableTeamStatsUserFeedback,
                )
            }

            params.optionalBoolean("enablePresetBotResponse")?.let { enablePresetBotResponse ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    enablePresetBotResponse = enablePresetBotResponse,
                )
            }

            params.optionalBoolean("enableProductFeedback")?.let { enableProductFeedback ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    enableProductFeedback = enableProductFeedback,
                )
            }

            params.optionalBoolean("disableCiTriageAutoSeatAssignment")?.let { disableCiTriageAutoSeatAssignment ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    disableCiTriageAutoSeatAssignment = disableCiTriageAutoSeatAssignment,
                )
            }

            params.optionalInteger("productFeedbackQuestionThreshold")?.let { productFeedbackQuestionThreshold ->
                orgSettingsStore.upsert(
                    orgId = orgId,
                    productFeedbackQuestionThreshold = productFeedbackQuestionThreshold,
                )
            }
        }
    }

    suspend fun RoutingContext.enableOrg(
        onboardingEnablementService: OnboardingEnablementService,
    ) {
        call.parameters.orgId().also { orgId ->
            onboardingEnablementService.enableIfNecessary(orgId = orgId, emailTrigger = EmailTrigger.ADMIN_CONSOLE)
        }
    }

    suspend fun RoutingContext.disableOrg(
        onboardingEnablementService: OnboardingEnablementService,
    ) {
        call.parameters.orgId().also { orgId ->
            onboardingEnablementService.disableIfNecessary(orgId = orgId)
        }
    }

    suspend fun RoutingContext.deleteOrg(
        orgDeletionService: OrgDeletionService,
    ) {
        val orgId = call.parameters.orgId()
        val personId = call.getAdminIdentity().identity.person.required()
        orgDeletionService.softDeleteOrg(
            orgId = orgId,
            personId = personId,
            auditService = AuditService.forSystem(orgId = orgId),
        )
    }

    suspend fun RoutingContext.deleteOrgPermanently(
        orgDeletionService: OrgDeletionService,
    ) {
        orgDeletionService.hardDeleteOrg(orgId = call.parameters.orgId())
        call.respondRedirect(WEB_ROOT.ifEmpty { "/" })
    }

    suspend fun RoutingContext.undeleteOrg() {
        val orgId = call.parameters.orgId()
        orgStore.unmarkOrgForDeletion(orgId)
    }

    private fun FlowContent.renderOrgProxy(
        path: String,
        orgProxy: OrgProxy?,
    ) {
        // Enable toggle
        renderToggleList(
            href = "$path/updateOrgProxy",
            tristate = false,
            items = listOf(
                ToggleItem(
                    label = "isEnabled",
                    name = "isEnabled",
                    checked = orgProxy?.isEnabled,
                    description = "Enable outbound traffic through this proxy.",
                ),
            ),
        )

        // Proxy URL input
        insert(PropertyListTemplate()) {
            propertyList {
                property("Proxy URL") {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateOrgProxy",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] = "return confirm('Update proxy URL?');"
                        textArea(
                            classes = "form-control",
                            rows = "1",
                            cols = "60",
                            wrap = TextAreaWrap.soft,
                        ) {
                            name = "proxyUrl"
                            +(orgProxy?.proxyUrl?.asString.orEmpty())
                        }
                        button(classes = "btn btn-outline-info btn-sm mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
            }
        }

        if (orgProxy != null) {
            div {
                form(
                    classes = "d-flex form-group mt-2",
                    action = "$path/deleteOrgProxy",
                    method = FormMethod.post,
                ) {
                    attributes["onsubmit"] =
                        "return confirm('Are you sure you want to delete this proxy configuration?');"
                    button(classes = "btn btn-outline-danger btn-sm", type = ButtonType.submit) {
                        +"Delete Proxy"
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.updateOrgProxy(
        orgProxyStore: OrgProxyStore = Stores.orgProxyStore,
    ) {
        val orgId = call.parameters.orgId()
        val params = call.receiveParameters()

        val proxyUrl = params["proxyUrl"]?.trim()?.takeUnless { it.isEmpty() }?.asUrl
            ?: orgProxyStore.getProxyUrlByOrgId(orgId = orgId)
            ?: return call.respondText(
                status = HttpStatusCode.BadRequest,
                text = "proxyUrl must be provided",
            )

        val isEnabled = params.optionalBoolean("isEnabled")

        orgProxyStore.upsert(
            orgId = orgId,
            proxyUrl = proxyUrl,
            isEnabled = isEnabled,
        )
    }

    suspend fun RoutingContext.deleteOrgProxy(
        orgProxyStore: OrgProxyStore = Stores.orgProxyStore,
    ) {
        val orgId = call.parameters.orgId()
        orgProxyStore.deleteOrgProxyByOrgId(orgId = orgId)
    }

    suspend fun RoutingContext.updateSubscribedReleaseChannel(
        orgStore: OrgStore = Stores.orgStore,
    ) {
        call.parameters.orgId().also { orgId ->
            val bodyParams = call.receiveParameters()
            bodyParams["subscribedReleaseChannel"]?.let { channel ->
                runSuspendCatching {
                    ReleaseChannel.valueOf(channel)
                }.getOrNull()
            }?.also { releaseChannel ->
                orgStore.subscribeToReleaseChannel(orgId, releaseChannel)
            }
        }
    }

    private fun FlowContent.renderClientConfig(path: String, clientConfig: ClientConfigBundle) {
        div {
            renderToggleList(
                href = "$path/updateOrgCapability",
                tristate = true,
                items = ClientCapabilityType.allActiveConfigs().map { type ->
                    ToggleItem(
                        label = type.name,
                        name = type.name,
                        checked = clientConfig.capabilities[type]?.value,
                        description = type.description,
                    )
                },
            )
        }
    }

    suspend fun RoutingContext.updateOrgCapability(
        clientConfigService: ClientConfigService,
    ) {
        val orgId = call.parameters.orgId()
        val bodyParams = call.receiveParameters()
        val key = bodyParams
            .names()
            .map { ClientCapabilityType.valueOf(it) }
            .single()

        when (val value = bodyParams[key.name]?.toBooleanStrictOrNull()) {
            null -> clientConfigService.deleteOrgCapability(
                orgId = orgId,
                key = key,
            )

            else -> clientConfigService.setOrgCapability(
                orgId = orgId,
                key = key,
                value = value,

                )
        }
    }

    @Suppress("LongMethod")
    private fun FlowContent.renderSlackSettings(
        slackSettings: SlackSettings,
        path: String,
    ) {
        h4(classes = "mt-5") { +"Slack Settings" }
        div {
            renderToggleList(
                href = "$path/updateSlackSettings",
                tristate = false,
                items = listOf(
                    ToggleItem(
                        label = "slackUserAcceptanceTesting",
                        name = "slackUserAcceptanceTesting",
                        checked = slackSettings.slackUserAcceptanceTesting,
                        description = """
                            Enable slack user acceptance testing mode which allows testing against a different Slack app for acceptance testing.
                            This is to allow Slack Application reviewers to test additional scopes/events.
                            This is NOT the same as the client capability [ClientCapabilityType.SlackUserAcceptanceTesting] which simply controls UI components.
                            The Client Capability can sometimes be enabled while this is disabled as in the case of Slack penetration testing.
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "enableFeedbackButtonsForDisconnected",
                        name = "enableFeedbackButtonsForDisconnected",
                        checked = slackSettings.enableFeedbackButtonsForDisconnected,
                        description = """
                            Enable feedback buttons for at-mentions and auto-answers for disconnected users
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "enableProgressMessages",
                        name = "enableProgressMessages",
                        checked = slackSettings.enableProgressMessages,
                        description = """
                            Enable progress messages for slack while waiting for Q&A response
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "wideSlackThreadIngestion",
                        name = "wideSlackThreadIngestion",
                        checked = slackSettings.wideSlackThreadIngestion,
                        description = """
                            Enable wide slack thread ingestion override for legacy orgs
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "disableAutoResponseEvalFilters",
                        name = "disableAutoResponseEvalFilters",
                        checked = slackSettings.disableAutoResponseEvalFilters,
                        description = """
                            Disable auto-response eval filters
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "disableForcedLogin",
                        name = "disableForcedLogin",
                        checked = slackSettings.disableForcedLogin,
                        description = """
                            Disable forced login for slack.
                        """.trimIndent(),
                    ),
                    ToggleItem(
                        label = "disableSharedChannelsQA",
                        name = "disableSharedChannelsQA",
                        checked = slackSettings.disableSharedChannelsQA,
                        description = """
                            Disable all forms of Unblocked Q&A from shared channels.
                        """.trimIndent(),
                    ),
                ),
            )
        }

        h4(classes = "mt-5") { +"Slack QA Validation Settings" }
        insert(PropertyListTemplate()) {
            propertyList {
                property(
                    label = "Slack QA Validation Channel",
                    description = """
                        Comma-separated list of Slack channels to direct all Question & Answers to so that an external team can validate answers.
                        These channels should be shared and created by Unblocked.
                    """.trimIndent(),
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateSlackSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] =
                            "return confirm('Are you sure you want to update the slack QA validation channel?');"
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "slackQAValidationChannel"
                            +(slackSettings.slackQAValidationChannel ?: "")
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
            }
        }

        h4(classes = "mt-5") { +"Slack Auto-Response Shadow Approval Channel" }
        insert(PropertyListTemplate()) {
            propertyList {
                property(
                    label = "Slack Auto-Response Shadow Approval Channel",
                    description = """
                        External Slack ID of the channel to send auto-response approvals to.
                    """.trimIndent(),
                ) {
                    form(
                        classes = "d-flex form-group",
                        action = "$path/updateSlackSettings",
                        method = FormMethod.post,
                    ) {
                        attributes["onsubmit"] =
                            "return confirm('Are you sure you want to update the auto-answer shadow approval channel?');"
                        textArea(classes = "d-flex form-control", rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
                            name = "shadowApprovalChannelId"
                            +(slackSettings.shadowApprovalChannelId ?: "")
                        }
                        button(classes = "btn btn-primary my-0 mx-1", type = ButtonType.submit) {
                            +"Update"
                        }
                    }
                }
            }
        }

        h4(classes = "mt-5") { +"SlackBot Settings" }
        div {
            renderToggleList(
                href = "$path/updateSlackSettings",
                tristate = false,
                items = listOf(
                    ToggleItem(
                        label = "slackBotDisclaimer",
                        name = "slackBotDisclaimer",
                        checked = slackSettings.slackBotDisclaimer,
                        description = "Toggle slack bot disclaimer",
                    ),
                    ToggleItem(
                        label = "slackBotSuggestedQuestions",
                        name = "slackBotSuggestedQuestions",
                        checked = slackSettings.slackBotSuggestedQuestions,
                        description = "Toggle slack bot suggested questions",
                    ),
                ),
            )
        }
    }

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    suspend fun RoutingContext.updateSlackSettings() {
        val orgId = call.parameters.orgId()
        val params = call.receiveParameters()

        params.optionalBoolean("disableSharedChannelsQA")?.also { disableSharedChannelsQA ->
            slackSettingsStore.upsert(
                orgId = orgId,
                disableSharedChannelsQA = disableSharedChannelsQA,
            )
        }

        params.optionalBoolean("slackUserAcceptanceTesting")?.also { slackUserAcceptanceTesting ->
            slackSettingsStore.upsert(
                orgId = orgId,
                slackUserAcceptanceTesting = slackUserAcceptanceTesting,
            )
        }

        params.optionalBoolean("slackBotDisclaimer")?.also { slackBotDisclaimer ->
            slackSettingsStore.upsert(
                orgId = orgId,
                slackBotDisclaimer = slackBotDisclaimer,
            )
        }

        params.optionalBoolean("slackBotSuggestedQuestions")?.also { slackBotSuggestedQuestions ->
            slackSettingsStore.upsert(
                orgId = orgId,
                slackBotSuggestedQuestions = slackBotSuggestedQuestions,
            )
        }

        params["slackQAValidationChannel"]?.also { slackQAValidationChannel ->
            slackSettingsStore.upsert(
                orgId = orgId,
                slackQAValidationChannel = slackQAValidationChannel,
            )
        }

        params["shadowApprovalChannelId"]?.also { shadowApprovalChannelId ->
            slackSettingsStore.upsert(
                orgId = orgId,
                shadowApprovalChannelId = shadowApprovalChannelId,
            )
        }

        params.optionalBoolean("wideSlackThreadIngestion")?.also { wideSlackThreadIngestion ->
            slackSettingsStore.upsert(
                orgId = orgId,
                wideSlackThreadIngestion = wideSlackThreadIngestion,
            )
        }

        params.optionalBoolean("disableAutoResponseEvalFilters")?.also { disableAutoResponseEvalFilters ->
            slackSettingsStore.upsert(
                orgId = orgId,
                disableAutoResponseEvalFilters = disableAutoResponseEvalFilters,
            )
        }

        params.optionalBoolean("enableFeedbackButtonsForDisconnected")?.also { enableFeedbackButtonsForDisconnected ->
            slackSettingsStore.upsert(
                orgId = orgId,
                enableFeedbackButtonsForDisconnected = enableFeedbackButtonsForDisconnected,
            )
        }

        params.optionalBoolean("enableProgressMessages")?.also { enableProgressMessages ->
            slackSettingsStore.upsert(
                orgId = orgId,
                enableProgressMessages = enableProgressMessages,
            )
        }

        params.optionalBoolean("disableForcedLogin")?.also { disableForcedLogin ->
            slackSettingsStore.upsert(
                orgId = orgId,
                disableForcedLogin = disableForcedLogin,
            )
        }
    }

    suspend fun RoutingContext.generateUserActivityReport(
        orgBillingUsageService: OrgBillingUsageService = OrgBillingUsageService(),
    ) {
        val orgId = call.parameters.orgId()

        val receivedParameters = call.receiveParameters()
        val since = receivedParameters["since"].required().let {
            LocalDateTime.parse(it).toInstant(ReportingUtils.REPORTING_TIMEZONE)
        }
        val until = receivedParameters["until"].required().let {
            LocalDateTime.parse(it).toInstant(ReportingUtils.REPORTING_TIMEZONE)
        }

        LOGGER.debugAsync("orgId" to orgId, "since" to since, "until" to until) {
            "Generating user activity report"
        }

        val data = orgBillingUsageService.generateUsageCSVInternal(orgId = orgId, start = since, end = until)

        call.response.header(
            HttpHeaders.ContentDisposition,
            ContentDisposition.Attachment.withParameter(ContentDisposition.Parameters.FileName, "user_activity.csv")
                .toString(),
        )
        val tempFile = kotlin.io.path.createTempFile("user_activity", ".csv").toFile()
        tempFile.writeText(data)
        call.respondFile(tempFile)
    }

    suspend fun RoutingContext.updateTrialEndDate(
        orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
        orgBillingEmailStore: OrgBillingEmailStore = Stores.orgBillingEmailStore,
    ) {
        val orgId = call.parameters.orgId()
        val receivedParameters = call.receiveParameters()
        val trialEndDate = receivedParameters["trialEndDate"].required().let {
            LocalDateTime.parse(it).toInstant(ReportingUtils.REPORTING_TIMEZONE)
        }

        orgBillingStore.setTrialEnd(orgId = orgId, trialEnd = trialEndDate)
        orgBillingStore.setTrialExtendedOn(orgId = orgId, trialExtendedOn = Instant.nowWithMicrosecondPrecision())
        orgBillingStore.findByOrg(orgId = orgId)?.let { orgBillingEmailStore.cancelAllPending(orgBillingId = it.id) }
        AuditService.forSystem(orgId = orgId).trialExtended()
    }

    suspend fun RoutingContext.setQuarterlyStartDate(
        orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    ) {
        val orgId = call.parameters.orgId()
        val receivedParameters = call.receiveParameters()
        val quarterlyStartDate = receivedParameters["quarterlyStartDate"].required().let {
            LocalDateTime.parse(it).toInstant(ReportingUtils.REPORTING_TIMEZONE)
        }

        orgBillingStore.setQuarterlyStartDate(orgId = orgId, quarterlyStartDate = quarterlyStartDate)
    }

    suspend fun RoutingContext.clearLastExtendedOn(
        orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    ) {
        val orgId = call.parameters.orgId()

        orgBillingStore.setTrialExtendedOn(orgId = orgId, trialExtendedOn = null)
    }

    suspend fun RoutingContext.resetBilling(
        billingService: BillingService,
    ) {
        call.parameters.orgId().also { orgId ->
            billingService.reset(orgId = orgId)
        }
    }

    fun RoutingContext.downgradeCapabilities(
        billingEventEnqueueService: BillingEventEnqueueService,
    ) {
        call.parameters.orgId().also { orgId ->
            billingEventEnqueueService.enqueueCapabilityDowngradeEvent(orgId = orgId)
        }
    }

    suspend fun RoutingContext.updatePlan(
        billingService: BillingService,
    ) {
        call.parameters.orgId().also { orgId ->
            val bodyParams = call.receiveParameters()
            bodyParams.optionalId("planId", ::PlanId)?.let { planId ->
                billingService.forceSetOrgToPlan(orgId = orgId, planId = planId)
            }
        }
    }

    suspend fun RoutingContext.limitLegacyTeam(
        billingService: BillingService,
        orgBillingSeatStore: OrgBillingSeatStore = Stores.orgBillingSeatStore,
        answerMetricsStore: AnswerMetricsStore = Stores.answerMetricsStore,
    ) {
        val orgId = call.parameters.orgId()
        val receivedParameters = call.receiveParameters()
        val seatCap = receivedParameters["seatCap"]?.toIntOrNull().required().also {
            require(it >= 0) { "Seats must be greater than or equal to 0" }
        }

        val (orgBilling, planAndPrices) = billingService.findWithModels(orgId = orgId).required()
        require(planAndPrices.plan.isLegacy) { "Plan is not legacy" }

        val currentSeatApprovalMode = orgBilling.seatApprovalMode

        val newSeatApprovalMode = when (seatCap == 0) {
            true -> OrgBillingSeatApprovalMode.Unlimited
            else -> OrgBillingSeatApprovalMode.Limited
        }

        // Assign seats when limiting seats
        if (newSeatApprovalMode == OrgBillingSeatApprovalMode.Limited) {
            val existingSeats = when (currentSeatApprovalMode == OrgBillingSeatApprovalMode.Unlimited) {
                true -> {
                    // Reset seats if switching from unlimited to limited
                    orgBillingSeatStore.delete(orgBillingId = orgBilling.id) // delete all seats for org
                    emptyList()
                }

                else -> {
                    orgBillingSeatStore.list(orgId = orgId, states = listOf(OrgBillingSeatState.Assigned))
                }
            }

            val seatsRemaining = seatCap - existingSeats.size
            when (seatsRemaining < 0) {
                true -> {
                    // Remove excess seats
                    existingSeats.takeLast(-seatsRemaining).forEach { orgBillingSeatStore.removeSeat(orgMemberId = it.orgMemberId) }
                }

                else -> {
                    // Assign seats at random
                    val orgMembersWithAnswers = answerMetricsStore.countAnswersByMemberForOrgForPeriod(
                        orgId = orgId,
                        since = orgBilling.createdAt,
                        until = Instant.nowWithMicrosecondPrecision(),
                    )

                    orgMembersWithAnswers.keys
                        .filterNot { it in existingSeats.map { it.orgMemberId } }
                        .shuffled()
                        .take(seatsRemaining)
                        .forEach { orgBillingSeatStore.upsert(orgBillingId = orgBilling.id, orgMemberId = it, state = OrgBillingSeatState.Assigned) }
                }
            }
        }

        // Finally, update
        billingService.setSeatApprovalMode(orgId = orgId, seatApprovalMode = newSeatApprovalMode, maxSeats = seatCap)
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun RoutingContext.regenerateSocialNetwork() {
        call.parameters.orgId().also { orgId ->
            GlobalScope.launch {
                withTimeout(3.minutes) {
                    SocialCommentNetwork().regenerateSocialNetwork(job = this, orgId = orgId)
                }
            }
        }
    }

    suspend fun RoutingContext.updateOrgOnboardingStates(
        orgStore: OrgStore = Stores.orgStore,
    ) {
        call.parameters.orgId().also { orgId ->
            val bodyParams = call.receiveParameters()

            OrgOnboardingState.entries.firstNotNullOfOrNull { state ->
                bodyParams[state.name]?.let { state to it.toBooleanStrict() }
            }?.also {
                orgStore.setOnboardingFlags(id = orgId, flags = mapOf(it))
            }
        }
    }

    fun RoutingContext.sendBusinessPlanConfirmationEmail(
        notificationEventEnqueueService: NotificationEventEnqueueService,
    ) {
        notificationEventEnqueueService.enqueueBusinessPlanConfirmation(orgId = call.parameters.orgId())
    }

    fun RoutingContext.sendAssignLicensesEmail(
        notificationEventEnqueueService: NotificationEventEnqueueService,
    ) {
        notificationEventEnqueueService.enqueueAssignLicenses(orgId = call.parameters.orgId())
    }

    fun RoutingContext.scheduleSeatUpgrade(
        billingEventEnqueueService: BillingEventEnqueueService,
    ) {
        val orgId = call.parameters.orgId()
        billingEventEnqueueService.enqueueSubscriptionSeatUpdateEvent(orgId = orgId)
    }

    suspend fun RoutingContext.scheduleSeatDowngrade(
        billingService: BillingService,
        billingEventEnqueueService: BillingEventEnqueueService,
        orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    ) {
        val orgId = call.parameters.orgId()
        val orgBilling = orgBillingStore.findByOrg(orgId) ?: error("Org billing not found")
        billingService.maybeScheduleSeatDowngrade(orgBilling = orgBilling)
        billingEventEnqueueService.enqueueGenerateNextInvoiceEvent(orgId = orgId)
    }

    suspend fun RoutingContext.scheduleSubscriptionCancellation(
        billingService: BillingService,
        orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
        planStore: PlanStore = Stores.planStore,
    ) {
        val starterPlanId = suspendedTransaction {
            PlanModel
                .select(PlanModel.id)
                .where { PlanModel.tier eq PlanTier.Starter }
                .limit(1)
                .map { it[PlanModel.id].value }
                .firstOrNull()
        } ?: error("Starter plan not found")

        billingService.cancelSubscription(
            orgBilling = orgBillingStore.findByOrg(orgId = call.parameters.orgId()) ?: error("Org billing not found"),
            nextPlanAndPrices = planStore.findWithPrices(planId = starterPlanId),
        )
    }

    suspend fun RoutingContext.getVectorCounts(
        embeddingStatsFacade: EmbeddingStatsFacade,
        mlSettingsStore: MLSettingsStore = Stores.mlSettingsStore,
    ) {
        runSuspendCatching {
            val orgId = call.parameters.orgId()
            val embeddingPlatform = mlSettingsStore.getReadEmbeddingPlatform(orgId = orgId)
            embeddingStatsFacade.getNamespaceVectorCount(orgId = orgId, embeddingPlatform = embeddingPlatform).toString()
        }.onSuccess {
            call.respondText(it, ContentType.Text.Plain)
        }.onFailure {
            val htmlFragment = buildString {
                appendHTML().div(classes = "text-danger") {
                    +"Failed to get vector counts: ${it.message}"
                }
            }
            call.respondText(htmlFragment, ContentType.Text.Html)
        }
    }

    suspend fun RoutingContext.getActiveUsersInRange() {
        val orgId = call.parameters.orgId()
        val since = call.queryParameters["since"].required().toStartOfDayInstant()
        val until = call.queryParameters["until"].required().toStartOfDayInstant()

        runSuspendCatching {
            suspendedTransaction {
                AnswerMetricModel
                    .select(AnswerMetricModel.humanOrgMember.countDistinct())
                    .whereAll(
                        AnswerMetricModel.org eq orgId,
                        AnswerMetricModel.isSlackAutoAnswer eq false,
                        AnswerMetricModel.isSuggestion eq false,
                        AnswerMetricModel.humanPerson.isNotNull(),
                        AnswerMetricModel.createdAt greaterEq since,
                        AnswerMetricModel.createdAt less until,
                    )
                    .first()
                    .let { it[AnswerMetricModel.humanOrgMember.countDistinct()] }
            }
        }.onSuccess {
            call.respondText(it.toString(), ContentType.Text.Plain)
        }.onFailure {
            val htmlFragment = buildString {
                appendHTML().div(classes = "text-danger") {
                    +"Error: ${it.message}"
                }
            }
            call.respondText(htmlFragment, ContentType.Text.Html)
        }
    }

    suspend fun RoutingContext.getOrgStats(
        slackQuestionAnalyticsService: SlackQuestionAnalyticsService = SlackQuestionAnalyticsService(),
        analyticsMembers: AnalyticsMembers = AnalyticsMembers(),
        analyticsAnswers: AnalyticsAnswers = AnalyticsAnswers(),
    ) {
        runSuspendCatching {
            val orgId = call.parameters.orgId()
            val questionsFeedback = analyticsAnswers.answerCountByFeedback(orgId = orgId)
            val membersCount = analyticsMembers.memberJoinDates(orgId = orgId).size
            val slackUsersWithoutAccountsWhoHaveAskedQuestions = slackQuestionAnalyticsService.getActiveMembersInRange(
                orgId = orgId,
                period = Instant.epoch..<Instant.nowWithMicrosecondPrecision(),
            ).size

            buildString {
                appendHTML().div {
                    renderOrgStats(
                        membersCount = membersCount,
                        questionsFeedback = questionsFeedback,
                        slackUsersWithoutAccountsWhoHaveAskedQuestions = slackUsersWithoutAccountsWhoHaveAskedQuestions,
                    )
                }
            }
        }.onSuccess { htmlFragment ->
            call.respondText(htmlFragment, ContentType.Text.Html)
        }.onFailure {
            val htmlFragment = buildString {
                appendHTML().div(classes = "text-danger") {
                    +"Failed to load org stats: ${it.message}"
                }
            }
            call.respondText(htmlFragment, ContentType.Text.Html)
        }
    }

    suspend fun RoutingContext.getOrgActivity(
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
    ) {
        runSuspendCatching {
            val orgId = call.parameters.orgId()
            val activity = userEngagementStore.getAggregateEventsForOrg(orgId = orgId)

            buildString {
                appendHTML().div {
                    renderActivity(activity = activity)
                }
            }
        }.onSuccess { htmlFragment ->
            call.respondText(htmlFragment, ContentType.Text.Html)
        }.onFailure {
            val htmlFragment = buildString {
                appendHTML().div(classes = "text-danger") {
                    +"Failed to load org activity: ${it.message}"
                }
            }
            call.respondText(htmlFragment, ContentType.Text.Html)
        }
    }
}
