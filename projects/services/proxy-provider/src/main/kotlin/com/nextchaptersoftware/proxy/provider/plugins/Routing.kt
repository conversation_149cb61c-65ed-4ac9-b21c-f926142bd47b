package com.nextchaptersoftware.proxy.provider.plugins

import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.api.auth.services.identity.ProviderIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.identity.ScmIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.integration.extension.services.DataSourcePresetsConfigurationDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.PlanConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.asana.AsanaConfigurationDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.coda.CodaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.google.GoogleDriveConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.jira.JiraConfigurationDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.microsoft.MicrosoftConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsService
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.asana.auth.oauth.AsanaProviderAuthApi
import com.nextchaptersoftware.asset.ScmAssetService
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApi
import com.nextchaptersoftware.ci.CIProjectApiFactory
import com.nextchaptersoftware.ci.CIServerApiFactory
import com.nextchaptersoftware.ci.CIUserApiFactory
import com.nextchaptersoftware.ci.services.CIProjectControlService
import com.nextchaptersoftware.ci.services.CITokenValidationService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.confluence.auth.oauth.ConfluenceProviderAuthApi
import com.nextchaptersoftware.confluence.ingestion.services.ConfluenceMemberModelService
import com.nextchaptersoftware.google.auth.oauth.GoogleProviderAuthApi
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.auth.oauth.JiraProviderAuthApi
import com.nextchaptersoftware.jira.ingestion.services.JiraMemberModelService
import com.nextchaptersoftware.ktor.serialization.KtorSerialization.installSerializer
import com.nextchaptersoftware.linear.auth.oauth.LinearProviderAuthApi
import com.nextchaptersoftware.microsoft.graph.oauth.MicrosoftGraphProviderApi
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.api.NotionOauthTokenProvider
import com.nextchaptersoftware.notion.auth.oauth.NotionProviderAuthApi
import com.nextchaptersoftware.notion.auth.services.NotionAuthInstallService
import com.nextchaptersoftware.proxy.provider.api.HealthApiDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.CIProjectManagementDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.CIServerManagementDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.CITokenManagementDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.ScmEnterpriseDelegateImpl
import com.nextchaptersoftware.proxy.provider.rpc.ForApiServiceRpcImpl
import com.nextchaptersoftware.proxy.provider.rpc.ForAssetServiceRpcImpl
import com.nextchaptersoftware.proxy.provider.rpc.ForAuthServiceRpcImpl
import com.nextchaptersoftware.proxy.provider.rpc.ForSearchServiceRpcImpl
import com.nextchaptersoftware.proxy.provider.rpc.handlers.AsanaHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.AwsStsHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.CIHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.CodaHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ConfluenceHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.DataSourcePresetsHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.GoogleDriveHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.JiraHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.MicrosoftHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.OAuthHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.PlanHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ProviderIdentityHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.RepoComputeServiceHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmAppHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmEnterpriseHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmIdentityHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmInstallationHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmRepoApiHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmUserApiHandler
import com.nextchaptersoftware.proxy.provider.rpc.handlers.StackOverflowTeamsHandler
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.rpc.ktor.installRPC
import com.nextchaptersoftware.rpc.proxy.provider.ForApiServiceRpc
import com.nextchaptersoftware.rpc.proxy.provider.ForAssetServiceRpc
import com.nextchaptersoftware.rpc.proxy.provider.ForAuthServiceRpc
import com.nextchaptersoftware.rpc.proxy.provider.ForSearchServiceRpc
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.delegates.ScmAppDelegate
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.gitlab.GitLabPipelinesClientProvider
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.slack.auth.oauth.SlackProviderAuthApi
import com.nextchaptersoftware.slack.auth.services.SlackAuthInstallService
import com.nextchaptersoftware.slack.auth.services.SlackUserAuthInstallService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.resources.Resources
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlinx.rpc.krpc.ktor.server.rpc

@Suppress("LongMethod")
fun Application.configureRouting(
    asanaConfigurationDelegate: AsanaConfigurationDelegateInterface,
    asanaProviderAuthApi: AsanaProviderAuthApi?,
    awsStsHandler: AwsStsHandler,
    codaConfigurationService: CodaConfigurationService,
    config: GlobalConfig = GlobalConfig.INSTANCE,
    confluenceAuthApi: AtlassianAuthApi,
    confluenceCloudApiProvider: ConfluenceCloudApiProvider,
    confluenceMemberModelService: ConfluenceMemberModelService,
    confluenceConfigurationService: ConfluenceConfigurationService,
    dataSourcePresetsConfigurationDelegate: DataSourcePresetsConfigurationDelegateInterface,
    googleDriveConfigurationService: GoogleDriveConfigurationService,
    googleProviderAuthApi: GoogleProviderAuthApi?,
    jiraApiProvider: JiraApiProvider,
    jiraAuthApi: AtlassianAuthApi,
    jiraConfigurationDelegate: JiraConfigurationDelegateInterface,
    jiraMemberModelService: JiraMemberModelService,
    linearProviderAuthApi: LinearProviderAuthApi?,
    loginUrlService: LoginUrlService,
    microsoftConfigurationService: MicrosoftConfigurationService,
    microsoftGraphProviderAuthApi: MicrosoftGraphProviderApi?,
    notionAuthInstallService: NotionAuthInstallService,
    notionTokenProvider: NotionOauthTokenProvider,
    planConfigurationService: PlanConfigurationService,
    providerIdentityAuthExchangeService: ProviderIdentityAuthExchangeService,
    repoComputeService: LocalRepoComputeService,
    scmAppApiFactory: ScmAppApiFactory,
    scmAppDelegate: ScmAppDelegate,
    scmAssetService: ScmAssetService,
    scmAuthApiFactory: ScmAuthApiFactory,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    scmIdentityAuthExchangeService: ScmIdentityAuthExchangeService,
    scmInstallService: ScmInstallationService,
    scmNoAuthApiFactory: ScmNoAuthApiFactory,
    scmRepoApiFactory: ScmRepoApiFactory,
    scmTeamApiFactory: ScmTeamApiFactory,
    scmUserApiFactory: ScmUserApiFactory,
    serviceLifecycle: ServiceLifecycle,
    slackAuthInstallService: SlackAuthInstallService,
    slackUserAuthInstallService: SlackUserAuthInstallService,
    stackOverflowTeamsService: StackOverflowTeamsService,
    userSecretServiceResolver: UserSecretServiceResolver,
) {
    installRPC()
    install(Resources) {
        installSerializer()
    }

    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(
            serviceHealthApi = ServiceHealthApi(
                serviceLifecycle = serviceLifecycle,
            ),
        )
    }

    val ciUserApiFactory by lazy {
        CIUserApiFactory()
    }

    val ciTokenValidationService by lazy {
        CITokenValidationService(
            ciUserApiFactory = ciUserApiFactory,
        )
    }

    val bitbucketPipelinesClientProvider by lazy {
        BitbucketPipelinesClientProvider(
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val gitHubActionsClientProvider by lazy {
        GitHubActionsClientProvider(
            scmAppApiFactory = scmAppApiFactory,
        )
    }

    val gitLabPipelinesClientProvider by lazy {
        GitLabPipelinesClientProvider(
            gitLabCloudConfig = scmConfig.gitlabCloud,
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val ciProjectApiFactory by lazy {
        CIProjectApiFactory(
            bitbucketPipelinesClientProvider = bitbucketPipelinesClientProvider,
            gitHubActionsClientProvider = gitHubActionsClientProvider,
            gitLabPipelinesClientProvider = gitLabPipelinesClientProvider,
        )
    }

    val ciProjectControlService by lazy {
        CIProjectControlService(
            ciProjectApiFactory = ciProjectApiFactory,
            ciUserApiFactory = ciUserApiFactory,
        )
    }

    val ciTokenManagementDelegateImpl by lazy {
        CITokenManagementDelegateImpl(
            ciTokenValidationService = ciTokenValidationService,
        )
    }
    val ciProjectManagementDelegateImpl by lazy {
        CIProjectManagementDelegateImpl(
            ciProjectControlService = ciProjectControlService,
        )
    }

    val ciServerManagementDelegateImpl by lazy {
        CIServerManagementDelegateImpl(
            ciServerApiFactory = CIServerApiFactory(),
        )
    }

    val ciHandler by lazy {
        CIHandler(
            ciProjectManagementDelegate = ciProjectManagementDelegateImpl,
            ciTokenManagementDelegate = ciTokenManagementDelegateImpl,
            ciServerManagementDelegate = ciServerManagementDelegateImpl,
        )
    }

    val asanaHandler = AsanaHandler(
        asanaConfigurationDelegate = asanaConfigurationDelegate,
    )

    val codaHandler by lazy {
        CodaHandler(
            codaConfigurationService = codaConfigurationService,
        )
    }

    val confluenceHandler by lazy {
        ConfluenceHandler(
            confluenceConfigurationService = confluenceConfigurationService,
        )
    }

    val confluenceProviderAuthApi by lazy {
        ConfluenceProviderAuthApi(
            confluenceAuthApi = confluenceAuthApi,
            confluenceCloudApiProvider = confluenceCloudApiProvider,
            confluenceMemberModelService = confluenceMemberModelService,
        )
    }

    val dataSourcePresetsHandler by lazy {
        DataSourcePresetsHandler(
            dataSourcePresetsConfigurationDelegate = dataSourcePresetsConfigurationDelegate,
        )
    }

    val googleDriveHandler by lazy {
        GoogleDriveHandler(
            googleDriveConfigurationService = googleDriveConfigurationService,
        )
    }

    val jiraProviderAuthApi by lazy {
        JiraProviderAuthApi(
            jiraAuthApi = jiraAuthApi,
            jiraApiProvider = jiraApiProvider,
            jiraMemberModelService = jiraMemberModelService,
        )
    }

    val jiraHandler by lazy {
        JiraHandler(
            jiraConfigurationDelegate = jiraConfigurationDelegate,
        )
    }

    val microsoftHandler by lazy {
        MicrosoftHandler(
            microsoftConfigurationService = microsoftConfigurationService,
        )
    }

    val planHandler by lazy {
        PlanHandler(
            planConfigurationService = planConfigurationService,
        )
    }

    val notionApiProvider by lazy {
        NotionApiProvider(
            config = config.providers.notion ?: return@lazy null,
            oauthTokenProvider = notionTokenProvider,
        )
    }

    val notionProviderAuthApi by lazy {
        NotionProviderAuthApi(
            notionApiProvider = notionApiProvider ?: return@lazy null,
            notionAuthInstallService = notionAuthInstallService,
        )
    }

    val repoComputeServiceHandler by lazy {
        RepoComputeServiceHandler(
            repoComputeService = repoComputeService,
        )
    }

    val scmAppHandler by lazy {
        ScmAppHandler(
            scmAppDelegate = scmAppDelegate,
        )
    }
    val scmInstallHandler by lazy {
        ScmInstallationHandler(
            scmInstallService = scmInstallService,
            scmTeamApiFactory = scmTeamApiFactory,
        )
    }

    val scmRepoApiHandler by lazy {
        ScmRepoApiHandler(
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val scmUserApiHandler by lazy {
        ScmUserApiHandler(
            scmAppApiFactory = scmAppApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val stackOverflowTeamsHandler by lazy {
        StackOverflowTeamsHandler(
            stackOverflowTeamsService = stackOverflowTeamsService,
        )
    }

    val scmEnterpriseHandler by lazy {
        ScmEnterpriseHandler(
            scmEnterpriseDelegate = ScmEnterpriseDelegateImpl(
                loginUrlService = loginUrlService,
                scmNoAuthApiFactory = scmNoAuthApiFactory,
            ),
        )
    }

    val scmIdentityHandler by lazy {
        ScmIdentityHandler(
            scmIdentityAuthExchangeService = scmIdentityAuthExchangeService,
        )
    }

    val providerIdentityHandler by lazy {
        ProviderIdentityHandler(
            providerIdentityAuthExchangeService = providerIdentityAuthExchangeService,
        )
    }

    val slackProviderAuthApi by lazy {
        SlackProviderAuthApi(
            slackAuthInstallService = slackAuthInstallService,
            slackUserAuthInstallService = slackUserAuthInstallService,
        )
    }

    val oauthHandler by lazy {
        OAuthHandler(
            asanaProviderAuthApi = asanaProviderAuthApi,
            confluenceProviderAuthApi = confluenceProviderAuthApi,
            googleProviderAuthApi = googleProviderAuthApi,
            jiraProviderAuthApi = jiraProviderAuthApi,
            linearProviderAuthApi = linearProviderAuthApi,
            microsoftGraphProviderAuthApi = microsoftGraphProviderAuthApi,
            notionProviderAuthApi = notionProviderAuthApi,
            slackProviderAuthApi = slackProviderAuthApi,
        )
    }

    routing {
        rpc("/rpc") {
            registerService<ForApiServiceRpc> {
                ForApiServiceRpcImpl(
                    asanaHandler = asanaHandler,
                    ciHandler = ciHandler,
                    codaHandler = codaHandler,
                    dataSourcePresetsHandler = dataSourcePresetsHandler,
                    confluenceHandler = confluenceHandler,
                    googleDriveHandler = googleDriveHandler,
                    jiraHandler = jiraHandler,
                    microsoftHandler = microsoftHandler,
                    planHandler = planHandler,
                    repoComputeServiceHandler = repoComputeServiceHandler,
                    scmAppHandler = scmAppHandler,
                    scmInstallHandler = scmInstallHandler,
                    scmRepoApiHandler = scmRepoApiHandler,
                    scmUserApiHandler = scmUserApiHandler,
                    stackOverflowTeamsHandler = stackOverflowTeamsHandler,
                    call = call,
                )
            }
            registerService<ForAssetServiceRpc> {
                ForAssetServiceRpcImpl(
                    scmAssetService = scmAssetService,
                    call = call,
                )
            }
            registerService<ForAuthServiceRpc> {
                ForAuthServiceRpcImpl(
                    call = call,
                    oauthHandler = oauthHandler,
                    repoComputeServiceHandler = repoComputeServiceHandler,
                    scmEnterpriseHandler = scmEnterpriseHandler,
                    scmIdentityHandler = scmIdentityHandler,
                    providerIdentityHandler = providerIdentityHandler,
                )
            }
            registerService<ForSearchServiceRpc> {
                ForSearchServiceRpcImpl(
                    call = call,
                    awsStsHandler = awsStsHandler,
                )
            }
        }

        route("/api") {
            HealthApi(healthApiDelegateImpl)
        }

        // Path used for exposing health endpoint to Kubernetes
        route("/api/health/proxy-provider") {
            HealthApi(healthApiDelegateImpl)
        }
    }
}
