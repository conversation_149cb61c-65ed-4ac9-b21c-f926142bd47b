package com.nextchaptersoftware.proxy.provider

import com.nextchaptersoftware.access.RestrictedAccessServiceFactory
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.api.auth.services.identity.ProviderIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.identity.SSOIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.identity.ScmIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.identity.SlackIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.integration.extension.services.PlanConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.asana.NoopAsanaConfigurationDelegate
import com.nextchaptersoftware.api.integration.extension.services.coda.CodaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.google.GoogleDriveConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.microsoft.MicrosoftConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsService
import com.nextchaptersoftware.api.services.EmailService
import com.nextchaptersoftware.api.services.PersonEmailPreferencesService
import com.nextchaptersoftware.api.services.PersonUpsertService
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.asana.api.AsanaApiProvider
import com.nextchaptersoftware.asana.auth.oauth.AsanaProviderAuthApi
import com.nextchaptersoftware.asana.ingestion.queue.enqueue.AsanaEventEnqueueService
import com.nextchaptersoftware.asana.ingestion.services.AsanaMemberModelService
import com.nextchaptersoftware.asana.services.AsanaTokenProvider
import com.nextchaptersoftware.asset.ScmAssetService
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApiImpl
import com.nextchaptersoftware.atlassian.api.AtlassianDataCenterAuthProvider
import com.nextchaptersoftware.atlassian.api.NoopAtlassianAuthApi
import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.oauth.NoopOAuthTokenRefresher
import com.nextchaptersoftware.auth.provider.services.ProviderAuthenticationStateService
import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UnencryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.aws.sts.StandardStsProviderFactory
import com.nextchaptersoftware.aws.sts.StsProviderFactory
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.services.BillingService
import com.nextchaptersoftware.billing.services.PlanUpdateLockProvider
import com.nextchaptersoftware.billing.services.PlanUpdateResolver
import com.nextchaptersoftware.billing.services.PlanUpdateService
import com.nextchaptersoftware.cas.RedisCAS
import com.nextchaptersoftware.ci.config.CISecretsConfig
import com.nextchaptersoftware.coda.api.CodaApiProvider
import com.nextchaptersoftware.coda.events.queue.enqueue.CodaEventEnqueueService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterApiProvider
import com.nextchaptersoftware.confluence.enqueue.ConfluenceEventEnqueueService
import com.nextchaptersoftware.confluence.ingestion.services.ConfluenceMemberModelService
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSAClientServerCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.data.preset.config.DataSourcePresetService
import com.nextchaptersoftware.data.preset.config.providers.AsanaDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.CodaDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.ConfluenceDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.DataSourcePresetInstallationProvider
import com.nextchaptersoftware.data.preset.config.providers.GoogleDriveDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.GoogleDriveWorkspaceDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.JiraDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.LinearDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.ScmDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.SlackDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.data.preset.config.providers.WebDataSourcePresetInstallationGroupProvider
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.provider.GoogleAccessComputeService
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.api.GoogleAuthApiProvider
import com.nextchaptersoftware.google.auth.oauth.GoogleProviderAuthApi
import com.nextchaptersoftware.google.auth.services.GoogleAuthInstallService
import com.nextchaptersoftware.google.events.queue.enqueue.GoogleEventEnqueueService
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.google.services.GoogleWorkspaceServiceAccountKeyProvider
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.installation.services.InstallationCreationService
import com.nextchaptersoftware.integration.queue.redis.cache.NoopIngestionProgressServiceProvider
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.api.JiraDataCenterApiProvider
import com.nextchaptersoftware.jira.ingestion.services.JiraMemberModelService
import com.nextchaptersoftware.jira.queue.enqueue.JiraEventEnqueueService
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.auth.oauth.LinearProviderAuthApi
import com.nextchaptersoftware.linear.auth.services.LinearAuthInstallService
import com.nextchaptersoftware.linear.ingestion.services.LinearMemberModelService
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamMaintenance
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.membership.alignment.IdentityAlignment
import com.nextchaptersoftware.microsoft.graph.auth.MicrosoftGraphMemberModelService
import com.nextchaptersoftware.microsoft.graph.oauth.MicrosoftGraphProviderApi
import com.nextchaptersoftware.microsoftgraph.api.MicrosoftGraphApiProvider
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.notion.api.NotionOauthTokenProvider
import com.nextchaptersoftware.notion.auth.services.NotionAuthInstallService
import com.nextchaptersoftware.notion.events.queue.enqueue.NotionEventEnqueueService
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.proxy.provider.delegates.AsanaConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.CodaConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.ConfluenceConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.DataSourcePresetsConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.GoogleDriveConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.JiraConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.MicrosoftConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.PlanConfigurationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.ScmInstallationDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.StackOverflowTeamsDelegateImpl
import com.nextchaptersoftware.proxy.provider.delegates.providers.AsanaProjectProvider
import com.nextchaptersoftware.proxy.provider.delegates.providers.ConfluenceSpaceProvider
import com.nextchaptersoftware.proxy.provider.delegates.providers.JiraProjectProvider
import com.nextchaptersoftware.proxy.provider.plugins.configureRouting
import com.nextchaptersoftware.proxy.provider.rpc.handlers.AwsStsHandler
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.ScmWebhookService
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.delegates.ScmAppDelegateImpl
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.scm.services.ScmAppRequestSyncService
import com.nextchaptersoftware.scm.utils.DefaultEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionNotifier
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.service.plugins.configureStatusPages
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.auth.services.SlackAuthInstallService
import com.nextchaptersoftware.slack.auth.services.SlackOpenIdConnectService
import com.nextchaptersoftware.slack.auth.services.SlackUserAuthInstallService
import com.nextchaptersoftware.slack.auth.services.SlackUserAuthLoginExchange
import com.nextchaptersoftware.slack.config.SlackConfigProvider
import com.nextchaptersoftware.slack.events.queue.enqueue.SlackEventEnqueueService
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelPreferencesResolverService
import com.nextchaptersoftware.slack.services.SlackMemberModelService
import com.nextchaptersoftware.slack.services.SlackUserConnectService
import com.nextchaptersoftware.stripe.services.StripeService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import io.ktor.server.application.Application
import kotlin.getValue
import kotlin.time.Duration.Companion.minutes

@Suppress("LongMethod", "CyclomaticComplexMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle,
    ciSecretsConfig: CISecretsConfig = CISecretsConfig.INSTANCE,
    config: GlobalConfig = GlobalConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    userSecretConfig: UserSecretConfig = UserSecretConfig.INSTANCE,
    stsProviderFactory: StsProviderFactory = StandardStsProviderFactory(),
) {
    val jwt by lazy {
        Jwt(authenticationConfig = config.authentication)
    }

    val insiderService by lazy {
        InsiderService()
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
        )
    }

    val enterpriseAppConfigOrgIdsResolver by lazy {
        DefaultEnterpriseAppConfigOrgIdsResolver()
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = ScmWebFactory(scmConfig),
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val userSecretServiceResolver by lazy {
        UserSecretServiceResolver(
            userSecretServiceRSA = UserSecretService(
                encryption = RSACryptoSystem.RSAEncryption(
                    publicKey = config.encryption.userSecrets4096PublicKey,
                    modulusBitLength = 4096,
                ),
                decryption = RSACryptoSystem.RSADecryption(
                    privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                    modulusBitLength = 4096,
                ),
            ),
            userSecretServiceAES = AESCryptoSystem.importKey(userSecretConfig.encryption.userSecretsAesKey.value).let {
                UserSecretService(
                    encryption = AESCryptoSystem.AESEncryption(it),
                    decryption = AESCryptoSystem.AESDecryption(it),
                )
            },
        )
    }

    val unencryptedTokenPersistence by lazy {
        UnencryptedTokenPersistence(
            identityStore = Stores.identityStore,
        )
    }

    val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmAppApiFactory by lazy {
        ScmAppApiFactory(
            scmConfig = scmConfig,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmTeamApiFactory by lazy {
        ScmTeamApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmAppApiFactory = scmAppApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val repoComputeService by lazy {
        LocalRepoComputeService(
            scmTeamApiFactory = scmTeamApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    val scmRepoApiFactory by lazy {
        ScmRepoApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmAppApiFactory = scmAppApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmAssetService by lazy {
        ScmAssetService(
            repoAccessService = repoAccessService,
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val scmNoAuthApiFactory by lazy {
        ScmNoAuthApiFactory(
            scmConfig = scmConfig,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val scmMembershipMaintenance by lazy {
        ScmMembershipMaintenance(
            slackNotifier = slackNotifier,
        )
    }

    val repoMaintenance by lazy {
        RepoMaintenance()
    }

    val scmWebhookService by lazy {
        ScmWebhookService(
            scmConfig = scmConfig,
        )
    }

    val scmInstallService by lazy {
        ScmInstallationService(
            scmInstallationDelegate = ScmInstallationDelegateImpl(
                scmAppRequestSyncService = ScmAppRequestSyncService(
                    scmAppApiFactory = scmAppApiFactory,
                ),
                scmTeamMaintenance = ScmTeamMaintenance(
                    scmMembershipMaintenance = scmMembershipMaintenance,
                    lockProvider = LockProvider(
                        type = LockType.ScmMaintenanceScmTeam,
                    ),
                    repoMaintenance = repoMaintenance,
                    scmTeamApiFactory = scmTeamApiFactory,
                    scmUserApiFactory = scmUserApiFactory,
                    scmWebhookService = scmWebhookService,
                ),
                scmTeamApiFactory = scmTeamApiFactory,
                scmUserApiFactory = scmUserApiFactory,
            ),
            scmNoAuthApiFactory = scmNoAuthApiFactory,
            scmWebFactory = scmWebFactory,
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val installationCreationService by lazy {
        InstallationCreationService(slackNotifier = slackNotifier)
    }

    val googleOAuthRefreshService by lazy {
        UserSecretOAuthRefreshService(
            tokenRefresher = NoopOAuthTokenRefresher(), // Not used for Google OAuth
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretServiceResolver.resolve(Provider.GoogleDrive)),
        )
    }

    val googleWorkspaceServiceAccountKeyProvider by lazy {
        GoogleWorkspaceServiceAccountKeyProvider(
            userSecretService = userSecretServiceResolver.resolve(Provider.GoogleDriveWorkspace),
        )
    }

    val googleCredentialProvider by lazy {
        GoogleCredentialProvider(
            config = config.providers.googleDrive,
            oAuthRefreshService = googleOAuthRefreshService,
            googleWorkspaceServiceAccountKeyProvider = googleWorkspaceServiceAccountKeyProvider,
        )
    }

    val googleApiProvider by lazy {
        GoogleApiProvider()
    }

    val googleProviderAuthApi by lazy {
        config.providers.googleDrive?.let {
            GoogleProviderAuthApi(
                googleApiProvider = googleApiProvider,
                googleAuthApiProvider = GoogleAuthApiProvider(config = it),
                googleCredentialProvider = googleCredentialProvider,
                googleAuthInstallService = GoogleAuthInstallService(
                    userSecretService = userSecretServiceResolver.resolve(Provider.GoogleDrive),
                    installationCreationService = installationCreationService,
                ),
            )
        }
    }

    val googleEventEnqueueService by lazy {
        GoogleEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.googleEventsQueueName,
                ),
            ),
            progressServiceProvider = NoopIngestionProgressServiceProvider(),
        )
    }

    val googleDriveConfigurationService by lazy {
        GoogleDriveConfigurationService(
            googleDriveConfigurationDelegate = GoogleDriveConfigurationDelegateImpl(
                googleApiProvider = googleApiProvider,
                googleEventEnqueueService = googleEventEnqueueService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                accessComputeService = GoogleAccessComputeService(
                    googleApiProvider = googleApiProvider,
                    googleCredentialProvider = googleCredentialProvider,
                ),
                googleCredentialProvider = googleCredentialProvider,
                userSecretServiceResolver = userSecretServiceResolver,
                installationCreationService = installationCreationService,
            ),
        )
    }

    val stackOverflowTeamsService by lazy {
        StackOverflowTeamsService(
            stackOverflowTeamsDelegate = StackOverflowTeamsDelegateImpl(
                userSecretService = userSecretServiceResolver.resolve(Provider.StackOverflowTeams),
                client = StackOverflowTeamsDelegateImpl.StackOverflowTeamsClient(
                    config = config.providers.stackOverflowTeams,
                ),
                installationCreationService = installationCreationService,
            ),
        )
    }

    val codaApiProvider by lazy {
        CodaApiProvider(config.providers.coda)
    }

    val codaEventEnqueueService by lazy {
        CodaEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.codaEventsQueueName,
                ),
            ),
        )
    }

    val codaConfigurationService by lazy {
        CodaConfigurationService(
            codaConfigurationDelegate = CodaConfigurationDelegateImpl(
                codaEventEnqueueService = codaEventEnqueueService,
                codaApiProvider = codaApiProvider,
                userSecretService = userSecretServiceResolver.resolve(Provider.Coda),
                installationCreationService = installationCreationService,
            ),
        )
    }

    val tokenSecretDecryption by lazy {
        // TODO refactor this to support using a team specific private key to decrypt
        RSAClientServerCryptoSystem.RSADecryption(ciSecretsConfig.ci.secretsPrivateKey.value)
    }

    val dataCenterAuthProvider by lazy {
        AtlassianDataCenterAuthProvider(tokenSecretDecryption::decrypt)
    }

    val confluenceDataCenterApiProvider by lazy {
        ConfluenceDataCenterApiProvider(dataCenterAuthProvider)
    }

    val confluenceEventEnqueueService by lazy {
        ConfluenceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.confluenceEventsQueueName,
                ),
            ),
        )
    }

    val asanaApiProvider by lazy {
        config.providers.asana?.let {
            AsanaApiProvider(
                config = it,
            )
        }
    }

    val asanaTokenProvider by lazy {
        asanaApiProvider?.let {
            AsanaTokenProvider(
                oauthTokenRefreshService = UserSecretOAuthRefreshService(
                    tokenRefresher = it.authApi,
                    tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretServiceResolver.resolve(Provider.Asana)),
                ),
            )
        }
    }

    val asanaEventEnqueueService by lazy {
        AsanaEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.asanaEventsQueueName,
                ),
            ),
        )
    }

    val asanaMemberModelService by lazy {
        AsanaMemberModelService(
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretServiceResolver.resolve(Provider.Asana)),
        )
    }

    val asanaProviderAuthApi by lazy {
        asanaApiProvider?.let {
            AsanaProviderAuthApi(
                asanaApiProvider = it,
                asanaMemberModelService = asanaMemberModelService,
            )
        }
    }

    val asanaDataSourcePresetInstallationGroupProvider by lazy {
        AsanaDataSourcePresetInstallationGroupProvider(
            asanaProjectProvider = AsanaProjectProvider()::getAvailableProjects,
        )
    }

    val asanaConfigurationDelegate by lazy {
        asanaApiProvider?.let {
            AsanaConfigurationDelegateImpl(
                asanaApiProvider = it,
                asanaMemberModelService = asanaMemberModelService,
                asanaEventEnqueueService = asanaEventEnqueueService,
                asanaTokenProvider = asanaTokenProvider ?: return@let null,
                installationCreationService = installationCreationService,
            )
        } ?: NoopAsanaConfigurationDelegate()
    }

    val jiraAuthApi by lazy {
        config.providers.jira?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val jiraApiProvider by lazy {
        JiraApiProvider()
    }

    val jiraDataCenterApiProvider by lazy {
        JiraDataCenterApiProvider(dataCenterAuthProvider)
    }

    val jiraEventEnqueueService by lazy {
        JiraEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.jiraEventsQueueName,
                ),
            ),
        )
    }

    val jiraMemberModelService by lazy {
        JiraMemberModelService(
            config = config.providers.jira,
            tokenPersistence = unencryptedTokenPersistence,
        )
    }

    val jiraConfigurationDelegate by lazy {
        JiraConfigurationDelegateImpl(
            jiraApiProvider = jiraApiProvider,
            jiraDataCenterApiProvider = jiraDataCenterApiProvider,
            jiraEventEnqueueService = jiraEventEnqueueService,
            jiraMemberModelService = jiraMemberModelService,
            tokenSecretDecryption = tokenSecretDecryption,
            installationCreationService = installationCreationService,
        )
    }

    val confluenceApiAuth by lazy {
        config.providers.confluence?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val confluenceAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = confluenceApiAuth,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val confluenceCloudApiProvider by lazy {
        ConfluenceCloudApiProvider()
    }

    val confluenceMemberModelService by lazy {
        ConfluenceMemberModelService(
            tokenPersistence = unencryptedTokenPersistence,
        )
    }

    val confluenceConfigurationService by lazy {
        ConfluenceConfigurationService(
            confluenceConfigurationDelegate = ConfluenceConfigurationDelegateImpl(
                confluenceCloudApiProvider = confluenceCloudApiProvider,
                confluenceDataCenterApiProvider = confluenceDataCenterApiProvider,
                confluenceEventEnqueueService = confluenceEventEnqueueService,
                tokenSecretDecryption = tokenSecretDecryption,
                confluenceMemberModelService = confluenceMemberModelService,
                confluenceSpaceProvider = ConfluenceSpaceProvider(
                    atlassianTokenProvider = confluenceAtlassianTokenProvider,
                    confluenceCloudApiProvider = confluenceCloudApiProvider,
                ),
                installationCreationService = installationCreationService,
            ),
        )
    }

    val loginUrlService by lazy {
        LoginUrlService(
            deploymentConfig = config.service,
        )
    }

    val restrictedAccessService by lazy {
        RestrictedAccessServiceFactory.fromConfig()
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val personEmailPreferencesService by lazy {
        PersonEmailPreferencesService()
    }

    val planCapabilitiesService by lazy {
        PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val memberService by lazy {
        MemberService()
    }

    val emailService by lazy {
        EmailService(
            notificationEventEnqueueService = notificationEventEnqueueService,
            personEmailPreferencesService = personEmailPreferencesService,
            memberService = memberService,
        )
    }

    val inactiveFollowupService by lazy {
        InactiveFollowupService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val searchPriorityEventEnqueueService by lazy {
        SearchPriorityEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchPriorityEventsQueueName,
                ),
            ),
        )
    }

    val sampleQuestionNotifier by lazy {
        SampleQuestionNotifier(
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        )
    }

    val personUpsertService by lazy {
        PersonUpsertService(
            emailService = emailService,
            inactiveFollowupService = inactiveFollowupService,
            sampleQuestionNotifier = sampleQuestionNotifier,
            slackNotifier = slackNotifier,
        )
    }

    val scmIdentityAuthExchangeService by lazy {
        ScmIdentityAuthExchangeService(
            scmUserApiFactory = scmUserApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            restrictedAccessService = restrictedAccessService,
            userSecretServiceResolver = userSecretServiceResolver,
            personUpsertService = personUpsertService,
        )
    }

    val samlCodeExchangeStore by lazy {
        RedisCAS(namespace = "SAML-CODE-EXCHANGE", ttl = 5.minutes)
    }

    val linearProviderAuthApi by lazy {
        config.providers.linear?.let {
            val linearApiProvider = LinearApiProvider(
                config = it,
            )
            val linearAuthInstallService = LinearAuthInstallService(
                linearApiProvider = linearApiProvider,
                linearMemberModelService = LinearMemberModelService(
                    userSecretService = userSecretServiceResolver.resolve(Provider.Linear),
                ),
                installationCreationService = installationCreationService,
            )
            LinearProviderAuthApi(
                linearApiProvider = linearApiProvider,
                linearAuthInstallService = linearAuthInstallService,
            )
        }
    }

    val microsoftGraphMemberModelService by lazy {
        MicrosoftGraphMemberModelService(
            tokenPersistence = EncryptedTokenPersistence(
                userSecretService = userSecretServiceResolver.resolve(Provider.MicrosoftTeams),
            ),
        )
    }

    val microsoftGraphApiProvider by lazy {
        config.providers.microsoftTeams?.let {
            MicrosoftGraphApiProvider(
                config = it,
            )
        }
    }

    val microsoftGraphProviderAuthApi by lazy {
        microsoftGraphApiProvider?.let {
            MicrosoftGraphProviderApi(
                microsoftGraphApiProvider = it,
                microsoftGraphMemberModelService = microsoftGraphMemberModelService,
            )
        }
    }

    val microsoftConfigurationService by lazy {
        MicrosoftConfigurationService(
            microsoftConfigurationDelegate = MicrosoftConfigurationDelegateImpl(
                installationCreationService = installationCreationService,
            ),
        )
    }

    val notionEventEnqueueService by lazy {
        NotionEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notionEventsQueueName,
                ),
            ),
            progressServiceProvider = NoopIngestionProgressServiceProvider(),
        )
    }

    val notionAuthInstallService by lazy {
        NotionAuthInstallService(
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretServiceResolver.resolve(Provider.Notion)),
            notionEventEnqueueService = notionEventEnqueueService,
            installationCreationService = installationCreationService,
        )
    }

    val notionTokenProvider by lazy {
        NotionOauthTokenProvider(
            userSecretService = userSecretServiceResolver.resolve(Provider.Notion),
        )
    }

    val providerAuthenticationStateService by lazy {
        ProviderAuthenticationStateService(
            jwt = jwt,
            expiry = config.providers.exchangeTokenExpiry,
        )
    }

    val slackMemberModelService by lazy {
        SlackMemberModelService()
    }

    val identityAlignment by lazy {
        IdentityAlignment()
    }

    val slackEventEnqueueService by lazy {
        SlackEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackEventsQueueName,
                ),
            ),
        )
    }

    val slackApiProvider by lazy {
        SlackApiProvider()
    }

    val slackConfigProvider by lazy {
        SlackConfigProvider()
    }

    val slackAuthInstallService by lazy {
        SlackAuthInstallService(
            slackApiProvider = slackApiProvider,
            userSecretService = userSecretServiceResolver.resolve(Provider.Slack),
            slackEventEnqueueService = slackEventEnqueueService,
            slackNotifier = slackNotifier,
            providerAuthenticationStateService = providerAuthenticationStateService,
            slackConfigProvider = slackConfigProvider,
            identityAlignment = identityAlignment,
            slackMemberModelService = slackMemberModelService,
            installationCreationService = installationCreationService,
        )
    }

    val slackUserConnectService by lazy {
        SlackUserConnectService(
            slackNotifier = slackNotifier,
        )
    }

    val slackOpenIdConnectService by lazy {
        SlackOpenIdConnectService(
            slackConfigProvider = slackConfigProvider,
            slackApiProvider = slackApiProvider,
        )
    }

    val slackUserAuthInstallService by lazy {
        SlackUserAuthInstallService(
            slackOpenIdConnectService = slackOpenIdConnectService,
            slackMemberModelService = slackMemberModelService,
            identityAlignment = identityAlignment,
            slackUserConnectService = slackUserConnectService,
        )
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackChannelPreferencesResolverService by lazy {
        SlackChannelPreferencesResolverService(
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val googleDriveDataSourcePresetInstallationGroupProvider by lazy {
        GoogleDriveDataSourcePresetInstallationGroupProvider(
            googleConfigurationProvider = googleDriveConfigurationService::getConfiguration,
        )
    }

    val googleDriveWorkspaceDataSourcePresetInstallationGroupProvider by lazy {
        GoogleDriveWorkspaceDataSourcePresetInstallationGroupProvider(
            googleDriveDataSourcePresetInstallationGroupProvider = googleDriveDataSourcePresetInstallationGroupProvider,
        )
    }

    val jiraDataSourcePresetInstallationGroupProvider by lazy {
        JiraDataSourcePresetInstallationGroupProvider(
            jiraProjectProvider = JiraProjectProvider()::accessibleProjects,
        )
    }

    val slackDataSourcePresetInstallationGroupProvider by lazy {
        SlackDataSourcePresetInstallationGroupProvider(
            slackChannelsSelected = slackChannelPreferencesResolverService::getAllSelectedSlackChannels,
        )
    }

    val dataSourcePresetInstallationProvider by lazy {
        DataSourcePresetInstallationProvider(
            repoAccessService = repoAccessService,
            scmDataSourcePresetInstallationProvider = ScmDataSourcePresetInstallationGroupProvider(),
            confluenceDataSourcePresetInstallationGroupProvider = ConfluenceDataSourcePresetInstallationGroupProvider(),
            asanaDataSourcePresetInstallationGroupProvider = asanaDataSourcePresetInstallationGroupProvider,
            jiraDataSourcePresetInstallationGroupProvider = jiraDataSourcePresetInstallationGroupProvider,
            linearDataSourcePresetInstallationGroupProvider = LinearDataSourcePresetInstallationGroupProvider(),
            googleDriveDataSourcePresetInstallationGroupProvider = googleDriveDataSourcePresetInstallationGroupProvider,
            googleDriveWorkspaceDataSourcePresetInstallationGroupProvider = googleDriveWorkspaceDataSourcePresetInstallationGroupProvider,
            webDataSourcePresetInstallationGroupProvider = WebDataSourcePresetInstallationGroupProvider(),
            codaDataSourcePresetInstallationGroupProvider = CodaDataSourcePresetInstallationGroupProvider(),
            slackDataSourcePresetInstallationGroupProvider = slackDataSourcePresetInstallationGroupProvider,
        )
    }

    val dataSourcePresetService by lazy {
        DataSourcePresetService(
            dataSourcePresetInstallationProvider = dataSourcePresetInstallationProvider,
        )
    }

    val dataSourcePresetsConfigurationDelegate by lazy {
        DataSourcePresetsConfigurationDelegateImpl(
            dataSourcePresetInstallationProvider = dataSourcePresetInstallationProvider,
            dataSourcePresetService = dataSourcePresetService,
        )
    }

    val stripeService by lazy {
        StripeService()
    }

    val billingService by lazy {
        BillingService(
            stripeService = stripeService,
            planCapabilitiesService = planCapabilitiesService,
            enableTestBillingActions = config.featureFlags.enableTestBillingActions,
            config = config.billing,
        )
    }

    val planUpdateLockService by lazy {
        PlanUpdateLockProvider()
    }

    val planUpdateResolver by lazy {
        PlanUpdateResolver(billingService = billingService)
    }

    val scmAppDelegate by lazy {
        ScmAppDelegateImpl(
            scmAppApiFactory = scmAppApiFactory,
        )
    }

    val billingEventEnqueueService by lazy {
        BillingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.billingEventsQueueName,
                ),
            ),
        )
    }

    val planUpdateService by lazy {
        PlanUpdateService(
            billingService = billingService,
            slackNotifier = slackNotifier,
            planUpdateResolver = planUpdateResolver,
            planUpdateLockProvider = planUpdateLockService,
            notificationEventEnqueueService = notificationEventEnqueueService,
            billingEventEnqueueService = billingEventEnqueueService,
        )
    }

    val planConfigurationService by lazy {
        PlanConfigurationService(
            planConfigurationDelegate = PlanConfigurationDelegateImpl(
                billingService = billingService,
                planUpdateService = planUpdateService,
                planCapabilitiesService = planCapabilitiesService,
                slackNotifier = slackNotifier,
            ),
        )
    }

    val slackUserAuthLoginExchange by lazy {
        SlackUserAuthLoginExchange(
            slackOpenIdConnectService = slackOpenIdConnectService,
            slackMemberModelService = slackMemberModelService,
            identityAlignment = identityAlignment,
            personUpsertService = personUpsertService,
            slackUserConnectService = slackUserConnectService,
        )
    }

    val providerIdentityAuthExchangeService by lazy {
        ProviderIdentityAuthExchangeService(
            ssoIdentityAuthExchangeService = SSOIdentityAuthExchangeService(
                samlCodeExchangeStore = samlCodeExchangeStore,
                personUpsertService = personUpsertService,
            ),
            slackIdentityAuthExchangeService = SlackIdentityAuthExchangeService(
                slackUserAuthLoginExchange = slackUserAuthLoginExchange,
            ),
        )
    }

    val awsStsHandler by lazy {
        AwsStsHandler(
            stsProvider = stsProviderFactory.generate(awsClientProvider = AWSClientProvider.from(ServiceInitializer.REGION.toRegion())),
        )
    }

    configureRouting(
        asanaConfigurationDelegate = asanaConfigurationDelegate,
        awsStsHandler = awsStsHandler,
        codaConfigurationService = codaConfigurationService,
        confluenceAuthApi = confluenceApiAuth,
        confluenceCloudApiProvider = confluenceCloudApiProvider,
        confluenceMemberModelService = confluenceMemberModelService,
        confluenceConfigurationService = confluenceConfigurationService,
        dataSourcePresetsConfigurationDelegate = dataSourcePresetsConfigurationDelegate,
        googleProviderAuthApi = googleProviderAuthApi,
        googleDriveConfigurationService = googleDriveConfigurationService,
        asanaProviderAuthApi = asanaProviderAuthApi,
        jiraApiProvider = jiraApiProvider,
        jiraAuthApi = jiraAuthApi,
        jiraConfigurationDelegate = jiraConfigurationDelegate,
        jiraMemberModelService = jiraMemberModelService,
        linearProviderAuthApi = linearProviderAuthApi,
        microsoftConfigurationService = microsoftConfigurationService,
        microsoftGraphProviderAuthApi = microsoftGraphProviderAuthApi,
        loginUrlService = loginUrlService,
        notionAuthInstallService = notionAuthInstallService,
        notionTokenProvider = notionTokenProvider,
        planConfigurationService = planConfigurationService,
        providerIdentityAuthExchangeService = providerIdentityAuthExchangeService,
        repoComputeService = repoComputeService,
        scmAppApiFactory = scmAppApiFactory,
        scmAppDelegate = scmAppDelegate,
        scmAssetService = scmAssetService,
        scmAuthApiFactory = scmAuthApiFactory,
        scmIdentityAuthExchangeService = scmIdentityAuthExchangeService,
        scmInstallService = scmInstallService,
        scmNoAuthApiFactory = scmNoAuthApiFactory,
        scmRepoApiFactory = scmRepoApiFactory,
        scmTeamApiFactory = scmTeamApiFactory,
        scmUserApiFactory = scmUserApiFactory,
        serviceLifecycle = serviceLifecycle,
        slackAuthInstallService = slackAuthInstallService,
        slackUserAuthInstallService = slackUserAuthInstallService,
        stackOverflowTeamsService = stackOverflowTeamsService,
        userSecretServiceResolver = userSecretServiceResolver,
    )
    configureMonitoring(
        insiderService = NoOpInsiderService(),
    )
    configureSerialization()
    configureStatusPages()
    configureJvmMetrics()
}
