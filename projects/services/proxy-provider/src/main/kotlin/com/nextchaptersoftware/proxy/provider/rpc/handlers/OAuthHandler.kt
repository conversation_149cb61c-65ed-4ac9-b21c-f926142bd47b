package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.asana.auth.oauth.AsanaProviderAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.confluence.auth.oauth.ConfluenceProviderAuthApi
import com.nextchaptersoftware.google.auth.oauth.GoogleProviderAuthApi
import com.nextchaptersoftware.jira.auth.oauth.JiraProviderAuthApi
import com.nextchaptersoftware.linear.auth.oauth.LinearProviderAuthApi
import com.nextchaptersoftware.microsoft.graph.oauth.MicrosoftGraphProviderApi
import com.nextchaptersoftware.notion.auth.oauth.NotionProviderAuthApi
import com.nextchaptersoftware.rpc.calls.OAuthCalls
import com.nextchaptersoftware.rpc.calls.OAuthCalls.OAuthExchangeParams
import com.nextchaptersoftware.rpc.calls.OAuthCalls.OAuthExchangeResult
import com.nextchaptersoftware.slack.auth.oauth.SlackProviderAuthApi

class OAuthHandler(
    private val asanaProviderAuthApi: AsanaProviderAuthApi?,
    private val confluenceProviderAuthApi: ConfluenceProviderAuthApi?,
    private val googleProviderAuthApi: GoogleProviderAuthApi?,
    private val microsoftGraphProviderAuthApi: MicrosoftGraphProviderApi?,
    private val notionProviderAuthApi: NotionProviderAuthApi?,
    private val jiraProviderAuthApi: JiraProviderAuthApi?,
    private val linearProviderAuthApi: LinearProviderAuthApi?,
    private val slackProviderAuthApi: SlackProviderAuthApi,
) : OAuthCalls {

    override suspend fun oauthExchange(params: OAuthExchangeParams): OAuthExchangeResult {
        val oauthApi: OAuthApi = getApi(params.provider)
        val oauthTokenExchange = oauthApi.exchangeForToken(params.context)

        return OAuthExchangeResult(
            url = oauthTokenExchange.redirectUrl,
        )
    }

    @Suppress("CyclomaticComplexMethod")
    private fun getApi(provider: Provider): OAuthApi {
        return when (provider) {
            Provider.asana -> checkNotNull(asanaProviderAuthApi) { "Asana is not configured for this environment" }

            Provider.confluence -> checkNotNull(confluenceProviderAuthApi) { "Confluence is not configured for this environment" }

            Provider.google -> checkNotNull(googleProviderAuthApi) { "Google Drive Cloud is not configured for this environment" }

            Provider.jira -> checkNotNull(jiraProviderAuthApi) { "Jira is not configured for this environment" }

            Provider.linear -> checkNotNull(linearProviderAuthApi) { "Linear is not configured for this environment" }

            Provider.microsoftTeams -> checkNotNull(microsoftGraphProviderAuthApi) { "Microsoft Teams is not configured for this environment" }

            Provider.notion -> checkNotNull(notionProviderAuthApi) { "Notion is not configured for this environment" }

            Provider.slack -> slackProviderAuthApi

            // Not applicable
            Provider.aws,
            Provider.awsIdentityCenter,
            Provider.azureDevOps,
            Provider.bitbucket,
            Provider.bitbucketDataCenter,
            Provider.bitbucketPipelines,
            Provider.buildKite,
            Provider.circleci,
            Provider.coda,
            Provider.confluenceDataCenter,
            Provider.customIntegration,
            Provider.github,
            Provider.githubActions,
            Provider.githubEnterprise,
            Provider.gitlab,
            Provider.gitlabPipelines,
            Provider.gitlabSelfHosted,
            Provider.googleDriveWorkspace,
            Provider.googleWorkspace,
            Provider.jenkins,
            Provider.jiraDataCenter,
            Provider.microsoftEntra,
            Provider.okta,
            Provider.pingOne,
            Provider.saml,
            Provider.stackOverflowTeams,
            Provider.unblocked,
            Provider.web,
                -> error("${provider.name} is not supported")
        }
    }
}
