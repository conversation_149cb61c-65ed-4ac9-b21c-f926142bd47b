package com.nextchaptersoftware.slackservice

import com.aallam.openai.client.OpenAIHost
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.api.threads.services.ThreadUnreadServiceImpl
import com.nextchaptersoftware.aws.bedrock.anthropic.api.BedrockAnthropicCompletionsApi
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.utils.BillingPlanService
import com.nextchaptersoftware.bot.search.services.SemanticSearchQueryServiceFactory
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.bot.toolbox.CompositeShortCircuitBotInputFilter
import com.nextchaptersoftware.bot.toolbox.IsInvalidHumanQueryLengthFilter
import com.nextchaptersoftware.bot.toolbox.IsSlackPrivateScopesDisabledFilter
import com.nextchaptersoftware.bot.toolbox.IsTrialExpiredFilter
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationServiceImpl
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.event.queue.dequeue.RealtimeEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.event.queue.handlers.CompositeTypedEventHandler
import com.nextchaptersoftware.feedback.services.MessageFeedbackService
import com.nextchaptersoftware.gemini.api.GeminiApiProvider
import com.nextchaptersoftware.gemini.config.GeminiApiConfig
import com.nextchaptersoftware.ingestion.services.CompositeMarkdownPreProcessorService
import com.nextchaptersoftware.ingestion.services.EmojiMarkdownPreProcessorService
import com.nextchaptersoftware.ingestion.services.MarkdownToMessageBodyService
import com.nextchaptersoftware.ingestion.services.ThreadParticipantService
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.insight.refresh.InsightRefreshService
import com.nextchaptersoftware.integration.data.events.queue.enqueue.DataEventEnqueueService
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.maintenance.events.queue.enqueue.MaintenanceEventEnqueueService
import com.nextchaptersoftware.metrics.MetricsService
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.completion.AnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockAnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockConverseCompletionService
import com.nextchaptersoftware.ml.completion.CohereCompletionService
import com.nextchaptersoftware.ml.completion.DecisionCompletionService
import com.nextchaptersoftware.ml.completion.GeminiCompletionService
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.ml.completion.OpenAICompletionService
import com.nextchaptersoftware.ml.completion.RoundRobinCompletionService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.redis.store.InviteRedisStore
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAIApiProvider
import com.nextchaptersoftware.openai.api.delegates.AzureOpenAIApiProviderDelegate
import com.nextchaptersoftware.opensearch.api.OpenSearchApiConfiguration
import com.nextchaptersoftware.opensearch.api.OpenSearchApiProvider
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.opensearch.plugins.configureOpenSearch
import com.nextchaptersoftware.pinecone.api.PineconeApiConfiguration
import com.nextchaptersoftware.pinecone.api.PineconeControlPlaneApiProvider
import com.nextchaptersoftware.pinecone.config.PineconeConfig
import com.nextchaptersoftware.pinecone.index.PineconeIndexLoader
import com.nextchaptersoftware.pinecone.plugins.configurePinecone
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.product.feedback.ProductFeedbackService
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.search.indexing.events.queue.enqueue.SearchIndexingEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.bot.SlackBotQuestionService
import com.nextchaptersoftware.search.semantic.services.bot.notifier.SlackQAValidationNotifier
import com.nextchaptersoftware.search.semantic.services.documentation.SlackDocumentRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.SlackThreadRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.presets.MessageDataSourcePresetService
import com.nextchaptersoftware.search.semantic.services.references.MessageReferenceLinkResolver
import com.nextchaptersoftware.search.semantic.services.references.MessageReferencesService
import com.nextchaptersoftware.search.semantic.services.suggestions.MessageSuggestionsService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.semantic.bot.services.MessageMentionService
import com.nextchaptersoftware.semantic.bot.services.MessageService
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.lifecycle.ShutdownHookManager
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.slack.api.SlackApiConfiguration
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.SlackInstance
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.PendingSlackQuestionService
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.bot.events.queue.handlers.SlackBotEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotAutoAnswerApprovalButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotChannelSettingsDataSourceSelectEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotProductFeedbackButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotSemanticSearchApprovalButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotSemanticSearchCreateAccountButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotSemanticSearchFeedbackButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.blockaction.SlackBotSemanticSearchSuggestionButtonEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.channels.SlackBotChannelsJoinEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.channels.SlackBotChannelsLeaveEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.ephemeral.SlackBotClearEphemeralMessageEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.notifications.SlackBotPendingQuestionNotificationsEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.semantic.SlackBotAtMentionSemanticSearchEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.semantic.SlackBotAutoAnswerSemanticSearchEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.services.SlackBotChannelsJoinService
import com.nextchaptersoftware.slack.bot.events.queue.handlers.services.SlackBotChannelsLeaveService
import com.nextchaptersoftware.slack.bot.events.queue.handlers.services.SlackNotifyPendingQuestionsService
import com.nextchaptersoftware.slack.bot.events.queue.handlers.slashcommand.channelsettings.SlackBotChannelSettingsEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.slashcommand.channelsettings.SlackBotChannelSettingsHelpEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.view.SlackBotChannelSettingsViewSubmissionEventHandler
import com.nextchaptersoftware.slack.bot.events.queue.handlers.view.SlackBotSemanticSearchViewSubmissionEventHandler
import com.nextchaptersoftware.slack.bot.invites.SlackAccountInviteUrlBuilder
import com.nextchaptersoftware.slack.bot.models.payload.SlackBotSemanticSearchPayload
import com.nextchaptersoftware.slack.bot.models.view.DefaultSlackBotChannelSettingsViewBuilder
import com.nextchaptersoftware.slack.bot.models.view.SlackBotChannelSettingsUpsellViewBuilder
import com.nextchaptersoftware.slack.bot.services.completion.AnswerEvaluationService
import com.nextchaptersoftware.slack.bot.services.completion.IsQuestionCompletionService
import com.nextchaptersoftware.slack.bot.services.completion.TickleResponderService
import com.nextchaptersoftware.slack.bot.services.inference.SlackMessageFeedbackService
import com.nextchaptersoftware.slack.bot.services.inference.SlackMessageInferenceExampleService
import com.nextchaptersoftware.slack.bot.services.notification.CompositeSlackBotMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAppHomeOpenedPrivateScopesDisabledWelcomeNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAppHomeOpenedWelcomeConnectedNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAppHomeOpenedWelcomeDisconnectedNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAppHomeOpenedWelcomeLicenseRequiredNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAppHomePendingQuestionsContinuationNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionForceConnectMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionForceConnectWarningMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionLinkAccountsMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionLinkAccountsPayloadGenerator
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionProductFeedbackNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionRequestLicenseForLinkedAccountMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionRequestLicenseForLinkedAccountWarningMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAtMentionRequestLicenseForUnlinkedAccountMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAutoAnswerConnectedMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAutoAnswerDisconnectedMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotAutoAnswerTickleNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotChannelSettingsCommandHelpNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotExpiredTrialMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotHumanQueryOverLimitMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotIngestionPendingMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotMemberContextResolver
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotMemberJoinedDisclaimerNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotMemberJoinedPrivateScopesDisabledDisclaimerNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotNeedsAccountPayloadGenerator
import com.nextchaptersoftware.slack.bot.services.notification.SlackBotPrivateScopesDisabledMemberNotificationService
import com.nextchaptersoftware.slack.bot.services.notification.SlackUserQuestionsCountResolver
import com.nextchaptersoftware.slack.bot.services.semantic.SlackBotAtMentionSemanticSearchService
import com.nextchaptersoftware.slack.bot.services.semantic.SlackBotAutoAnswerSemanticSearchService
import com.nextchaptersoftware.slack.bot.services.semantic.component.CompositeSlackBotQuestionTransformer
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAnswerDsacService
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAnswerProcessor
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAnswerTextBlockConverter
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAtMentionNotificationService
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAutoAnswerNotificationService
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotAutoAnswerStateService
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotContextRetriever
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotEventProcessor
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotNotifier
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotPayloadContextRetriever
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotSlackMessageResolver
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackBotThreadManager
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackContextRetriever
import com.nextchaptersoftware.slack.bot.services.semantic.component.SlackMemberContextRetriever
import com.nextchaptersoftware.slack.bot.services.semantic.component.UnblockedBotAtMentionRemovalTransformer
import com.nextchaptersoftware.slack.bot.services.semantic.component.UserAtMentionUsernameHydrationTransformer
import com.nextchaptersoftware.slack.bot.services.semantic.filter.CompositeSlackBotSemanticInputFilter
import com.nextchaptersoftware.slack.bot.services.semantic.filter.SlackBotSemanticInputMentionFilter
import com.nextchaptersoftware.slack.bot.services.semantic.filter.SlackBotSemanticInputQuestionFilter
import com.nextchaptersoftware.slack.bot.services.semantic.filter.SlackBotSemanticInputTextFilter
import com.nextchaptersoftware.slack.bot.services.semantic.filter.SlackBotSemanticInputThreadFilter
import com.nextchaptersoftware.slack.bot.services.settings.SlackBotDisclaimerSettingsService
import com.nextchaptersoftware.slack.bot.services.settings.SlackBotSemanticSearchSettingsService
import com.nextchaptersoftware.slack.events.queue.enqueue.SlackEventEnqueueService
import com.nextchaptersoftware.slack.ingestion.services.BotSlackMessageMarkdownService
import com.nextchaptersoftware.slack.ingestion.services.CompositeSlackMessageMarkdownService
import com.nextchaptersoftware.slack.ingestion.services.SlackChannelDeletionService
import com.nextchaptersoftware.slack.ingestion.services.SlackChannelJoinService
import com.nextchaptersoftware.slack.ingestion.services.SlackChannelLeaveService
import com.nextchaptersoftware.slack.ingestion.services.SlackChannelMessageAnalysisService
import com.nextchaptersoftware.slack.ingestion.services.SlackChannelMessageIngestionService
import com.nextchaptersoftware.slack.ingestion.services.SlackMarkdownMentionResolutionService
import com.nextchaptersoftware.slack.ingestion.services.SlackMarkdownPreProcessorService
import com.nextchaptersoftware.slack.ingestion.services.SlackMessageBodyService
import com.nextchaptersoftware.slack.ingestion.services.SlackMessageModelService
import com.nextchaptersoftware.slack.ingestion.services.SlackMessageUrlService
import com.nextchaptersoftware.slack.ingestion.services.SlackPullRequestResolutionService
import com.nextchaptersoftware.slack.ingestion.services.SlackThreadFinalizationService
import com.nextchaptersoftware.slack.ingestion.services.SlackThreadModelService
import com.nextchaptersoftware.slack.ingestion.services.SlackWideThreadIngestionEnablementService
import com.nextchaptersoftware.slack.ingestion.services.StandardSlackMessageMarkdownService
import com.nextchaptersoftware.slack.ingestion.services.files.CompositeSlackFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.SlackFileEmbeddingEventEnqueueService
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackCSVFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackDOCXFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackJPGFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackPDFFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackPNGFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackTextFileHandler
import com.nextchaptersoftware.slack.ingestion.services.files.internal.SlackXLSXFileHandler
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.CompositeSlackMessageRelevancyVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.CompositeSlackThreadRelevancyVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackMessageRepliesVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackNonBotMessageVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackRequiredPullRequestsRelevancyVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackThreadDistinctUserVerifier
import com.nextchaptersoftware.slack.ingestion.utils.relevancy.SlackThreadSizeVerifier
import com.nextchaptersoftware.slack.notify.MermaidLinksDecoratorService
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.SlackActionResponseService
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackChannelPreferencesResolverService
import com.nextchaptersoftware.slack.services.SlackDeepLinkService
import com.nextchaptersoftware.slack.services.SlackMemberModelService
import com.nextchaptersoftware.slack.services.SlackMemberResolutionService
import com.nextchaptersoftware.slack.services.SlackSnippetFileContentExtractor
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.slack.services.SlackUserConnectService
import com.nextchaptersoftware.slack.services.SlackUserEngagementService
import com.nextchaptersoftware.slack.services.StandardSlackFileContentExtractor
import com.nextchaptersoftware.slack.webhook.queue.dequeue.SlackWebhookEventMessageProcessor
import com.nextchaptersoftware.slack.webhook.queue.handlers.AppHomeOpenedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.AppMentionEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.AppUninstalledEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.BlockActionEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelArchiveEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelCreatedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelDeletedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelLeftEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelRenameEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ChannelUnarchiveEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.FileChangeEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.FileCreatedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.FileDeletedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.GroupArchiveEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.GroupDeletedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.GroupLeftEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.GroupRenameEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.GroupUnarchiveEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MemberJoinedChannelEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MemberLeftChannelEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MessageBotEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MessageChangedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MessageDeletedEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MessageEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.MessageFileShareEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.SlackWebhookEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.SlashCommandEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.UserChangeEventHandler
import com.nextchaptersoftware.slack.webhook.queue.handlers.ViewSubmissionEventHandler
import com.nextchaptersoftware.slack.webhook.services.SlackAppHomeOpenedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackAppMentionEventService
import com.nextchaptersoftware.slack.webhook.services.SlackAppUninstalledEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelArchiveEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelCreatedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelDeletedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelLeftEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelRenameEventService
import com.nextchaptersoftware.slack.webhook.services.SlackChannelUnarchiveEventService
import com.nextchaptersoftware.slack.webhook.services.SlackFileChangeEventService
import com.nextchaptersoftware.slack.webhook.services.SlackFileCreatedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackFileDeletedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackGroupArchiveEventService
import com.nextchaptersoftware.slack.webhook.services.SlackGroupDeletedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackGroupLeftEventService
import com.nextchaptersoftware.slack.webhook.services.SlackGroupRenameEventService
import com.nextchaptersoftware.slack.webhook.services.SlackGroupUnarchiveEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMemberJoinedChannelEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMemberLeftChannelEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMessageChangedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMessageDeletedEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMessageEventService
import com.nextchaptersoftware.slack.webhook.services.SlackMessageFileShareEventService
import com.nextchaptersoftware.slack.webhook.services.SlackUserChangeEventService
import com.nextchaptersoftware.slack.webhook.services.bot.SlackBotAtMentionWebhookEventProcessor
import com.nextchaptersoftware.slack.webhook.services.bot.SlackBotAutoAnswerWebhookEventProcessor
import com.nextchaptersoftware.slack.webhook.services.context.SlackWebhookEventProcessorContextResolver
import com.nextchaptersoftware.slack.webhook.services.deletion.SlackFileEventDeletionService
import com.nextchaptersoftware.slack.webhook.services.deletion.SlackMessageEventDeletionService
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackChannelEventIngestionService
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackChannelMemberEventDeletionService
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackChannelMemberJoinedEventIngestionService
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackFileEventIngestionService
import com.nextchaptersoftware.slack.webhook.services.ingestion.SlackMessageEventIngestionService
import com.nextchaptersoftware.slackservice.jobs.SlackBotEventJob
import com.nextchaptersoftware.slackservice.jobs.SlackWebhookProcessingJob
import com.nextchaptersoftware.slackservice.plugins.configureRouting
import com.nextchaptersoftware.slackservice.rpc.MermaidDelegateViaRpc
import com.nextchaptersoftware.summarization.events.queue.enqueue.SummarizationEventEnqueueService
import com.nextchaptersoftware.topic.config.TopicConfig
import com.nextchaptersoftware.topic.insight.services.ClusterInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.DecisionInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.content.InsightContentModelService
import com.nextchaptersoftware.topic.insight.services.map.DecisionTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.QuestionAnswerThreadService
import com.nextchaptersoftware.topic.insight.services.map.StandardTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.TopicMappingRuleService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import io.ktor.server.application.Application
import kotlin.collections.map
import kotlin.collections.plus
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

/**
 * The number of concurrent consumers for the topic event queue.
 */
private const val CONSUMER_COUNT = 6
private const val PRIORITY_CONSUMER_COUNT = 2

@Suppress("LongMethod", "CyclomaticComplexMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = emptyList()),
    config: GlobalConfig = GlobalConfig.INSTANCE,
    openSearchConfig: OpenSearchConfig = OpenSearchConfig.INSTANCE,
    pineconeConfig: PineconeConfig = PineconeConfig.INSTANCE,
    topicConfig: TopicConfig = TopicConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    userSecretConfig: UserSecretConfig = UserSecretConfig.INSTANCE,
    geminiConfig: GeminiApiConfig = GeminiApiConfig.INSTANCE,
    bedrockRuntimeProviderFactory: BedrockRuntimeProviderFactory = StandardBedrockRuntimeProviderFactory(),
    bedrockRuntimeAsyncProviderFactory: BedrockRuntimeAsyncProviderFactory = StandardBedrockRuntimeAsyncProviderFactory(),
) {
    val openSearchApiProvider by lazy {
        OpenSearchApiProvider(
            config = OpenSearchApiConfiguration(
                baseApiUri = config.openSearch.baseApiUri.asUrl,
                timeout = config.openSearch.defaultTimeout,
                userName = config.openSearch.userName,
                password = config.openSearch.password,
            ),
        )
    }

    val openSearchIndexLoader by lazy {
        OpenSearchIndexLoader(
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchLockProvider by lazy {
        LockProvider(type = LockType.OpenSearchLoader)
    }

    val pineconeControlPlaneApiProvider by lazy {
        PineconeControlPlaneApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeIndexLoader by lazy {
        PineconeIndexLoader(
            pineconeControlPlaneApiProvider = pineconeControlPlaneApiProvider,
        )
    }

    val pineconeLockProvider by lazy {
        LockProvider(type = LockType.PineconeLoader)
    }

    val awsClientProvider by lazy {
        AWSClientProvider.from(
            region = ServiceInitializer.REGION.toRegion(),
        )
    }

    val bedrockRuntimeProvider by lazy {
        bedrockRuntimeProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockRuntimeAsyncProvider by lazy {
        bedrockRuntimeAsyncProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockAnthropicCompletionsApi by lazy {
        BedrockAnthropicCompletionsApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val slackApiConfiguration by lazy {
        SlackApiConfiguration()
    }

    val slackApiProvider by lazy {
        SlackApiProvider(
            configuration = slackApiConfiguration,
            client = SlackInstance
                .SAFE_RATE_LIMIT_INSTANCE
                .methodsAsync(),
        )
    }

    val openAIApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openAI.defaultTimeout,
                token = config.openAI.apiKey,
            ),
        )
    }

    val openRouterApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openRouter.defaultTimeout,
                token = config.openRouter.apiKey,
                host = OpenAIHost(baseUrl = config.openRouter.baseApiUri),
            ),
        )
    }

    val azureGPT41OpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41DeploymentId,
    )

    val azureGPT41NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41NanoDeploymentId,
    )

    val azureGPT41MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41MiniDeploymentId,
    )

    val azureGPT5MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5MiniDeploymentId,
    )

    val azureGPT5NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5NanoDeploymentId,
    )

    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val machineLearningCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                MachineLearningCompletionService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val anthropicApiConfiguration by lazy {
        AnthropicApiConfiguration(
            baseApiUri = config.anthropic.baseApiUri.asUrl,
            timeout = config.anthropic.defaultTimeout,
            token = config.anthropic.apiKey,
        )
    }

    val anthropicApiProvider by lazy {
        AnthropicApiProvider(
            config = anthropicApiConfiguration,
        )
    }

    val anthropicCompletionService by lazy {
        AnthropicCompletionService(
            anthropicApiProvider = anthropicApiProvider,
        )
    }

    val bedrockAnthropicCompletionService by lazy {
        BedrockAnthropicCompletionService(
            bedrockAnthropicCompletionsApi = bedrockAnthropicCompletionsApi,
        )
    }

    val bedrockConverseCompletionService by lazy {
        BedrockConverseCompletionService(
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val roundRobinAnthropicCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = listOf(
                anthropicCompletionService,
                bedrockAnthropicCompletionService,
            ),
        )
    }

    val roundRobinGPT41MiniCompletionService = RoundRobinCompletionService(
        completionServices = azureGPT41MiniApiProviders.plus(openAIApiProvider).map {
            OpenAICompletionService(
                openAIApiProvider = it,
                useResponsesApi = config.openAI.useResponsesApi,
            )
        },
    )

    val botAccountService by lazy {
        InstallationBotAccountService()
    }

    val userSecretServiceRSA by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = RSACryptoSystem.RSADecryption(
                privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                modulusBitLength = 4096,
            ),
        )
    }

    val userSecretService by lazy {
        // TODO: remove once all have been migrated to `userSecretServiceRSA`
        userSecretServiceRSA
    }

    val clientConfigService by lazy {
        ClientConfigService()
    }

    val insiderService by lazy {
        InsiderService()
    }

    val threadInsightIndexContentService by lazy {
        ThreadInsightIndexContentService()
    }

    val pullRequestInsightIndexContentService by lazy {
        PullRequestInsightIndexContentService()
    }

    val summarizationEventEnqueueService by lazy {
        SummarizationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.summarizationEventsQueueName,
                ),
            ),
        )
    }

    val inferenceService by lazy {
        MLInferenceService()
    }

    val maintenanceEventEnqueueService by lazy {
        MaintenanceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.maintenanceEventsQueueName,
                ),
            ),
        )
    }

    val messageFeedbackService by lazy {
        MessageFeedbackService(
            inferenceService = inferenceService,
            maintenanceEventEnqueueService = maintenanceEventEnqueueService,
        )
    }

    val planCapabilitiesService by lazy {
        PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val slackDocumentRetentionFilterService by lazy {
        SlackDocumentRetentionFilterService(
            slackThreadStore = Stores.slackThreadStore,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val searchIndexingEventEnqueueService by lazy {
        SearchIndexingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchIndexingEventsQueueName,
                ),
            ),
        )
    }

    val indexingAndEmbeddingService by lazy {
        IndexingAndEmbeddingService(
            searchIndexingEventEnqueueService = searchIndexingEventEnqueueService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackThreadRetentionFilterService by lazy {
        SlackThreadRetentionFilterService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val cohereApiProvider by lazy {
        CohereApiProvider(
            config = CohereApiConfiguration(config),
        )
    }

    val cohereCompletionService by lazy {
        CohereCompletionService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val geminiApiProvider by lazy {
        GeminiApiProvider(
            config = geminiConfig,
        )
    }

    val geminiCompletionService by lazy {
        GeminiCompletionService(
            geminiApiProvider = geminiApiProvider,
        )
    }

    val completionService by lazy {
        DecisionCompletionService(
            machineLearningCompletionService = machineLearningCompletionService,
            openAICompletionService = OpenAICompletionService(
                openAIApiProvider = openAIApiProvider,
                useResponsesApi = config.openAI.useResponsesApi,
            ),
            openRouterCompletionService = OpenAICompletionService(
                openAIApiProvider = openRouterApiProvider,
                useResponsesApi = config.openAI.useResponsesApi,
            ),
            bedrockAnthropicCompletionService = bedrockAnthropicCompletionService,
            anthropicCompletionService = anthropicCompletionService,
            roundRobinGPT41CompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41OpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT41NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT5MiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5MiniApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT5NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                        useResponsesApi = config.openAI.useResponsesApi,
                    )
                },
            ),
            roundRobinGPT41MiniCompletionService = roundRobinGPT41MiniCompletionService,
            roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
            cohereCompletionService = cohereCompletionService,
            geminiCompletionService = geminiCompletionService,
            bedrockConverseCompletionService = bedrockConverseCompletionService,
        )
    }

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val promptCompilerService by lazy {
        PromptCompilerService(
            scmWebFactory = scmWebFactory,
        )
    }

    val inferenceTemplateService by lazy {
        MLInferenceTemplateService()
    }

    val messageReferencesService by lazy {
        MessageReferencesService()
    }

    val messageDataSourcePresetService by lazy {
        MessageDataSourcePresetService()
    }

    val slackTokenService by lazy {
        SlackTokenService(
            userSecretService = userSecretService,
        )
    }

    val dataSourcePresetConfigurationService by lazy {
        DataSourcePresetConfigurationServiceImpl(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
        )
    }

    val questionAnswerThreadService by lazy {
        QuestionAnswerThreadService(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val topicMappingRuleService by lazy {
        TopicMappingRuleService(
            questionAnswerThreadService = questionAnswerThreadService,
        )
    }

    val insightContentModelService by lazy {
        InsightContentModelService(
            threadInsightIndexContentService = threadInsightIndexContentService,
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val semanticSearchQueryService by lazy {
        SemanticSearchQueryServiceFactory().generate(
                documentFilters = listOf(
                    slackDocumentRetentionFilterService,
                    slackThreadRetentionFilterService,
                ),
        )
    }

    val clusterInsightTopicsService by lazy {
        ClusterInsightTopicsService(
            insightContentModelService = insightContentModelService,
            machineLearningApiProvider = machineLearningApiProviders.first(),
            topicConfig = topicConfig,
        )
    }

    val insightTopicsService by lazy {
        DecisionInsightTopicsService(
            clusterInsightTopicsService = clusterInsightTopicsService,
        )
    }

    val insightRefreshService by lazy {
        InsightRefreshService()
    }

    val topicMappingService by lazy {
        DecisionTopicMappingService(
            delegate = StandardTopicMappingService(
                topicMappingRuleService = topicMappingRuleService,
                insightTopicsService = insightTopicsService,
                insightRefreshService = insightRefreshService,
            ),
        )
    }

    val slackChannelFilterService by lazy {
        SlackChannelFilterService(
            clientConfigService = clientConfigService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val slackQAValidationNotifier by lazy {
        SlackQAValidationNotifier(
            slackApiProvider = slackApiProvider,
            slackConfig = config.providers.slack,
            slackChannelFilterService = slackChannelFilterService,
        )
    }

    val mermaidLinksDecoratorService by lazy {
        MermaidLinksDecoratorService(
            authenticationSecret = config.mermaid.hmacSecret,
            mermaidLinksDecoratorDelegate = MermaidDelegateViaRpc(),
        )
    }

    val slackChannelPreferencesResolverService by lazy {
        SlackChannelPreferencesResolverService(
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val slackBotQuestionService by lazy {
        SlackBotQuestionService(
            inferenceService = inferenceService,
            semanticSearchQueryService = semanticSearchQueryService,
            slackNotifier = slackNotifier,
            templateService = inferenceTemplateService,
            topicMappingService = topicMappingService,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            slackQAValidationNotifier = slackQAValidationNotifier,
            slackQAHistoricalMessageLimit = config.search.slackQAHistoricalMessageLimit,
            historicalMessageSizeLimit = config.search.historicalMessageSizeLimit,
            maxConversationHistoryToPromptRatio = config.search.maxConversationHistoryToPromptRatio,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
            slackChannelPreferencesResolverService = slackChannelPreferencesResolverService,
        )
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val peerInviteSuggestionService by lazy {
        PeerInviteSuggestionService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val inactiveFollowupService by lazy {
        InactiveFollowupService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val metricsService: MetricsService by lazy {
        MetricsService(
            peerInviteSuggestionService = peerInviteSuggestionService,
            inactiveFollowupService = inactiveFollowupService,
        )
    }

    val messageService by lazy {
        MessageService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = botAccountService,
        )
    }

    val threadService by lazy {
        ThreadService(
            messageService = messageService,
            metricsService = metricsService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            botAccountService = null,
        )
    }

    val threadUnreadService by lazy {
        ThreadUnreadServiceImpl()
    }

    val threadParticipantService by lazy {
        ThreadParticipantService(
            threadUnreadService = threadUnreadService,
        )
    }

    val messageMentionService by lazy {
        MessageMentionService()
    }

    val slackMessageRelevancyVerifier by lazy {
        CompositeSlackMessageRelevancyVerifier(
            slackRelevancyVerifiers = listOf(
                SlackNonBotMessageVerifier(),
                SlackMessageRepliesVerifier(),
            ),
        )
    }

    val slackActionResponseService by lazy {
        SlackActionResponseService()
    }

    val slackThreadRelevancyVerifier by lazy {
        CompositeSlackThreadRelevancyVerifier(
            slackRelevancyVerifiers = listOf(
                SlackThreadSizeVerifier(),
                SlackThreadDistinctUserVerifier(),
            ),
        )
    }

    val slackMemberModelService by lazy {
        SlackMemberModelService()
    }

    val slackMemberResolutionService by lazy {
        SlackMemberResolutionService(
            slackTokenService = slackTokenService,
            slackMemberModelService = slackMemberModelService,
            slackApiProvider = slackApiProvider,
        )
    }

    val slackMessageUrlService by lazy {
        SlackMessageUrlService(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
        )
    }

    val markdownToMessageBodyService by lazy {
        MarkdownToMessageBodyService(
            markdownPreProcessorService = CompositeMarkdownPreProcessorService(
                listOf(EmojiMarkdownPreProcessorService(), SlackMarkdownPreProcessorService()),
            ),
            markdownMentionResolutionService = SlackMarkdownMentionResolutionService(
                slackMemberResolutionService = slackMemberResolutionService,
            ),
        )
    }

    val slackSnippetFileContentExtractor by lazy {
        SlackSnippetFileContentExtractor(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
        )
    }

    val slackMessageMarkdownService by lazy {
        CompositeSlackMessageMarkdownService(
            slackMessageMarkdownServices = listOf(
                BotSlackMessageMarkdownService(
                    blockIds = listOf(
                        SlackBotSemanticSearchPayload.SEMANTIC_SEARCH_QUESTION_BLOCK_ID,
                        SlackBotSemanticSearchPayload.SEMANTIC_SEARCH_ANSWER_BLOCK_ID,
                    ),
                ),
                StandardSlackMessageMarkdownService(
                    slackSnippetFileContentExtractor = slackSnippetFileContentExtractor,
                ),
            ),
        )
    }

    val slackMessageBodyService by lazy {
        SlackMessageBodyService(
            markdownToMessageBodyService = markdownToMessageBodyService,
            slackMessageMarkdownService = slackMessageMarkdownService,
        )
    }

    val slackMessageModelService by lazy {
        SlackMessageModelService(
            slackMessageBodyService = slackMessageBodyService,
            slackMemberResolutionService = slackMemberResolutionService,
            slackMessageUrlService = slackMessageUrlService,
            threadParticipantService = threadParticipantService,
            messageMentionService = messageMentionService,
        )
    }

    val slackPullRequestResolutionService by lazy {
        SlackPullRequestResolutionService()
    }

    val slackThreadFinalizationService by lazy {
        SlackThreadFinalizationService(
            threadService = threadService,
            threadUnreadService = threadUnreadService,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            summarizationEventEnqueueService = summarizationEventEnqueueService,
        )
    }

    val slackThreadModelService by lazy {
        SlackThreadModelService(
            threadParticipantService = threadParticipantService,
            slackMessageModelService = slackMessageModelService,
            slackMemberResolutionService = slackMemberResolutionService,
            slackMessageMarkdownService = slackMessageMarkdownService,
        )
    }

    val dataEventEnqueueService by lazy {
        DataEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.dataEventsQueueName,
                ),
            ),
        )
    }

    val slackPullRequestRelevancyVerifier by lazy {
        SlackRequiredPullRequestsRelevancyVerifier()
    }

    val slackChannelMessageAnalysisService by lazy {
        SlackChannelMessageAnalysisService(
            slackThreadModelService = slackThreadModelService,
            slackPullRequestResolutionService = slackPullRequestResolutionService,
            slackThreadRelevancyVerifier = slackThreadRelevancyVerifier,
            slackMessageRelevancyVerifier = slackMessageRelevancyVerifier,
            slackPullRequestRelevancyVerifier = slackPullRequestRelevancyVerifier,
        )
    }

    val slackWideThreadIngestionEnablementService by lazy {
        SlackWideThreadIngestionEnablementService()
    }

    val slackChannelMessageIngestionService by lazy {
        SlackChannelMessageIngestionService(
            slackApiProvider = slackApiProvider,
            slackMessageModelService = slackMessageModelService,
            slackThreadModelService = slackThreadModelService,
            slackThreadFinalizationService = slackThreadFinalizationService,
            slackTokenService = slackTokenService,
            dataEventEnqueueService = dataEventEnqueueService,
            slackChannelMessageAnalysisService = slackChannelMessageAnalysisService,
            summarizationEventEnqueueService = summarizationEventEnqueueService,
            slackWideThreadIngestionEnablementService = slackWideThreadIngestionEnablementService,
        )
    }

    val slackMessageEventIngestionService by lazy {
        SlackMessageEventIngestionService(
            slackMessageModelService = slackMessageModelService,
            slackChannelMessageIngestionService = slackChannelMessageIngestionService,
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackChannelFilterService = slackChannelFilterService,
        )
    }

    val slackEventDeletionService by lazy {
        SlackMessageEventDeletionService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackMessageChangedEventService by lazy {
        SlackMessageChangedEventService(
            slackMessageEventIngestionService = slackMessageEventIngestionService,
        )
    }

    val slackMessageDeletedEventService by lazy {
        SlackMessageDeletedEventService(
            slackMessageEventDeletionService = slackEventDeletionService,
        )
    }

    val slackEventEnqueueService by lazy {
        SlackEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackEventsQueueName,
                ),
            ),
        )
    }

    val slackBotEventEnqueueService by lazy {
        SlackBotEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackBotEventsQueueName,
                ),
            ),
        )
    }

    val isQuestionCompletionService by lazy {
        IsQuestionCompletionService(
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
        )
    }

    val answerEvaluationService by lazy {
        AnswerEvaluationService(
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
        )
    }

    val slackBotSemanticSearchSettingsService by lazy {
        SlackBotSemanticSearchSettingsService(
            clientConfigService = clientConfigService,
            slackChannelPreferencesResolverService = slackChannelPreferencesResolverService,
        )
    }

    val slackBotSemanticInputFilter by lazy {
        CompositeSlackBotSemanticInputFilter(
            slackBotSemanticInputFilterServices = listOf(
                SlackBotSemanticInputTextFilter(),
                SlackBotSemanticInputMentionFilter(),
                SlackBotSemanticInputThreadFilter(),
                SlackBotSemanticInputQuestionFilter(),
            ),
        )
    }

    val slackUserEngagementService by lazy {
        SlackUserEngagementService(
            metricsService = metricsService,
        )
    }

    val messageReferenceLinkResolver by lazy {
        MessageReferenceLinkResolver()
    }

    val messageSuggestionsService by lazy {
        MessageSuggestionsService(
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
        )
    }

    val slackBotQuestionTransformer by lazy {
        CompositeSlackBotQuestionTransformer(
            slackBotQuestionTransformers = listOf(
                UnblockedBotAtMentionRemovalTransformer(),
                UserAtMentionUsernameHydrationTransformer(),
            ),
        )
    }

    val billingPlanService by lazy {
        BillingPlanService(
            orgBillingStore = Stores.orgBillingStore,
        )
    }

    val isTrialExpiredFilter by lazy {
        IsTrialExpiredFilter(
            billingPlanService = billingPlanService,
        )
    }

    val isSlackPrivateScopesDisabledFilter by lazy {
        IsSlackPrivateScopesDisabledFilter(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val isInvalidHumanQueryLengthFilter by lazy {
        IsInvalidHumanQueryLengthFilter()
    }

    val shortCircuitBotInputFilter by lazy {
        CompositeShortCircuitBotInputFilter(
            filters = listOf(
                isTrialExpiredFilter,
                isInvalidHumanQueryLengthFilter,
                isSlackPrivateScopesDisabledFilter,
            ),
        )
    }

    val slackUserConnectService by lazy {
        SlackUserConnectService(
            slackNotifier = slackNotifier,
        )
    }
    val inviteRedisStore by lazy {
        InviteRedisStore()
    }

    val slackAccountInviteUrlBuilder by lazy {
        SlackAccountInviteUrlBuilder(
            inviteRedisStore = inviteRedisStore,
        )
    }

    val productFeedbackService by lazy {
        ProductFeedbackService(slackNotifier = slackNotifier)
    }

    val slackDeepLinkService by lazy {
        SlackDeepLinkService()
    }

    val slackUserQuestionsCountResolver by lazy {
        SlackUserQuestionsCountResolver()
    }

    val slackBotShortCircuitMemberNotificationService by lazy {
        CompositeSlackBotMemberNotificationService(
            slackBotMemberNotificationServices = listOf(
                SlackBotAtMentionRequestLicenseForLinkedAccountMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                    slackUserQuestionsCountResolver = slackUserQuestionsCountResolver,
                ),
                SlackBotAtMentionRequestLicenseForUnlinkedAccountMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackUserConnectService = slackUserConnectService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                    slackUserQuestionsCountResolver = slackUserQuestionsCountResolver,
                ),
                SlackBotAtMentionForceConnectMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackUserConnectService = slackUserConnectService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotHumanQueryOverLimitMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    isInvalidHumanQueryLengthFilter = isInvalidHumanQueryLengthFilter,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotExpiredTrialMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    isTrialExpiredFilter = isTrialExpiredFilter,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotIngestionPendingMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotPrivateScopesDisabledMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    isSlackPrivateScopesDisabledFilter = isSlackPrivateScopesDisabledFilter,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAtMentionProductFeedbackNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    productFeedbackService = productFeedbackService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
            ),
        )
    }

    val slackBotMemberContextResolver by lazy {
        SlackBotMemberContextResolver(
            slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
            slackDeepLinkService = slackDeepLinkService,
        )
    }

    val slackBotAtMentionLinkAccountsPayloadGenerator by lazy {
        SlackBotAtMentionLinkAccountsPayloadGenerator(
            slackBotMemberContextResolver = slackBotMemberContextResolver,
        )
    }

    val slackBotAtMentionMemberNotificationService by lazy {
        CompositeSlackBotMemberNotificationService(
            slackBotMemberNotificationServices = listOf(
                SlackBotAtMentionForceConnectWarningMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackUserConnectService = slackUserConnectService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAtMentionRequestLicenseForLinkedAccountWarningMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackUserQuestionsCountResolver = slackUserQuestionsCountResolver,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAtMentionLinkAccountsMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackBotAtMentionLinkAccountsPayloadGenerator = slackBotAtMentionLinkAccountsPayloadGenerator,
                    slackBotMemberContextResolver = slackBotMemberContextResolver,
                    slackDeepLinkService = slackDeepLinkService,
                ),
            ),
        )
    }

    val slackChannelEventIngestionService by lazy {
        SlackChannelEventIngestionService(
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackEventEnqueueService = slackEventEnqueueService,
        )
    }

    val slackWebhookEventProcessorContextResolver by lazy {
        SlackWebhookEventProcessorContextResolver(
            slackChannelEventIngestionService = slackChannelEventIngestionService,
            slackMemberResolutionService = slackMemberResolutionService,
        )
    }

    val slackBotAutoAnswerWebhookEventProcessor by lazy {
        SlackBotAutoAnswerWebhookEventProcessor(
            slackBotSemanticSearchSettingsService = slackBotSemanticSearchSettingsService,
            slackBotEventEnqueueService = slackBotEventEnqueueService,
            slackWebhookEventProcessorContextResolver = slackWebhookEventProcessorContextResolver,
        )
    }

    val slackBotAtMentionWebhookEventProcessor by lazy {
        SlackBotAtMentionWebhookEventProcessor(
            slackBotEventEnqueueService = slackBotEventEnqueueService,
            slackWebhookEventProcessorContextResolver = slackWebhookEventProcessorContextResolver,
            slackMemberResolutionService = slackMemberResolutionService,
            slackBotSemanticSearchSettingsService = slackBotSemanticSearchSettingsService,
        )
    }

    val slackContextRetriever by lazy {
        SlackContextRetriever()
    }

    val slackBotAnswerDsacService by lazy {
        SlackBotAnswerDsacService()
    }

    val slackBotAnswerProcessor by lazy {
        SlackBotAnswerProcessor(
            messageSuggestionsService = messageSuggestionsService,
            slackBotQuestionService = slackBotQuestionService,
        )
    }

    val slackBotEventProcessor by lazy {
        SlackBotEventProcessor(
            slackUserEngagementService = slackUserEngagementService,
        )
    }

    val slackBotSlackMessageResolver by lazy {
        SlackBotSlackMessageResolver(
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
        )
    }

    val slackMemberContextRetriever by lazy {
        SlackMemberContextRetriever(
            slackMemberResolutionService = slackMemberResolutionService,
        )
    }

    val slackBotPayloadContextRetriever by lazy {
        SlackBotPayloadContextRetriever(
            slackBotSemanticSearchSettingsService = slackBotSemanticSearchSettingsService,
        )
    }

    val slackBotContextRetriever by lazy {
        SlackBotContextRetriever(
            slackBotSlackMessageResolver = slackBotSlackMessageResolver,
            slackBotQuestionTransformer = slackBotQuestionTransformer,
            slackMemberContextRetriever = slackMemberContextRetriever,
            slackBotPayloadContextRetriever = slackBotPayloadContextRetriever,
        )
    }

    val slackBotNotifier by lazy {
        SlackBotNotifier(
            slackNotifier = slackNotifier,
            slackQAValidationNotifier = slackQAValidationNotifier,
        )
    }

    val slackBotThreadManager by lazy {
        SlackBotThreadManager(
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            topicMappingService = topicMappingService,
            slackThreadFinalizationService = slackThreadFinalizationService,
            slackChannelMessageIngestionService = slackChannelMessageIngestionService,
            slackMessageModelService = slackMessageModelService,
        )
    }

    val billingEventEnqueueService by lazy {
        BillingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.billingEventsQueueName,
                ),
            ),
        )
    }

    val slackBotAnswerTextBlockConverter by lazy {
        SlackBotAnswerTextBlockConverter(
            mermaidLinksDecoratorService = mermaidLinksDecoratorService,
        )
    }

    val slackBotAtMentionNotificationService by lazy {
        SlackBotAtMentionNotificationService(
            messageReferenceLinkResolver = messageReferenceLinkResolver,
            scmWebFactory = scmWebFactory,
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackBotAtMentionMemberNotificationService = slackBotAtMentionMemberNotificationService,
            slackBotThreadManager = slackBotThreadManager,
            slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
            slackBotAtMentionLinkAccountsPayloadGenerator = slackBotAtMentionLinkAccountsPayloadGenerator,
            slackBotAnswerDsacService = slackBotAnswerDsacService,
        )
    }

    val slackBotAtMentionSemanticSearchService by lazy {
        SlackBotAtMentionSemanticSearchService(
            slackApiProvider = slackApiProvider,
            slackContextRetriever = slackContextRetriever,
            slackTokenService = slackTokenService,
            slackBotAtMentionNotificationService = slackBotAtMentionNotificationService,
            slackBotShortCircuitMemberNotificationService = slackBotShortCircuitMemberNotificationService,
            slackBotNotifier = slackBotNotifier,
            slackBotThreadManager = slackBotThreadManager,
            slackBotEventProcessor = slackBotEventProcessor,
            slackBotAnswerProcessor = slackBotAnswerProcessor,
            slackBotContextRetriever = slackBotContextRetriever,
            billingEventEnqueueService = billingEventEnqueueService,
        )
    }

    val slackBotDisclaimerSettingsService by lazy {
        SlackBotDisclaimerSettingsService()
    }

    val slackChannelJoinService by lazy {
        SlackChannelJoinService(
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackChannelFilterService = slackChannelFilterService,
        )
    }

    val slackChannelLeaveService by lazy {
        SlackChannelLeaveService(
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackChannelFilterService = slackChannelFilterService,
        )
    }

    val slackBotChannelsJoinService by lazy {
        SlackBotChannelsJoinService(
            slackChannelJoinService = slackChannelJoinService,
        )
    }

    val slackBotChannelsLeaveService by lazy {
        SlackBotChannelsLeaveService(
            slackChannelLeaveService = slackChannelLeaveService,
        )
    }

    val slackBotAutoAnswerMemberNotificationService by lazy {
        CompositeSlackBotMemberNotificationService(
            slackBotMemberNotificationServices = listOf(
                SlackBotAutoAnswerDisconnectedMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackUserConnectService = slackUserConnectService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAutoAnswerConnectedMemberNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
            ),
        )
    }

    val slackBotAutoAnswerStateService by lazy {
        SlackBotAutoAnswerStateService(
            slackMemberResolutionService = slackMemberResolutionService,
        )
    }

    val slackTickleResponderService by lazy {
        TickleResponderService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val slackBotAutoAnswerTickleNotificationService by lazy {
        SlackBotAutoAnswerTickleNotificationService(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
            tickleResponderService = slackTickleResponderService,
            slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
            slackDeepLinkService = slackDeepLinkService,
        )
    }

    val slackBotNeedsAccountPayloadGenerator by lazy {
        SlackBotNeedsAccountPayloadGenerator(
            slackBotMemberContextResolver = slackBotMemberContextResolver,
        )
    }

    val slackBotAutoAnswerNotificationService by lazy {
        SlackBotAutoAnswerNotificationService(
            slackApiProvider = slackApiProvider,
            slackTokenService = slackTokenService,
            slackNotifier = slackNotifier,
            messageReferenceLinkResolver = messageReferenceLinkResolver,
            scmWebFactory = scmWebFactory,
            slackBotAutoAnswerMemberNotificationService = slackBotAutoAnswerMemberNotificationService,
            slackBotThreadManager = slackBotThreadManager,
            slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
            slackBotAnswerDsacService = slackBotAnswerDsacService,
            slackBotNeedsAccountPayloadGenerator = slackBotNeedsAccountPayloadGenerator,
            slackConfig = config.providers.slack,
        )
    }

    val slackBotAutoAnswerSemanticSearchService by lazy {
        SlackBotAutoAnswerSemanticSearchService(
            shortCircuitBotInputFilter = shortCircuitBotInputFilter,
            isQuestionCompletionService = isQuestionCompletionService,
            answerEvaluationService = answerEvaluationService,
            slackContextRetriever = slackContextRetriever,
            slackNotifier = slackNotifier,
            slackBotSemanticInputFilter = slackBotSemanticInputFilter,
            slackBotTickleMemberNotificationService = slackBotAutoAnswerTickleNotificationService,
            slackBotNotifier = slackBotNotifier,
            slackBotEventProcessor = slackBotEventProcessor,
            slackBotAnswerProcessor = slackBotAnswerProcessor,
            slackBotContextRetriever = slackBotContextRetriever,
            slackBotAutoAnswerStateService = slackBotAutoAnswerStateService,
            slackBotAutoAnswerNotificationService = slackBotAutoAnswerNotificationService,
            slackChannelPreferencesResolverService = slackChannelPreferencesResolverService,
        )
    }

    val slackBotAppHomeOpenedWelcomeConnectedNotificationService by lazy {
        CompositeSlackBotMemberNotificationService(
            slackBotMemberNotificationServices = listOf(
                SlackBotAppHomeOpenedWelcomeLicenseRequiredNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAppHomeOpenedPrivateScopesDisabledWelcomeNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    planCapabilitiesService = planCapabilitiesService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAppHomeOpenedWelcomeConnectedNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    planCapabilitiesService = planCapabilitiesService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotAppHomeOpenedWelcomeDisconnectedNotificationService(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    planCapabilitiesService = planCapabilitiesService,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
            ),
        )
    }

    val slackAppHomeOpenedEventService by lazy {
        SlackAppHomeOpenedEventService(
            slackBotAppHomedOpenedWelcomeNotificationService = slackBotAppHomeOpenedWelcomeConnectedNotificationService,
        )
    }

    val slackAppMentionEventService by lazy {
        SlackAppMentionEventService(
            slackBotAtMentionWebhookEventProcessor = slackBotAtMentionWebhookEventProcessor,
        )
    }

    val slackAppUninstalledEventService by lazy {
        SlackAppUninstalledEventService()
    }

    val slackChannelDeletionService by lazy {
        SlackChannelDeletionService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val slackChannelMemberEventDeletionService by lazy {
        SlackChannelMemberEventDeletionService(
            slackChannelDeletionService = slackChannelDeletionService,
            slackMemberResolutionService = slackMemberResolutionService,
        )
    }

    val slackChannelLeftEventService by lazy {
        SlackChannelLeftEventService(
            slackChannelMemberEventDeletionService = slackChannelMemberEventDeletionService,
        )
    }

    val slackChannelCreatedEventService by lazy {
        SlackChannelCreatedEventService(
            slackEventEnqueueService = slackEventEnqueueService,
        )
    }

    val slackChannelDeletedEventService by lazy {
        SlackChannelDeletedEventService(
            slackChannelDeletionService = slackChannelDeletionService,
        )
    }

    val slackChannelArchiveEventService by lazy {
        SlackChannelArchiveEventService()
    }

    val slackChannelUnarchiveEventService by lazy {
        SlackChannelUnarchiveEventService()
    }

    val slackChannelRenameEventService by lazy {
        SlackChannelRenameEventService()
    }

    val slackGroupDeletedEventService by lazy {
        SlackGroupDeletedEventService(
            slackChannelDeletionService = slackChannelDeletionService,
        )
    }

    val slackGroupLeftEventService by lazy {
        SlackGroupLeftEventService(
            slackChannelMemberEventDeletionService = slackChannelMemberEventDeletionService,
        )
    }

    val slackGroupArchiveEventService by lazy {
        SlackGroupArchiveEventService()
    }

    val slackGroupUnarchiveEventService by lazy {
        SlackGroupUnarchiveEventService()
    }

    val slackGroupRenameEventService by lazy {
        SlackGroupRenameEventService()
    }

    val slackFileEmbeddingEventEnqueueService by lazy {
        SlackFileEmbeddingEventEnqueueService(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackFileContentExtractor by lazy {
        StandardSlackFileContentExtractor(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
        )
    }

    val slackFileHandler by lazy {
        CompositeSlackFileHandler(
            slackFileHandlers = listOf(
                SlackJPGFileHandler(),
                SlackPNGFileHandler(),
                SlackPDFFileHandler(
                    slackFileEmbeddingEventEnqueueService = slackFileEmbeddingEventEnqueueService,
                    slackFileContentExtractor = slackFileContentExtractor,
                ),
                SlackCSVFileHandler(
                    slackFileEmbeddingEventEnqueueService = slackFileEmbeddingEventEnqueueService,
                    slackFileContentExtractor = slackFileContentExtractor,
                ),
                SlackDOCXFileHandler(
                    slackFileEmbeddingEventEnqueueService = slackFileEmbeddingEventEnqueueService,
                    slackFileContentExtractor = slackFileContentExtractor,
                ),
                SlackXLSXFileHandler(
                    slackFileEmbeddingEventEnqueueService = slackFileEmbeddingEventEnqueueService,
                    slackFileContentExtractor = slackFileContentExtractor,
                ),
                SlackTextFileHandler(
                    slackFileEmbeddingEventEnqueueService = slackFileEmbeddingEventEnqueueService,
                    slackFileContentExtractor = slackFileContentExtractor,
                ),
            ),
        )
    }

    val slackFileEventIngestionService by lazy {
        SlackFileEventIngestionService(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
            slackFileHandler = slackFileHandler,
        )
    }

    val slackFileChangeEventService by lazy {
        SlackFileChangeEventService(
            slackFileEventIngestionService = slackFileEventIngestionService,
        )
    }

    val slackFileCreatedEventService by lazy {
        SlackFileCreatedEventService(
            slackFileEventIngestionService = slackFileEventIngestionService,
        )
    }

    val slackFileEventDeletionService by lazy {
        SlackFileEventDeletionService(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackFileDeletedEventService by lazy {
        SlackFileDeletedEventService(
            slackFileEventDeletionService = slackFileEventDeletionService,
        )
    }

    val slackBotMemberJoinedDisclaimerNotificationService by lazy {
        CompositeSlackBotMemberNotificationService(
            slackBotMemberNotificationServices = listOf(
                SlackBotMemberJoinedDisclaimerNotificationService(
                    slackBotDisclaimerSettingsService = slackBotDisclaimerSettingsService,
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    isSlackPrivateScopesDisabledFilter = isSlackPrivateScopesDisabledFilter,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
                SlackBotMemberJoinedPrivateScopesDisabledDisclaimerNotificationService(
                    slackBotDisclaimerSettingsService = slackBotDisclaimerSettingsService,
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    isSlackPrivateScopesDisabledFilter = isSlackPrivateScopesDisabledFilter,
                    slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
                    slackDeepLinkService = slackDeepLinkService,
                ),
            ),
        )
    }

    val slackChannelMemberJoinedEventIngestionService by lazy {
        SlackChannelMemberJoinedEventIngestionService(
            slackBotMemberJoinedDisclaimerNotificationService = slackBotMemberJoinedDisclaimerNotificationService,
            slackMemberResolutionService = slackMemberResolutionService,
            slackChannelEventIngestionService = slackChannelEventIngestionService,
        )
    }

    val slackMemberJoinedChannelEventService by lazy {
        SlackMemberJoinedChannelEventService(
            slackChannelMemberJoinedEventIngestionService = slackChannelMemberJoinedEventIngestionService,
        )
    }

    val slackMemberLeftChannelEventService by lazy {
        SlackMemberLeftChannelEventService(
            slackChannelMemberEventDeletionService = slackChannelMemberEventDeletionService,
        )
    }

    val slackMessageFileShareEventService by lazy {
        SlackMessageFileShareEventService(
            slackMessageEventIngestionService = slackMessageEventIngestionService,
            slackBotAutoAnswerWebhookEventProcessor = slackBotAutoAnswerWebhookEventProcessor,
            slackBotAtMentionWebhookEventProcessor = slackBotAtMentionWebhookEventProcessor,
        )
    }

    val slackUserChangeEventService by lazy {
        SlackUserChangeEventService(
            slackMemberModelService = slackMemberModelService,
        )
    }

    val slackMessageEventService by lazy {
        SlackMessageEventService(
            slackMessageEventIngestionService = slackMessageEventIngestionService,
            slackBotAutoAnswerWebhookEventProcessor = slackBotAutoAnswerWebhookEventProcessor,
            slackBotAtMentionWebhookEventProcessor = slackBotAtMentionWebhookEventProcessor,
        )
    }

    val slackMessageInferenceExampleService by lazy {
        SlackMessageInferenceExampleService()
    }

    val slackMessageFeedbackService by lazy {
        SlackMessageFeedbackService(
            messageFeedbackService = messageFeedbackService,
            slackMemberResolutionService = slackMemberResolutionService,
            slackMessageInferenceExampleService = slackMessageInferenceExampleService,
            slackNotifier = slackNotifier,
            slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
        )
    }

    val slackWebhookEventEnqueueService by lazy {
        StandardEventEnqueueService(
            messageProducer = ActiveMQProducer.producer(
                queueName = config.queue.hooksSlackQueueName,
            ),
        )
    }

    val defaultSlackBotChannelSettingsViewBuilder by lazy {
        DefaultSlackBotChannelSettingsViewBuilder(clientConfigService = clientConfigService)
    }

    val slackBotChannelSettingsUpsellViewBuilder by lazy {
        SlackBotChannelSettingsUpsellViewBuilder()
    }

    val pendingSlackQuestionService by lazy {
        PendingSlackQuestionService(
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        )
    }

    val slackBotChannelSettingsCommandHelpNotificationService by lazy {
        SlackBotChannelSettingsCommandHelpNotificationService(
            slackAccountInviteUrlBuilder = slackAccountInviteUrlBuilder,
            slackActionResponseService = slackActionResponseService,
            slackDeepLinkService = slackDeepLinkService,
        )
    }

    val slackBotSlashCommandEventHandler by lazy {
        CompositeTypedEventHandler(
            handlers = listOf(
                SlackBotChannelSettingsEventHandler(
                    slackApiProvider = slackApiProvider,
                    slackTokenService = slackTokenService,
                    defaultSlackBotChannelSettingsViewBuilder = defaultSlackBotChannelSettingsViewBuilder,
                    slackBotChannelSettingsUpsellViewBuilder = slackBotChannelSettingsUpsellViewBuilder,
                    slackMemberResolutionService = slackMemberResolutionService,
                    planCapabilitiesService = planCapabilitiesService,
                ),
                SlackBotChannelSettingsHelpEventHandler(
                    slackBotChannelSettingsCommandHelpNotificationService = slackBotChannelSettingsCommandHelpNotificationService,
                ),
            ),
        )
    }

    val slackBotBlockActionEventHandler by lazy {
        CompositeTypedEventHandler(
            handlers = listOf(
                SlackBotSemanticSearchFeedbackButtonEventHandler(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackContextRetriever = slackContextRetriever,
                    slackMessageFeedbackService = slackMessageFeedbackService,
                    slackMessageInferenceExampleService = slackMessageInferenceExampleService,
                    scmWebFactory = scmWebFactory,
                    messageReferenceLinkResolver = messageReferenceLinkResolver,
                    slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
                ),
                SlackBotSemanticSearchSuggestionButtonEventHandler(
                    slackBotEventEnqueueService = slackBotEventEnqueueService,
                ),
                SlackBotSemanticSearchApprovalButtonEventHandler(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackMessageInferenceExampleService = slackMessageInferenceExampleService,
                    scmWebFactory = scmWebFactory,
                    messageReferenceLinkResolver = messageReferenceLinkResolver,
                    slackActionResponseService = slackActionResponseService,
                    slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
                    slackBotSemanticSearchSettingsService = slackBotSemanticSearchSettingsService,
                ),
                SlackBotAutoAnswerApprovalButtonEventHandler(
                    slackBotAutoAnswerNotificationService = slackBotAutoAnswerNotificationService,
                    slackMemberContextRetriever = slackMemberContextRetriever,
                    slackBotPayloadContextRetriever = slackBotPayloadContextRetriever,
                ),
                SlackBotSemanticSearchCreateAccountButtonEventHandler(),
                SlackBotChannelSettingsDataSourceSelectEventHandler(
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    defaultSlackBotChannelSettingsViewBuilder = defaultSlackBotChannelSettingsViewBuilder,
                    slackMemberResolutionService = slackMemberResolutionService,
                ),
                SlackBotProductFeedbackButtonEventHandler(
                    productFeedbackService = productFeedbackService,
                    slackMemberResolutionService = slackMemberResolutionService,
                    slackActionResponseService = slackActionResponseService,
                    pendingSlackQuestionService = pendingSlackQuestionService,
                ),
            ),
        )
    }

    val slackBotViewSubmissionEventHandler by lazy {
        CompositeTypedEventHandler(
            handlers = listOf(
                SlackBotSemanticSearchViewSubmissionEventHandler(
                    slackMessageFeedbackService = slackMessageFeedbackService,
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                    slackMessageInferenceExampleService = slackMessageInferenceExampleService,
                    scmWebFactory = scmWebFactory,
                    messageReferenceLinkResolver = messageReferenceLinkResolver,
                    slackBotAnswerTextBlockConverter = slackBotAnswerTextBlockConverter,
                ),
                SlackBotChannelSettingsViewSubmissionEventHandler(
                    slackMemberResolutionService = slackMemberResolutionService,
                    slackTokenService = slackTokenService,
                    slackApiProvider = slackApiProvider,
                ),
            ),
        )
    }

    val slackBotClearEphemeralMessageEventHandler by lazy {
        SlackBotClearEphemeralMessageEventHandler(
            slackActionResponseService = slackActionResponseService,
        )
    }

    val slackBotAppHomePendingQuestionsContinuationNotificationService by lazy {
        SlackBotAppHomePendingQuestionsContinuationNotificationService(
            slackTokenService = slackTokenService,
            slackApiProvider = slackApiProvider,
        )
    }

    val slackNotifyPendingQuestionsService by lazy {
        SlackNotifyPendingQuestionsService(
            slackBotAppHomePendingQuestionsContinuationNotificationService = slackBotAppHomePendingQuestionsContinuationNotificationService,
        )
    }

    val slackBotPendingQuestionsNotificationEventHandler by lazy {
        SlackBotPendingQuestionNotificationsEventHandler(
            slackNotifyPendingQuestionsService = slackNotifyPendingQuestionsService,
        )
    }

    val slackBotEventHandler = SlackBotEventHandler(
        slackBotAutoAnswerSemanticSearchEventHandler = SlackBotAutoAnswerSemanticSearchEventHandler(
            slackBotAutoAnswerSemanticSearchService = slackBotAutoAnswerSemanticSearchService,
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        ),
        slackBotAtMentionSemanticSearchEventHandler = SlackBotAtMentionSemanticSearchEventHandler(
            slackBotAtMentionSemanticSearchService = slackBotAtMentionSemanticSearchService,
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        ),
        slackBotChannelsLeaveEventHandler = SlackBotChannelsLeaveEventHandler(
            slackBotChannelsLeaveService = slackBotChannelsLeaveService,
        ),
        slackBotChannelsJoinEventHandler = SlackBotChannelsJoinEventHandler(
            slackBotChannelsJoinService = slackBotChannelsJoinService,
        ),
        slackBotClearEphemeralMessageEventHandler = slackBotClearEphemeralMessageEventHandler,
        slackBotPendingQuestionNotificationsEventHandler = slackBotPendingQuestionsNotificationEventHandler,
    )

    val slackWebhookEventHandler = SlackWebhookEventHandler(
        slackWebhookApiEventHandler = CompositeTypedEventHandler(
            handlers = listOf(
                AppHomeOpenedEventHandler(
                    slackAppHomeOpenedEventService = slackAppHomeOpenedEventService,
                ),
                AppMentionEventHandler(
                    slackAppMentionEventService = slackAppMentionEventService,
                ),
                AppUninstalledEventHandler(
                    slackAppUninstalledEventService = slackAppUninstalledEventService,
                ),
                ChannelArchiveEventHandler(
                    slackChannelArchiveEventService = slackChannelArchiveEventService,
                ),
                ChannelUnarchiveEventHandler(
                    slackChannelUnarchiveEventService = slackChannelUnarchiveEventService,
                ),
                ChannelCreatedEventHandler(
                    slackChannelCreatedEventService = slackChannelCreatedEventService,
                ),
                ChannelLeftEventHandler(
                    slackChannelLeftEventService = slackChannelLeftEventService,
                ),
                ChannelDeletedEventHandler(
                    slackChannelDeletedEventService = slackChannelDeletedEventService,
                ),
                ChannelRenameEventHandler(
                    slackChannelRenameEventService = slackChannelRenameEventService,
                ),
                FileChangeEventHandler(
                    slackFileChangeEventService = slackFileChangeEventService,
                ),
                FileCreatedEventHandler(
                    slackFileCreatedEventService = slackFileCreatedEventService,
                ),
                FileDeletedEventHandler(
                    slackFileDeletedEventService = slackFileDeletedEventService,
                ),
                GroupArchiveEventHandler(
                    slackGroupArchiveEventService = slackGroupArchiveEventService,
                ),
                GroupUnarchiveEventHandler(
                    slackGroupUnarchiveEventService = slackGroupUnarchiveEventService,
                ),
                GroupDeletedEventHandler(
                    slackGroupDeletedEventService = slackGroupDeletedEventService,
                ),
                GroupLeftEventHandler(
                    slackGroupLeftEventService = slackGroupLeftEventService,
                ),
                GroupRenameEventHandler(
                    slackGroupRenameEventService = slackGroupRenameEventService,
                ),
                MemberJoinedChannelEventHandler(
                    slackMemberJoinedChannelEventService = slackMemberJoinedChannelEventService,
                ),
                MemberLeftChannelEventHandler(
                    slackMemberLeftChannelEventService = slackMemberLeftChannelEventService,
                ),
                MessageChangedEventHandler(
                    slackMessageChangedEventService = slackMessageChangedEventService,
                ),
                MessageDeletedEventHandler(
                    slackMessageDeletedEventService = slackMessageDeletedEventService,
                ),
                MessageBotEventHandler(
                    slackMessageEventService = slackMessageEventService,
                ),
                MessageEventHandler(
                    slackMessageEventService = slackMessageEventService,
                ),
                MessageFileShareEventHandler(
                    slackMessageFileShareEventService = slackMessageFileShareEventService,
                ),
                UserChangeEventHandler(
                    slackUserChangeEventService = slackUserChangeEventService,
                ),
            ),
        ),
        blockActionEventHandler = BlockActionEventHandler(
            slackBotBlockActionEventHandler = slackBotBlockActionEventHandler,
        ),
        viewSubmissionEventHandler = ViewSubmissionEventHandler(
            slackBotViewSubmissionEventHandler = slackBotViewSubmissionEventHandler,
        ),
        slackCommandEventHandler = SlashCommandEventHandler(
            slackBotSlashCommandEventHandler = slackBotSlashCommandEventHandler,
        ),
    )

    configureOpenSearch(
        lockProvider = openSearchLockProvider,
        config = config,
        openSearchIndexLoader = openSearchIndexLoader,
        openSearchConfig = openSearchConfig,
    )
    configurePinecone(
        lockProvider = pineconeLockProvider,
        pineconeConfig = pineconeConfig,
        pineconeIndexLoader = pineconeIndexLoader,
    )
    configureRouting(serviceLifecycle = serviceLifecycle)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureJvmMetrics()
    configureBackgroundJobs(
        jobs = buildList {
            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = SlackWebhookProcessingJob(
                            jobTimeout = 60.seconds,
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.hooksSlackQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = SlackWebhookEventMessageProcessor(
                                    handler = slackWebhookEventHandler,
                                    eventEnqueueService = slackWebhookEventEnqueueService,
                                ),
                            ),
                        ),
                    ).also {
                        ShutdownHookManager.registerShutdownHook(it)
                    },
                )
            }

            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = SlackBotEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.slackBotEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(slackBotEventHandler),
                            ),
                        ),
                    ).also {
                        ShutdownHookManager.registerShutdownHook(it)
                    },
                )
            }

            // Realtime consumer for high-priority events
            repeat(PRIORITY_CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 0.milliseconds,
                        job = SlackWebhookProcessingJob(
                            jobTimeout = 60.seconds,
                            eventDequeueService = RealtimeEventDequeue(
                                waitTimeout = 5.seconds,
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.hooksSlackQueueName,
                                    transacted = true,
                                    messageSelector = "JMSPriority = 9",
                                ),
                                eventMessageProcessor = SlackWebhookEventMessageProcessor(
                                    handler = slackWebhookEventHandler,
                                    eventEnqueueService = slackWebhookEventEnqueueService,
                                ),
                            ),
                        ),
                    ).also {
                        ShutdownHookManager.registerShutdownHook(it)
                    },
                )
            }

            // Realtime consumer for high-priority events
            repeat(PRIORITY_CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 0.milliseconds,
                        job = SlackBotEventJob(
                            eventDequeueService = RealtimeEventDequeue(
                                waitTimeout = 5.seconds,
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.slackBotEventsQueueName,
                                    transacted = true,
                                    messageSelector = "JMSPriority = 9",
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(slackBotEventHandler),
                            ),
                        ),
                    ).also {
                        ShutdownHookManager.registerShutdownHook(it)
                    },
                )
            }
        },
    )
}
