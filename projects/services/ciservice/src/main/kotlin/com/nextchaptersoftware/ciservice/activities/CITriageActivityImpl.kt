package com.nextchaptersoftware.ciservice.activities

import com.nextchaptersoftware.ci.CILicenseService
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.events.EventPublisher
import com.nextchaptersoftware.ci.workflows.CITriageWorkflow.TriageRequest
import com.nextchaptersoftware.db.models.BuildTriageExecutionId
import com.nextchaptersoftware.db.models.BuildTriageExecutionState
import com.nextchaptersoftware.db.stores.Stores.buildStore
import com.nextchaptersoftware.db.stores.Stores.buildTriageExecutionStore
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.debugSync
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.traceSync
import com.nextchaptersoftware.temporal.kotlin.ActivityKt.suspendedActivity
import com.nextchaptersoftware.utils.KotlinUtils.required

private val LOGGER = mu.KotlinLogging.logger {}

class CITriageActivityImpl(
    private val ciLicenseService: CILicenseService,
    private val ciTriageController: CITriageController,
) : CITriageActivity {

    @Suppress(
        "LongMethod",
        "CyclomaticComplexMethod",
    )
    override fun validate(
        event: TriageRequest,
    ): TriageRequestResult? = suspendedActivity fn@{
        val pullRequest = pullRequestStore.findById(prId = event.pullRequestId).required { "Missing Pull Request" }
        if (pullRequest.isTriageMuted && event.options?.disablePullRequestMuteCheck != true) {
            LOGGER.traceSync { "Pull Request is Muted" }
            return@fn null
        }

        buildStore.isLatestBuild(
            pullRequestId = event.pullRequestId,
            buildId = event.buildId,
        )
            .also { isLatest ->
                if (!isLatest) {
                    LOGGER.traceSync { "Build is not latest" }
                    return@fn null
                }
            }

        val scmTeam = scmTeamStore.findById(teamId = event.scmTeamId) ?: run {
            LOGGER.traceSync { "Scm Team not found" }
            return@fn null
        }

        val repo = pullRequest.repo() ?: run {
            LOGGER.traceSync { "Repo not found" }
            return@fn null
        }

        val build = buildStore.findById(buildId = event.buildId) ?: run {
            LOGGER.traceSync { "Build not found" }
            return@fn null
        }

        val jobIds = build.jobsLatestRun()
            ?.map { it.id }
            ?: run {
                LOGGER.traceSync { "Build does not have any failures" }
                return@fn null
            }

        if (event.options?.disableTriageExistsCheck != true) {
            buildTriageStore.findByBuild(
                buildId = build.id,
                jobIds = jobIds,
                limit = 1,
            )
                ?.also { triage ->
                    LOGGER.debugSync(
                        "triageId" to triage.id,
                    ) { "Triage already exists" }
                    return@fn null
                }
        }

        if (!ciTriageController.ciEnabled(
                orgId = event.orgId,
                orgMemberId = pullRequest.creatorOrgMemberId,
                ciInstallationId = build.ciInstallationId,
                scmInstallationId = scmTeam.installationId,
                repo = repo,
            )
        ) {
            LOGGER.debugAsync { "Not enabled" }
            return@fn null
        }

        val hasLicense = ciLicenseService.hasLicense(orgId = scmTeam.orgId, orgMemberId = pullRequest.creatorOrgMemberId)
        if (!hasLicense) {
            val isLicenseRequired = ciLicenseService.requiresLicense(
                orgId = scmTeam.orgId,
                orgMemberId = pullRequest.creatorOrgMemberId,
                pullRequestId = pullRequest.id,
            )

            if (isLicenseRequired) {
                LOGGER.debugAsync(
                    "orgId" to scmTeam.orgId,
                    "orgMemberId" to pullRequest.creatorOrgMemberId,
                    "check.hasLicense" to hasLicense,
                    "check.isLicenseRequired" to isLicenseRequired,
                    "check.notifies" to "true",
                ) { "LicenseCheckEvent" }
                ciLicenseService.notifyLicenseRequired(orgId = scmTeam.orgId, orgMemberId = pullRequest.creatorOrgMemberId)
                return@fn null
            } else {
                LOGGER.debugAsync(
                    "orgId" to scmTeam.orgId,
                    "orgMemberId" to pullRequest.creatorOrgMemberId,
                    "check.hasLicense" to hasLicense,
                    "check.isLicenseRequired" to isLicenseRequired,
                    "check.notifies" to "false",
                ) { "LicenseCheckEvent" }
            }
        } else {
            LOGGER.debugAsync(
                "orgId" to scmTeam.orgId,
                "orgMemberId" to pullRequest.creatorOrgMemberId,
                "check.hasLicense" to "true",
            ) { "LicenseCheckEvent" }
        }

        LOGGER.traceAsync { "Triaging build" }

        val execution = buildTriageExecutionStore.insert(
            orgId = event.orgId,
            ciInstallationId = event.ciInstallationId,
            scmTeamId = event.scmTeamId,
            repoId = event.repoId,
            pullRequestId = event.pullRequestId,
            pullRequestCreatorId = pullRequest.creatorOrgMemberId,
            buildId = event.buildId,
            buildJobsIds = jobIds,
        )

        EventPublisher.publish(
            type = "TriageRequestEvent",
            "triageExecutionId" to execution.id,
            "pipeline" to "temporal",
        )

        val previousBuildIds = buildTriageExecutionStore.findPreviousBy(
            pullRequestId = event.pullRequestId,
            currentBuildId = build.id,
            currentBuildGroupKey = build.groupKey,
        )
            ?.map { it.buildId }
            ?.distinct()

        return@fn TriageRequestResult(
            executionId = execution.id,
            jobIds = jobIds,
            pullRequestCreatorId = pullRequest.creatorOrgMemberId,
            previousBuildIds = previousBuildIds,
        )
    }

    override fun markCancelled(
        executionId: BuildTriageExecutionId,
    ): Unit = suspendedActivity {
        buildTriageExecutionStore.update(
            executionId = executionId,
            state = BuildTriageExecutionState.Cancelled,
        )
    }
}
