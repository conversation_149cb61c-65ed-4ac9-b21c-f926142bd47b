package com.nextchaptersoftware.ciservice.activities

import com.nextchaptersoftware.ci.triage.DocumentResult
import com.nextchaptersoftware.ci.triage.TriageEvalResult
import com.nextchaptersoftware.ci.triage.TriageModelResult
import com.nextchaptersoftware.ci.triage.pipeline.StepPullRequestMeta.PullRequestMeta
import com.nextchaptersoftware.ci.triage.pipeline.StepTriagePersist.TriageResult
import com.nextchaptersoftware.ci.workflows.CITriagePipelineWorkflow.TriagePipelineRequest
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildTriageExecutionStep
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.annotations.RunsOn
import io.temporal.activity.ActivityInterface
import io.temporal.activity.ActivityMethod

@RunsOn(Queue.CiTriages)
@ActivityInterface
interface CITriagePipelineActivity {

    @ActivityMethod
    fun start(event: TriagePipelineRequest)

    @ActivityMethod
    fun update(
        event: TriagePipelineRequest,
        step: BuildTriageExecutionStep,
    )

    @ActivityMethod
    fun select(
        event: TriagePipelineRequest,
        triageJobsIds: List<BuildJobId>,
    )

    @ActivityMethod
    fun stop(
        event: TriagePipelineRequest,
        stage: BuildTriageFilterStage,
    )

    @ActivityMethod
    fun pullRequestMeta(
        event: TriagePipelineRequest,
    ): PullRequestMeta?

    @ActivityMethod
    fun pullRequestDiff(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        logSummary: String,
    ): String?

    @ActivityMethod
    fun triageFetchDocs(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
    ): DocumentResult?

    @ActivityMethod
    fun triageReport(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        documentResult: DocumentResult,
        templateOverrides: Collection<MLInferenceTemplateId>?,
    ): TriageModelResult?

    @ActivityMethod
    fun triageEval(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        documentResult: DocumentResult,
        triageModelResult: TriageModelResult,
    ): TriageEvalResult

    @ActivityMethod
    fun triagePersist(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        triageModelResult: TriageModelResult,
        triageEvalResult: TriageEvalResult?,
    ): TriageResult
}
