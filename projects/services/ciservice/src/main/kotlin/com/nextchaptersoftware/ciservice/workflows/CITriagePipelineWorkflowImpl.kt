package com.nextchaptersoftware.ciservice.workflows

import com.nextchaptersoftware.ci.workflows.CITriagePipelineWorkflow
import com.nextchaptersoftware.ci.workflows.CITriagePipelineWorkflow.TriagePipelineRequest
import com.nextchaptersoftware.ci.workflows.CITriagePipelineWorkflow.TriagePipelineResult
import com.nextchaptersoftware.ciservice.activities.CITriagePipelineActivity
import com.nextchaptersoftware.serialization.Serialization.encode
import com.nextchaptersoftware.temporal.kotlin.ChildWorkflowOptionsKt.setWorkflowIdKt
import com.nextchaptersoftware.temporal.kotlin.WorkflowKt
import io.temporal.workflow.Async

internal object CITriagePipelineWorkflowImpl : CITriagePipelineWorkflow {

    @Suppress("ReturnCount")
    override fun execute(event: TriagePipelineRequest): TriagePipelineResult? {
        val ciTriage = WorkflowKt.newActivityStub<CITriagePipelineActivity>()

        ciTriage.start(event)

        val pullRequestMetaPromise = Async.function {
            ciTriage.pullRequestMeta(event)
        }

        val logsWorkflow = WorkflowKt.newChildWorkflowStub<CITriagePipelineLogsWorkflow> {
            setWorkflowIdKt<CITriagePipelineLogsWorkflow>(event.triageRequest.buildId)
        }

        val logSummaryPromise = Async.function {
            logsWorkflow.execute(event)
        }

        val pullRequestMeta = pullRequestMetaPromise.get() ?: return null
        val logSummary = logSummaryPromise.get() ?: return null

        val pullRequestDiff = ciTriage.pullRequestDiff(
            event = event,
            pullRequestMeta = pullRequestMeta,
            logSummary = logSummary,
        )
            ?: return null

        val documentResult = ciTriage.triageFetchDocs(
            event = event,
            pullRequestMeta = pullRequestMeta,
            pullRequestDiff = pullRequestDiff,
            logSummary = logSummary,
        ) ?: return null

        val triageModelResult = ciTriage.triageReport(
            event = event,
            pullRequestMeta = pullRequestMeta,
            pullRequestDiff = pullRequestDiff,
            logSummary = logSummary,
            documentResult = documentResult,
            templateOverrides = event.options?.templateOverrides,
        ) ?: return null

        val triageEvalResult = if (triageModelResult.shouldSuggestFix) {
             ciTriage.triageEval(
                event = event,
                pullRequestMeta = pullRequestMeta,
                pullRequestDiff = pullRequestDiff,
                logSummary = logSummary,
                documentResult = documentResult,
                triageModelResult = triageModelResult,
            )
        } else {
            null
        }

        if (event.options?.isEphemeral == true) {
            return TriagePipelineResult(
                triageId = null,
                isVisible = false,
                filter = triageModelResult.filterStage ?: triageEvalResult?.filterStage,
                triageModelResult = triageModelResult.encode(),
                triageEvalResult = triageEvalResult?.encode(),
            )
        }

        val persistResult = ciTriage.triagePersist(
            event = event,
            pullRequestMeta = pullRequestMeta,
            pullRequestDiff = pullRequestDiff,
            logSummary = logSummary,
            triageModelResult = triageModelResult,
            triageEvalResult = triageEvalResult,
        )

        return TriagePipelineResult(
            triageId = persistResult.triageId,
            isVisible = persistResult.isVisible,
            filter = triageModelResult.filterStage ?: triageEvalResult?.filterStage,
        )
    }
}
