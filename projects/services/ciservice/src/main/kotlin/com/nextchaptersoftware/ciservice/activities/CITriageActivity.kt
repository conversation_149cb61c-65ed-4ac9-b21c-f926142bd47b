package com.nextchaptersoftware.ciservice.activities

import com.nextchaptersoftware.ci.workflows.CITriageWorkflow.TriageRequest
import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildTriageExecutionId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.temporal.annotations.Queue
import com.nextchaptersoftware.temporal.annotations.RunsOn
import io.temporal.activity.ActivityInterface
import io.temporal.activity.ActivityMethod
import kotlinx.serialization.Serializable

@RunsOn(Queue.CiTriages)
@ActivityInterface
interface CITriageActivity {

    @ActivityMethod
    fun validate(
        event: TriageRequest,
    ): TriageRequestResult?

    @ActivityMethod
    fun markCancelled(
        executionId: BuildTriageExecutionId,
    )
}

@Serializable
data class TriageRequestResult(
    val executionId: BuildTriageExecutionId,
    val jobIds: List<BuildJobId>,
    val pullRequestCreatorId: OrgMemberId,
    val previousBuildIds: Collection<BuildId>?,
)
