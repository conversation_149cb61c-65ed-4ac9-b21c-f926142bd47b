package com.nextchaptersoftware.ciservice.activities

import com.nextchaptersoftware.ci.events.EventPublisher
import com.nextchaptersoftware.ci.triage.DocumentResult
import com.nextchaptersoftware.ci.triage.TriageEvalResult
import com.nextchaptersoftware.ci.triage.TriageModelResult
import com.nextchaptersoftware.ci.triage.pipeline.StepPullRequestDiff
import com.nextchaptersoftware.ci.triage.pipeline.StepPullRequestMeta
import com.nextchaptersoftware.ci.triage.pipeline.StepPullRequestMeta.PullRequestMeta
import com.nextchaptersoftware.ci.triage.pipeline.StepTriageGenerateEval
import com.nextchaptersoftware.ci.triage.pipeline.StepTriageGenerateFetchDocs
import com.nextchaptersoftware.ci.triage.pipeline.StepTriageGenerateReport
import com.nextchaptersoftware.ci.triage.pipeline.StepTriagePersist
import com.nextchaptersoftware.ci.triage.pipeline.StepTriagePersist.TriageResult
import com.nextchaptersoftware.ci.triage.unwrapOrElseStop
import com.nextchaptersoftware.ci.workflows.CITriagePipelineWorkflow.TriagePipelineRequest
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildTriageExecutionState
import com.nextchaptersoftware.db.models.BuildTriageExecutionStep
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.stores.Stores.buildJobStore
import com.nextchaptersoftware.db.stores.Stores.buildStore
import com.nextchaptersoftware.db.stores.Stores.buildTriageExecutionStore
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.temporal.kotlin.ActivityKt.suspendedActivity
import com.nextchaptersoftware.utils.KotlinUtils.required

internal class CITriagePipelineActivityImpl(
    private val stepPullRequestDiff: StepPullRequestDiff,
    private val stepPullRequestMeta: StepPullRequestMeta,
    private val stepTriageGenerateFetchDocs: StepTriageGenerateFetchDocs,
    private val stepTriageGenerateReport: StepTriageGenerateReport,
    private val stepTriageGenerateEval: StepTriageGenerateEval,
    private val stepTriagePersist: StepTriagePersist,
) : CITriagePipelineActivity {

    override fun start(event: TriagePipelineRequest): Unit = suspendedActivity fn@{
        event.executionId?.let { executionId ->
            buildTriageExecutionStore.update(
                executionId = executionId,
                state = BuildTriageExecutionState.Running,
            )
        }
    }

    override fun update(
        event: TriagePipelineRequest,
        step: BuildTriageExecutionStep,
    ): Unit = suspendedActivity fn@{
        event.executionId?.let { executionId ->
            buildTriageExecutionStore.update(
                executionId = executionId,
                step = step,
            )
        }
    }

    override fun select(
        event: TriagePipelineRequest,
        triageJobsIds: List<BuildJobId>,
    ): Unit = suspendedActivity fn@{
        event.executionId?.let { executionId ->
            buildTriageExecutionStore.update(
                executionId = executionId,
                triageJobsIds = triageJobsIds,
            )
        }
    }

    override fun stop(
        event: TriagePipelineRequest,
        stage: BuildTriageFilterStage,
    ): Unit = suspendedActivity fn@{
        event.executionId?.let { executionId ->
            buildTriageExecutionStore.update(
                executionId = executionId,
                step = BuildTriageExecutionStep.Complete,
                state = BuildTriageExecutionState.Filter,
                stage = stage,
            )
        }
    }

    override fun pullRequestMeta(event: TriagePipelineRequest): PullRequestMeta? = suspendedActivity fn@{
        update(event, BuildTriageExecutionStep.PullRequest)

        stepPullRequestMeta.execute(
            scmTeam = event.scmTeam(),
            repo = event.repo(),
            pullRequest = event.pullRequest(),
            build = event.build(),
        )
            .unwrapOrElseStop {
                stop(event, it.stage)
                return@fn null // pipeline stops
            }
    }

    override fun pullRequestDiff(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        logSummary: String,
    ): String? = suspendedActivity fn@{
        update(event, BuildTriageExecutionStep.Diff)

        stepPullRequestDiff.execute(
            org = event.org(),
            pullRequest = event.pullRequest(),
            pullRequestMeta = pullRequestMeta,
            build = event.build(),
            jobLogSummary = logSummary,
        )
            .unwrapOrElseStop {
                stop(event, it.stage)
                return@fn null // pipeline stops
            }
    }

    override fun triageFetchDocs(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
    ): DocumentResult? = suspendedActivity fn@{
        val org = event.org()
        val scmTeam = event.scmTeam()
        val repo = event.repo()
        val pullRequest = event.pullRequest()
        val build = event.build()

        return@fn stepTriageGenerateFetchDocs.execute(
            org = org,
            scmTeam = scmTeam,
            repo = repo,
            pullRequest = pullRequest,
            pullRequestMeta = pullRequestMeta,
            pullRequestDiff = pullRequestDiff,
            build = build,
            jobLogSummary = logSummary,
        )
            .unwrapOrElseStop {
                stop(event, it.stage)
                return@fn null // pipeline stops
            }
    }

    override fun triageReport(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        documentResult: DocumentResult,
        templateOverrides: Collection<MLInferenceTemplateId>?,
    ): TriageModelResult? = suspendedActivity fn@{
        update(event, BuildTriageExecutionStep.Triage)

        val org = event.org()
        val pullRequest = event.pullRequest()
        val build = event.build()

        stepTriageGenerateReport.execute(
            org = org,
            pullRequest = pullRequest,
            pullRequestDiff = pullRequestDiff,
            build = build,
            jobLogSummary = logSummary,
            templateOverrides = templateOverrides,
            documentResult = documentResult,
        )
            .unwrapOrElseStop {
                stop(event, it.stage)
                return@fn null // pipeline stops
            }
    }

    override fun triageEval(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        documentResult: DocumentResult,
        triageModelResult: TriageModelResult,
    ): TriageEvalResult = suspendedActivity fn@{
        val org = event.org()
        val pullRequest = event.pullRequest()
        val build = event.build()

        stepTriageGenerateEval.execute(
            org = org,
            pullRequest = pullRequest,
            pullRequestDiff = pullRequestDiff,
            build = build,
            jobLogSummary = logSummary,
            documentResult = documentResult,
            triageModelResult = triageModelResult,
        )
    }

    override fun triagePersist(
        event: TriagePipelineRequest,
        pullRequestMeta: PullRequestMeta,
        pullRequestDiff: String,
        logSummary: String,
        triageModelResult: TriageModelResult,
        triageEvalResult: TriageEvalResult?,
    ): TriageResult = suspendedActivity fn@{
        val executionId = checkNotNull(event.executionId) {
            "Attempt to persist an ephemeral triage execution"
        }

        val scmTeam = event.scmTeam()
        val repo = event.repo()
        val pullRequest = event.pullRequest()
        val build = event.build()

        val result = stepTriagePersist.execute(
            scmTeam = scmTeam,
            repo = repo,
            pullRequest = pullRequest,
            build = build,
            jobs = event.jobs(),
            executionId = executionId,
            triageModelResult = triageModelResult,
            triageEvalResult = triageEvalResult,
        )

        EventPublisher.publish(
            type = "TriageExecuteEvent",
            "triageId" to result.triageId,
            "triageIsVisible" to result.isVisible,
            "triageExecutionId" to event.executionId,
            "triageExecutionStage" to result.filter,
        )

        buildTriageExecutionStore.update(
            executionId = executionId,
            step = BuildTriageExecutionStep.Complete,
            state = when (result.filter) {
                null -> BuildTriageExecutionState.Complete
                else -> BuildTriageExecutionState.Filter
            },
            stage = result.filter,
        )

        return@fn result
    }
}

internal suspend fun TriagePipelineRequest.org() = orgStore.findById(orgId = triageRequest.orgId).required()

internal suspend fun TriagePipelineRequest.ciInstallation() = installationStore.findById(installationId = triageRequest.ciInstallationId).required()

internal suspend fun TriagePipelineRequest.scmTeam() = scmTeamStore.findById(teamId = triageRequest.scmTeamId).required()

internal suspend fun TriagePipelineRequest.repo() = repoStore.findById(repoId = triageRequest.repoId).required()

internal suspend fun TriagePipelineRequest.pullRequest() = pullRequestStore.findById(prId = triageRequest.pullRequestId).required()

internal suspend fun TriagePipelineRequest.build() = buildStore.findById(buildId = triageRequest.buildId).required()

internal suspend fun TriagePipelineRequest.jobs() = buildJobStore.findAll(jobIds = jobIds).required()
