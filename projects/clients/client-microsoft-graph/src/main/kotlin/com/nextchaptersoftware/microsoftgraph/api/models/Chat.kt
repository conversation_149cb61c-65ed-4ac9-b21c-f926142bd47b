package com.nextchaptersoftware.microsoftgraph.api.models

import kotlinx.serialization.Serializable

@Serializable
data class HookPayload(
    val text: String? = null,
    val textFormat: String? = null,
    val attachments: List<Attachment>? = null,
    val type: WebhookEventType,
    val timestamp: String,
    val localTimestamp: String,
    val id: String,
    val channelId: String,
    val serviceUrl: String,
    val from: ChatUser,
    val conversation: Conversation,
    val recipient: ChatUser,
    val entities: List<Entity>? = null,
    val channelData: ChannelData? = null,
    val locale: String? = null,
    val localTimezone: String? = null,
)

@Serializable
data class Attachment(
    val contentType: String,
    val content: String,
)

@Serializable
data class ChatUser(
    val id: String,
    val name: String,
    val aadObjectId: String? = null,
)

@Serializable
data class Conversation(
    val conversationType: String,
    val tenantId: String,
    val id: String,
    val isGroup: Boolean? = null,
)

@Serializable
data class Entity(
    val type: String,
    val locale: String? = null,
    val country: String? = null,
    val platform: String? = null,
    val timezone: String? = null,
    val mentioned: Mentioned? = null,
    val text: String? = null,
)

@Serializable
data class Mentioned(
    val id: String,
    val name: String,
)

@Serializable
data class ChannelData(
    val tenant: Tenant? = null,
    val teamsChannelId: String? = null,
    val teamsTeamId: String? = null,
    val channel: Channel? = null,
    val team: Team? = null,
)

@Serializable
data class Tenant(
    val id: String,
)

@Serializable
data class Channel(
    val id: String,
    val displayName: String? = null,
    val webUrl: String? = null,
    val isArchived: Boolean? = null,
)

@Serializable
data class Team(
    val id: String,
    val displayName: String? = null,
    val webUrl: String? = null,
    val isArchived: Boolean? = null,
)
