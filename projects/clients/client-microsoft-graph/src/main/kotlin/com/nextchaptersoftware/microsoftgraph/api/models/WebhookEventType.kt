package com.nextchaptersoftware.microsoftgraph.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class WebhookEventType(val value: String) {
    @SerialName("message")
    MESSAGE("message"),

    @SerialName("conversationUpdate")
    CONVERSATION_UPDATE("conversationUpdate"),

    @SerialName("contactRelationUpdate")
    CONTACT_RELATION_UPDATE("contactRelationUpdate"),

    @SerialName("typing")
    TYPING("typing"),

    @SerialName("endOfConversation")
    END_OF_CONVERSATION("endOfConversation"),

    @SerialName("event")
    EVENT("event"),

    @SerialName("invoke")
    INVOKE("invoke"),

    @SerialName("deleteUserData")
    DELETE_USER_DATA("deleteUserData"),

    @SerialName("messageUpdate")
    MESSAGE_UPDATE("messageUpdate"),

    @SerialName("messageDelete")
    MESSAGE_DELETE("messageDelete"),

    @SerialName("installationUpdate")
    INSTALLATION_UPDATE("installationUpdate"),

    @SerialName("messageReaction")
    MESSAGE_REACTION("messageReaction"),

    @SerialName("suggestion")
    SUGGESTION("suggestion"),

    @SerialName("trace")
    TRACE("trace"),

    @SerialName("handoff")
    HANDOFF("handoff"),
    ;

    companion object {
        fun fromString(value: String): WebhookEventType? {
            return values().find { it.value == value }
        }
    }
}
