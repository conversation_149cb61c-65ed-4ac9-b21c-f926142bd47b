CREATE TABLE IF NOT EXISTS ingestioncataloguemodel (id uuid PRIMARY KEY, "createdAt" TIMESTAMP NOT NULL, "modifiedAt" TIMESTAMP NOT NULL, installation uuid NOT NULL, org uuid NOT NULL, provider INT NOT NULL, "type" INT NOT NULL, "groupId" uuid NOT NULL, "externalId" TEXT NOT NULL, "externalParentId" TEXT NULL, "needsExtraction" BOOLEAN NULL, "needsEmbedding" BOOLEAN NULL, "contentHash" TEXT NULL);
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_installation ON ingestioncataloguemodel (installation);
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_org ON ingestioncataloguemodel (org);
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_groupid ON ingestioncataloguemodel ("groupId");
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_externalid ON ingestioncataloguemodel ("externalId");
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_externalparentid ON ingestioncataloguemodel ("externalParentId");
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_needsextraction ON ingestioncataloguemodel ("needsExtraction");
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_needsembedding ON ingestioncataloguemodel ("needsEmbedding");
CREATE INDEX IF NOT EXISTS ingestioncataloguemodel_contenthash ON ingestioncataloguemodel ("contentHash");
DO $$ BEGIN IF NOT EXISTS ( SELECT 1 FROM pg_constraint WHERE conname = 'ingestioncataloguemodel_installation_provider_type_groupid_exte' AND conrelid = 'ingestioncataloguemodel'::regclass ) THEN ALTER TABLE ingestioncataloguemodel ADD CONSTRAINT ingestioncataloguemodel_installation_provider_type_groupid_exte UNIQUE (installation, provider, "type", "groupId", "externalId"); END IF; END $$;
DO $$ BEGIN IF NOT EXISTS ( SELECT 1 FROM pg_constraint WHERE conname = 'fk_ingestioncataloguemodel_installation__id' AND conrelid = 'ingestioncataloguemodel'::regclass ) THEN ALTER TABLE ingestioncataloguemodel ADD CONSTRAINT fk_ingestioncataloguemodel_installation__id FOREIGN KEY (installation) REFERENCES installationmodel(id) ON DELETE CASCADE ON UPDATE CASCADE; END IF; END $$;
DO $$ BEGIN IF NOT EXISTS ( SELECT 1 FROM pg_constraint WHERE conname = 'fk_ingestioncataloguemodel_org__id' AND conrelid = 'ingestioncataloguemodel'::regclass ) THEN ALTER TABLE ingestioncataloguemodel ADD CONSTRAINT fk_ingestioncataloguemodel_org__id FOREIGN KEY (org) REFERENCES orgmodel(id) ON DELETE CASCADE ON UPDATE CASCADE; END IF; END $$;
ALTER TABLE teamsettingsmodel ADD IF NOT EXISTS "enablePullRequestIngestionCatalogue" BOOLEAN NULL;
DO $$ BEGIN IF NOT EXISTS ( SELECT 1 FROM pg_trigger WHERE tgrelid = 'ingestioncataloguemodel'::regclass AND tgname  = 'update_modifiedat' ) THEN CREATE TRIGGER update_modifiedat BEFORE UPDATE ON ingestioncataloguemodel FOR EACH ROW EXECUTE PROCEDURE trigger_update_modifiedAt(); END IF; END $$;
