package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.compress.compress
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.CodeReviewComment
import com.nextchaptersoftware.db.models.CodeReviewCommentModel
import com.nextchaptersoftware.db.models.CodeReviewId
import com.nextchaptersoftware.db.models.toCodeReviewComment
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.batchInsert
import org.jetbrains.exposed.sql.selectAll

class CodeReviewCommentStore internal constructor() {

    suspend fun findComments(codeReviewId: CodeReviewId): List<CodeReviewComment> {
        return suspendedTransaction {
            CodeReviewCommentModel
                .selectAll()
                .where { CodeReviewCommentModel.review eq codeReviewId }
                .orderBy(CodeReviewCommentModel.createdAt to SortOrder.ASC)
                .map { it.toCodeReviewComment() }
        }
    }

    suspend fun insertComments(codeReviewId: CodeReviewId, comments: List<CodeReviewComment>) {
        suspendedTransaction {
            CodeReviewCommentModel.batchInsert(comments) {
                this[CodeReviewCommentModel.review] = codeReviewId
                this[CodeReviewCommentModel.filePath] = it.filePath
                this[CodeReviewCommentModel.lineSide] = it.lineSide
                this[CodeReviewCommentModel.lineStart] = it.lineStart
                this[CodeReviewCommentModel.lineEnd] = it.lineEnd
                this[CodeReviewCommentModel.category] = it.category
                this[CodeReviewCommentModel.severity] = it.severity
                this[CodeReviewCommentModel.title] = it.title
                this[CodeReviewCommentModel.encodedBody] = it.body.compress()
            }
        }
    }
}
