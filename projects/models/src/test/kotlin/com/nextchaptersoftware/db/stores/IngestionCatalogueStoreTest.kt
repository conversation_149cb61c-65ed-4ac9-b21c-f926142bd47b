package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.models.IngestionCatalogueType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class IngestionCatalogueStoreTest : DatabaseTestsBase() {
    private val store = IngestionCatalogueStore()

    @Test
    fun `upsert insert and find by natural key`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        val groupId = UUID.randomUUID()
        val inserted = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "123",
            externalParentId = "parent-1",
            needsExtraction = true,
            needsEmbedding = true,
        )

        // Basic fields
        assertThat(inserted.orgId).isEqualTo(org.idValue)
        assertThat(inserted.installationId).isEqualTo(installation.idValue)
        assertThat(inserted.provider).isEqualTo(Provider.Jira)
        assertThat(inserted.type).isEqualTo(IngestionCatalogueType.ScmPullRequest)
        assertThat(inserted.groupId).isEqualTo(groupId)
        assertThat(inserted.externalId).isEqualTo("123")
        assertThat(inserted.externalParentId).isEqualTo("parent-1")
        assertThat(inserted.needsExtraction).isTrue()
        assertThat(inserted.needsEmbedding).isTrue()

        // Find by natural key
        val found = store.findByNaturalKey(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "123",
        )
        assertThat(found?.id).isEqualTo(inserted.id)
    }

    @Test
    fun `upsert update modifies parent and flags`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val groupId = UUID.randomUUID()

        val initial = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "321",
            externalParentId = null,
            needsExtraction = true,
            needsEmbedding = false,
        )

        val updated = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "321",
            externalParentId = "epic-9",
            needsExtraction = false,
            needsEmbedding = true,
        )

        assertThat(updated.id).isEqualTo(initial.id) // same row
        assertThat(updated.externalParentId).isEqualTo("epic-9")
        assertThat(updated.needsExtraction).isFalse()
        assertThat(updated.needsEmbedding).isTrue()
    }

    @Test
    fun `extraction queue lifecycle`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val groupId1 = UUID.randomUUID()
        val groupId2 = UUID.randomUUID()

        // Seed two items needing extraction
        val a = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId1,
            externalId = "1",
            needsExtraction = true,
            needsEmbedding = false,
        )
        val b = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId2,
            externalId = "2",
            needsExtraction = true,
            needsEmbedding = false,
        )

        // Installations with work
        val insts = store.getInstallationsForExtraction()
        assertThat(insts).contains(org.idValue to installation.idValue)

        // Per-installation fetch
        val toExtract = store.getInstallationItemsForExtraction(installationId = installation.idValue)
        assertThat(toExtract.map { it.id }).containsExactly(a.id, b.id)

        // Complete one
        store.completeExtraction(id = a.id)
        val remaining = store.getInstallationItemsForExtraction(installationId = installation.idValue)
        assertThat(remaining.map { it.id }).containsExactly(b.id)

        // Complete second
        store.completeExtraction(id = b.id)
        assertThat(store.getInstallationItemsForExtraction(installationId = installation.idValue)).isEmpty()
    }

    @Test
    fun `embedding queue lifecycle`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val groupId = UUID.randomUUID()

        // Two items ready for embedding (no extraction needed)
        val a = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "10",
            needsExtraction = false,
            needsEmbedding = true,
        )
        val b = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "20",
            needsExtraction = false,
            needsEmbedding = true,
        )

        val toEmbed = store.getNextItemsForEmbedding()
        assertThat(toEmbed[org.idValue]?.map { it.id }).containsExactly(a.id, b.id)

        // Complete one
        store.completeEmbedding(id = a.id)
        val afterOne = store.getNextItemsForEmbedding()
        assertThat(afterOne[org.idValue]?.map { it.id }).containsExactly(b.id)

        // Complete second
        store.completeEmbedding(id = b.id)
        val afterAll = store.getNextItemsForEmbedding()
        assertThat(afterAll[org.idValue]).isNullOrEmpty()
    }

    @Test
    fun `processingIsComplete reflects combined flags`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val groupId = UUID.randomUUID()

        // Start with an item needing extraction
        val item = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "99",
            needsExtraction = true,
            needsEmbedding = false,
        )
        assertThat(store.processingIsComplete(installationId = installation.idValue)).isFalse()

        // Clear extraction, now needs embedding
        store.completeExtraction(id = item.id)
        store.setNeedsEmbedding(id = item.id, needsEmbedding = true)
        assertThat(store.processingIsComplete(installationId = installation.idValue)).isFalse()

        // Clear embedding → complete
        store.completeEmbedding(id = item.id)
        assertThat(store.processingIsComplete(installationId = installation.idValue)).isTrue()
    }

    @Test
    fun `requeue and set flags`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val groupId = UUID.randomUUID()

        val item = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "77",
            needsExtraction = false,
            needsEmbedding = false,
        )

        // Requeue whole installation
        store.requeueInstallation(
            installationId = installation.idValue,
            extraction = true,
            embedding = true,
        )

        val afterRequeue = store.findByNaturalKey(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "77",
        )
        assertThat(afterRequeue?.needsExtraction).isTrue()
        assertThat(afterRequeue?.needsEmbedding).isTrue()

        // Set specific flags
        store.setNeedsExtraction(id = item.id, needsExtraction = false)
        store.setNeedsEmbedding(id = item.id, needsEmbedding = false)
        val afterSets =
            store.findByNaturalKey(
                orgId = org.idValue,
                installationId = installation.idValue,
                provider = Provider.Jira,
                type = IngestionCatalogueType.ScmPullRequest,
                groupId = groupId,
                externalId = "77",
            )
        assertThat(afterSets?.needsExtraction).isFalse()
        assertThat(afterSets?.needsEmbedding).isFalse()
    }

    @Test
    fun `delete by natural key removes row`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        val groupId = UUID.randomUUID()
        val item = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "555",
            needsExtraction = false,
            needsEmbedding = false,
        )
        val exists =
            store.findByNaturalKey(
                orgId = org.idValue,
                installationId = installation.idValue,
                provider = Provider.Jira,
                type = IngestionCatalogueType.ScmPullRequest,
                groupId = groupId,
                externalId = "555",
            )
        assertThat(exists?.id).isEqualTo(item.id)

        store.deleteByNaturalKey(
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = "555",
        )

        val afterDelete =
            store.findByNaturalKey(
                orgId = org.idValue,
                installationId = installation.idValue,
                provider = Provider.Jira,
                type = IngestionCatalogueType.ScmPullRequest,
                groupId = groupId,
                externalId = "555",
            )
        assertThat(afterDelete).isNull()
    }

    @Test
    fun `empty and isolation checks`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        // No items yet
        assertThat(store.getInstallationItemsForExtraction(installationId = installation.idValue)).isEmpty()
        assertThat(store.getNextItemsForEmbedding()).isEmpty()
        assertThat(store.processingIsComplete(installationId = installation.idValue)).isTrue()

        // Another random installation should be isolated
        val other = InstallationId.random()
        assertThat(store.getInstallationItemsForExtraction(installationId = other)).isEmpty()
        assertThat(store.processingIsComplete(installationId = other)).isTrue()
    }

    @Test
    fun `upsert sets contentHash`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        val groupId = UUID.randomUUID()

        val externalId = "abc123"
        val hash1 = "hash-value-1"

        val row = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
            contentHash = hash1,
        )

        assertThat(row.contentHash).isEqualTo(hash1)

        val found = store.findByNaturalKey(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
        )
        assertThat(found?.contentHash).isEqualTo(hash1)
    }

    @Test
    fun `upsert with same contentHash keeps hash unchanged`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        val groupId = UUID.randomUUID()
        val externalId = "id-1"
        val hash = "same-hash"

        val first = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
            contentHash = hash,
        )

        val second = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
            contentHash = hash,
        )

        assertThat(second.id).isEqualTo(first.id)
        assertThat(second.contentHash).isEqualTo(hash)
    }

    @Test
    fun `upsert with new contentHash updates hash`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        val groupId = UUID.randomUUID()

        val externalId = "id-xyz"
        val oldHash = "old-hash"
        val newHash = "new-hash"

        val first = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
            contentHash = oldHash,
        )
        assertThat(first.contentHash).isEqualTo(oldHash)

        val updated = store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.Jira,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = groupId,
            externalId = externalId,
            contentHash = newHash,
        )
        assertThat(updated.id).isEqualTo(first.id)
        assertThat(updated.contentHash).isEqualTo(newHash)
    }

    @Test
    fun `getInstallationsForEmbedding returns installations with items ready for embedding`() = suspendingDatabaseTest {
        val org1 = makeOrg()
        val org2 = makeOrg()
        val installation1 = makeInstallation(org = org1)
        val installation2 = makeInstallation(org = org2)
        val installation3 = makeInstallation(org = org1)

        // Installation 1: Has items ready for embedding (extraction complete, needs embedding)
        store.upsert(
            orgId = org1.idValue,
            installationId = installation1.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-1",
            needsExtraction = false,
            needsEmbedding = true,
        )

        // Installation 2: Has items ready for embedding
        store.upsert(
            orgId = org2.idValue,
            installationId = installation2.idValue,
            provider = Provider.GitLab,
            type = IngestionCatalogueType.ScmPullRequestComment,
            groupId = UUID.randomUUID(),
            externalId = "comment-1",
            needsExtraction = false,
            needsEmbedding = true,
        )

        // Installation 3: Has items but still needs extraction (should NOT appear)
        store.upsert(
            orgId = org1.idValue,
            installationId = installation3.idValue,
            provider = Provider.AzureDevOps,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-2",
            needsExtraction = true,
            needsEmbedding = true,
        )

        val installations = store.getInstallationsForEmbedding()

        assertThat(installations).containsExactlyInAnyOrder(
            org1.idValue to installation1.idValue,
            org2.idValue to installation2.idValue,
        )
        assertThat(installations).doesNotContain(org1.idValue to installation3.idValue)
    }

    @Test
    fun `getInstallationsForEmbedding excludes installations with no embedding work`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation1 = makeInstallation(org = org)
        val installation2 = makeInstallation(org = org)

        // Installation 1: No embedding needed
        store.upsert(
            orgId = org.idValue,
            installationId = installation1.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-1",
            needsExtraction = false,
            needsEmbedding = false,
        )

        // Installation 2: Has embedding work
        store.upsert(
            orgId = org.idValue,
            installationId = installation2.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-2",
            needsExtraction = false,
            needsEmbedding = true,
        )

        val installations = store.getInstallationsForEmbedding()

        assertThat(installations).containsExactly(org.idValue to installation2.idValue)
        assertThat(installations).doesNotContain(org.idValue to installation1.idValue)
    }

    @Test
    fun `getInstallationsForEmbedding returns distinct installation pairs`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        // Multiple items in same installation ready for embedding
        store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-1",
            needsExtraction = false,
            needsEmbedding = true,
        )
        store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequestComment,
            groupId = UUID.randomUUID(),
            externalId = "comment-1",
            needsExtraction = false,
            needsEmbedding = true,
        )
        store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.GitLab,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "mr-1",
            needsExtraction = false,
            needsEmbedding = true,
        )

        val installations = store.getInstallationsForEmbedding()

        // Should return only one pair despite multiple items
        assertThat(installations).containsExactly(org.idValue to installation.idValue)
    }

    @Test
    fun `getInstallationsForEmbedding returns empty list when no work available`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(org = org)

        // Item that still needs extraction
        store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-1",
            needsExtraction = true,
            needsEmbedding = true,
        )

        // Item that doesn't need embedding
        store.upsert(
            orgId = org.idValue,
            installationId = installation.idValue,
            provider = Provider.GitHub,
            type = IngestionCatalogueType.ScmPullRequest,
            groupId = UUID.randomUUID(),
            externalId = "pr-2",
            needsExtraction = false,
            needsEmbedding = false,
        )

        val installations = store.getInstallationsForEmbedding()

        assertThat(installations).isEmpty()
    }
}
