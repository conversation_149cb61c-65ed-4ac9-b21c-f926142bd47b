plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:documents:documents-mongo-core", "default"))
    implementation(project(":projects:libs:lib-ml-functions-mongo-core", "default"))
    implementation(project(":projects:libs:lib-ml-mongo", "default"))
    implementation(project(":projects:libs:lib-pr-ingestion-catalogue", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:documents:documents-mongo-core", "test"))

    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.core)
    testRuntimeOnly(testLibs.bundles.test.core.runtime)
}
