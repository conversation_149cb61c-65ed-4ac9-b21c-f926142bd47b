plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(libs.kotlinx.serialization.json)

    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-log-kotlin", "default"))

    testImplementation(testLibs.bundles.test.core)
    testRuntimeOnly(testLibs.bundles.test.core.runtime)

    testImplementation(project(":projects:libs:lib-common", "test"))
}
