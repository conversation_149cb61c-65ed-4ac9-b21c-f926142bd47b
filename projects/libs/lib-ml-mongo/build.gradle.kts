plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-openai", "default"))
    implementation(project(":projects:documents:documents-mongo-core", "default"))
    implementation(project(":projects:documents:documents-mongo-scm", "default"))
    implementation(project(":projects:libs:lib-config", "default"))
    implementation(project(":projects:libs:lib-document-schema", "default"))
    implementation(project(":projects:libs:lib-ml-completion", "default"))
    implementation(project(":projects:libs:lib-mongo", "default"))

    implementation(libs.bundles.mongo)
    implementation(libs.kotlinx.serialization.json)

    testImplementation(testLibs.bundles.test.core)
    testRuntimeOnly(testLibs.bundles.test.core.runtime)

    testImplementation(project(":projects:documents:documents-mongo-core", "test"))
    testImplementation(project(":projects:documents:documents-mongo-serialization", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
}
