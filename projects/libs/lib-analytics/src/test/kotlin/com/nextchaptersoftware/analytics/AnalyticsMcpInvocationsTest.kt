package com.nextchaptersoftware.analytics

import com.nextchaptersoftware.db.ModelBuilders.makeMcpInference
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.models.McpInferenceState
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.startOfDay
import com.nextchaptersoftware.utils.startOfWeek
import java.time.ZoneId
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import kotlinx.datetime.TimeZone
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AnalyticsMcpInvocationsTest : DatabaseTestsBase() {
    private val analyticsMcpInvocations = AnalyticsMcpInvocations()

    @Test
    fun `earliestInvocation returns null when no invocations exist`() = suspendingDatabaseTest {
        val org = makeOrg()

        val earliest = analyticsMcpInvocations.earliestInvocation(orgId = org.idValue)

        assertThat(earliest).isNull()
    }

    @Test
    fun `earliestInvocation returns earliest completed invocation`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val now = Instant.nowWithMicrosecondPrecision()

        // Create MCP inferences at different times
        val earliest = now.minus(3.days)
        val middle = now.minus(2.days)
        val latest = now.minus(1.days)

        // Create completed MCP inferences
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = middle,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = latest,
            productAgent = ProductAgentType.McpCursor,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = earliest,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )

        val result = analyticsMcpInvocations.earliestInvocation(orgId = org.idValue)

        assertThat(result).isEqualTo(earliest)
    }

    @Test
    fun `earliestInvocation ignores processing invocations`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val now = Instant.nowWithMicrosecondPrecision()

        // Create processing MCP inference (should be ignored)
        makeMcpInference(org = org, questionerOrgMember = member, createdAt = now.minus(3.days), productAgent = ProductAgentType.Mcp)

        // Create completed MCP inference
        val completedTime = now.minus(1.days)
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = completedTime,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )

        val result = analyticsMcpInvocations.earliestInvocation(orgId = org.idValue)

        assertThat(result).isEqualTo(completedTime)
    }

    @Test
    fun `invocationTimeSeriesByAgent returns empty when no invocations exist`() = suspendingDatabaseTest {
        val org = makeOrg()
        val now = Instant.nowWithMicrosecondPrecision()

        val (dates, agentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = now.minus(7.days),
            until = now,
            timeZone = TimeZone.currentSystemDefault(),
        )

        assertThat(dates).isEmpty()
        assertThat(agentData).isEmpty()
    }

    @Test
    fun `invocationTimeSeriesByAgent groups by product agent and date`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val now = Instant.nowWithMicrosecondPrecision().startOfDay()

        val oneDayAgo = now.minus(1.days)
        val twoDaysAgo = now.minus(2.days)

        // Create MCP inferences with different product agents and dates
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.McpCursor,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = twoDaysAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )

        val (dates, agentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = null,
            until = now,
            timeZone = TimeZone.currentSystemDefault(),
        )

        assertThat(dates).hasSize(3)
        assertThat(dates).containsExactlyInAnyOrder(
            twoDaysAgo.startOfDay(),
            oneDayAgo.startOfDay(),
            now.startOfDay(),
        )

        assertThat(agentData).hasSize(2)
        assertThat(agentData[ProductAgentType.Mcp]).containsExactly(1L, 2L, 0L) // twoDaysAgo: 1, oneDayAgo: 2
        assertThat(agentData[ProductAgentType.McpCursor]).containsExactly(0L, 1L, 0L) // twoDaysAgo: 0, oneDayAgo: 1
    }

    @Test
    fun `invocationTimeSeriesByAgent groups correctly with null product agents`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val now = Instant.nowWithMicrosecondPrecision().startOfDay()

        val oneDayAgo = now.minus(1.days)
        val twoDaysAgo = now.minus(2.days)

        // Create MCP inferences with different product agents and dates
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = twoDaysAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )

        val (dates, agentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = null,
            until = now,
            timeZone = TimeZone.currentSystemDefault(),
        )

        assertThat(dates).hasSize(3)
        assertThat(dates).containsExactlyInAnyOrder(
            twoDaysAgo.startOfDay(),
            oneDayAgo.startOfDay(),
            now.startOfDay(),
        )

        assertThat(agentData).hasSize(2)
        assertThat(agentData[ProductAgentType.Mcp]).containsExactly(1L, 2L, 0L) // twoDaysAgo: 1, oneDayAgo: 2
        assertThat(agentData[ProductAgentType.McpCursor]).containsExactly(0L, 1L, 0L) // twoDaysAgo: 0, oneDayAgo: 1
    }

    @Test
    fun `invocationTimeSeriesByAgent respects timezone alignment`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val baseZoneId = ZoneId.of("Z")
        val nowUTC = Instant.nowWithMicrosecondPrecision().startOfDay(zoneId = baseZoneId)

        val oneDayAgoUTC = nowUTC.minus(1.days)
        val twoDaysAgoUTC = nowUTC.minus(2.days)

        // Create MCP inferences at UTC times
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgoUTC,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = twoDaysAgoUTC,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )

        // Test with UTC timezone
        val (utcDates, utcAgentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = nowUTC.minus(2.days),
            until = nowUTC,
            timeZone = TimeZone.of(baseZoneId.id),
        )

        assertThat(utcDates).hasSize(3)
        assertThat(utcDates).containsExactlyInAnyOrder(
            twoDaysAgoUTC.startOfDay(zoneId = baseZoneId),
            oneDayAgoUTC.startOfDay(zoneId = baseZoneId),
            nowUTC.startOfDay(zoneId = baseZoneId),
        )

        // Test with different timezone
        val otherZoneId = ZoneId.of("Australia/Sydney")
        val (sydneyDates, sydneyAgentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = nowUTC.minus(2.days),
            until = nowUTC,
            timeZone = TimeZone.of(otherZoneId.id),
        )

        assertThat(sydneyDates).hasSize(3)
        assertThat(sydneyDates).containsExactlyInAnyOrder(
            twoDaysAgoUTC.startOfDay(zoneId = otherZoneId),
            oneDayAgoUTC.startOfDay(zoneId = otherZoneId),
            nowUTC.startOfDay(zoneId = otherZoneId),
        )
    }

    @Test
    fun `invocationTimeSeriesByAgent handles different resolutions`() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val now = Instant.nowWithMicrosecondPrecision().startOfWeek().minus(1.days) // End of previous week

        // Create MCP inferences across multiple days
        val threeDaysAgo = now.minus(3.days)
        val twoDaysAgo = now.minus(2.days)
        val oneDayAgo = now.minus(1.days)

        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = threeDaysAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = twoDaysAgo,
            productAgent = ProductAgentType.Mcp,
            state = McpInferenceState.Complete,
        )
        makeMcpInference(
            org = org,
            questionerOrgMember = member,
            createdAt = oneDayAgo,
            productAgent = ProductAgentType.McpCursor,
            state = McpInferenceState.Complete,
        )

        // Test daily resolution
        val (dailyDates, dailyAgentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Day,
            since = now.minus(3.days),
            until = now,
            timeZone = TimeZone.currentSystemDefault(),
        )

        assertThat(dailyDates).hasSize(4)
        assertThat(dailyAgentData[ProductAgentType.Mcp]).hasSize(4)
        assertThat(dailyAgentData[ProductAgentType.McpCursor]).hasSize(4)

        // Test weekly resolution
        val (weeklyDates, weeklyAgentData) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = org.idValue,
            resolution = AnalyticsDataResolution.Week,
            since = now.minus(3.days),
            until = now,
            timeZone = TimeZone.currentSystemDefault(),
        )

        assertThat(weeklyDates).hasSize(1) // All should fall within the same week
        assertThat(weeklyAgentData[ProductAgentType.Mcp]?.sum()).isEqualTo(2L)
        assertThat(weeklyAgentData[ProductAgentType.McpCursor]?.sum()).isEqualTo(1L)
    }
}
