package com.nextchaptersoftware.analytics

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.DateTruncatePrecision
import com.nextchaptersoftware.db.common.FunctionExtensions.dateTruncate
import com.nextchaptersoftware.db.models.McpInferenceModel
import com.nextchaptersoftware.db.models.McpInferenceState
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAny
import kotlin.time.Instant
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.TimeZone
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.Rank
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNull
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.longLiteral

internal data class AnalyticsMcpInvocationTuple(
    val date: Instant,
    val productAgent: ProductAgentType,
    val count: Long,
)

class AnalyticsMcpInvocations {

    suspend fun earliestInvocation(
        orgId: OrgId,
    ): Instant? {
        return suspendedTransaction {
            McpInferenceModel
                .select(McpInferenceModel.createdAt)
                .where { invocationsClause(orgId, null) }
                .orderBy(McpInferenceModel.createdAt, SortOrder.ASC)
                .limit(1)
                .firstOrNull()
                ?.let { it[McpInferenceModel.createdAt] }
        }
    }

    suspend fun invocationTimeSeriesByAgent(
        orgId: OrgId,
        resolution: AnalyticsDataResolution,
        since: Instant?,
        until: Instant,
        timeZone: TimeZone,
    ): Pair<List<Instant>, Map<ProductAgentType, List<Long>>> {
        val precision = when (resolution) {
            AnalyticsDataResolution.Month -> DateTruncatePrecision.month
            AnalyticsDataResolution.Week -> DateTruncatePrecision.week
            AnalyticsDataResolution.Day -> DateTruncatePrecision.day
        }

        val alignedSince = since?.let {
            DateUtils.truncate(since, resolution, timeZone)
        }

        val invocationTuples = timeSeriesInvocationCountByAgent(orgId, precision, alignedSince, timeZone)
            .ifEmpty {
                return emptyList<Instant>() to emptyMap()
            }

        val x = DateUtils.generateInstantSequence(
            start = alignedSince ?: invocationTuples.first().date,
            end = until,
            stepUnit = when (resolution) {
                AnalyticsDataResolution.Month -> DateTimeUnit.MONTH
                AnalyticsDataResolution.Week -> DateTimeUnit.WEEK
                AnalyticsDataResolution.Day -> DateTimeUnit.DAY
            },
            timeZone = timeZone,
        )

        // Every date in the invocationTuples must exist in x
        run {
            val dates = invocationTuples.map { it.date }
            check(dates.all { it in x }) { "All dates in invocationTuples should be in x\ninvocationTuples: $dates\nx: $x" }
        }

        val agentTypes = invocationTuples.map { it.productAgent }.distinct()

        val series = agentTypes.associateWith { agentType ->
            invocationTuples
                .filter { it.productAgent == agentType }
                .groupBy { it.date }
                .mapValues { (_, tuples) ->
                    check(tuples.size == 1) { "There should be only one tuple per date per agent type" }
                    tuples.sumOf { it.count }
                }
                .let { x.map { date -> it[date] ?: 0 } }
        }

        return x to series
    }

    private suspend fun timeSeriesInvocationCountByAgent(
        orgId: OrgId,
        precision: DateTruncatePrecision,
        since: Instant?,
        timeZone: TimeZone,
    ): List<AnalyticsMcpInvocationTuple> {
        val agentRank = Rank()
            .over()
            .partitionBy(McpInferenceModel.id)
            .orderBy(McpInferenceModel.createdAt to SortOrder.DESC)
            .alias("rank")

        val productAgentCoalesced = Coalesce(McpInferenceModel.productAgent, intLiteral(ProductAgentType.Mcp.dbOrdinal)).alias("productAgent")

        val rankedAgents = McpInferenceModel
            .select(
                McpInferenceModel.id,
                McpInferenceModel.createdAt,
                productAgentCoalesced,
                agentRank,
            )
            .where { invocationsClause(orgId, since) }
            .orderBy(McpInferenceModel.createdAt, SortOrder.ASC)
            .alias("rankedProductAgent")

        return suspendedTransaction {
            rankedAgents
                .select(
                    rankedAgents[McpInferenceModel.createdAt].dateTruncate(precision, timeZone),
                    rankedAgents[productAgentCoalesced],
                    rankedAgents[McpInferenceModel.id].count(),
                )
                .whereAny(
                    rankedAgents[agentRank] eq longLiteral(1),
                    rankedAgents[agentRank].isNull(),
                )
                .groupBy(
                    rankedAgents[McpInferenceModel.createdAt].dateTruncate(precision, timeZone),
                    rankedAgents[productAgentCoalesced],
                )
                .map { row ->
                    AnalyticsMcpInvocationTuple(
                        date = row[rankedAgents[McpInferenceModel.createdAt].dateTruncate(precision, timeZone)],
                        productAgent = row.getOrNull(rankedAgents[productAgentCoalesced]) ?: ProductAgentType.Mcp,
                        count = row[rankedAgents[McpInferenceModel.id].count()],
                    )
                }
        }
    }

    private fun invocationsClause(orgId: OrgId, since: Instant?) = AllOp(
        McpInferenceModel.org eq orgId,
        McpInferenceModel.state eq McpInferenceState.Complete,
        since?.let { McpInferenceModel.createdAt greaterEq it },
    )
}
